# 危险货物运输管理系统 (Danger Freight)

## 项目简介

危险货物运输管理系统是一个基于uni-app框架开发的跨平台应用，主要用于管理危险货物的运输流程。系统包含货主端和司机端两个主要角色，实现了从货物发布、接单、运输到交付的全流程管理。通过该系统，可以有效提高危险货物运输的安全性、可追溯性和管理效率。

## 技术栈

- **前端框架**：uni-app (支持Vue 2和Vue 3模式)
- **UI渲染**：基于Vue.js的MVVM架构
- **构建工具**：Vite
- **CSS预处理器**：SCSS
- **包管理工具**：npm
- **跨端能力**：支持编译到iOS、Android、H5以及各种小程序平台

## 主要依赖

- **dayjs** (^1.11.13)：轻量级日期处理库，用于处理日期和时间
- **lodash** (^4.17.21)：提供实用的JavaScript工具函数
- **clipboard** (^2.0.11)：实现剪贴板复制功能
- **element-china-area-data** (^6.1.0)：中国省市区数据

## UI组件库

### uni-ui官方组件库
项目广泛使用了uni-app官方组件库uni-ui，包括但不限于：

- **布局组件**：uni-row、uni-grid、uni-section等
- **表单组件**：uni-forms、uni-easyinput、uni-data-checkbox、uni-datetime-picker等
- **导航组件**：uni-nav-bar、uni-pagination、uni-segmented-control等
- **数据展示**：uni-table、uni-list、uni-card、uni-collapse等
- **操作反馈**：uni-popup、uni-transition、uni-swipe-action等

### uview-plus增强UI库
项目集成了uview-plus增强版UI组件库，提供了更丰富的UI组件和交互体验。

### 自定义组件
项目开发了多个自定义组件，提升用户体验和开发效率：

- **cu-header/cu-header-free**：自定义头部组件
- **cu-tab-swiper**：选项卡滑动组件
- **cu-cell/cu-cell-two**：单元格组件
- **cu-occupation**：占位组件
- **cu-secure**：安全组件

### 第三方组件
- **z-tabs**：增强版选项卡组件
- **z-paging**：下拉刷新、上拉加载更多组件

## 项目架构

### 分包设计
项目采用分包加载策略，优化应用性能和首次启动速度：

- **主包**：包含核心页面和公共组件
- **货主分包** (transportSubPages)：货主相关功能页面
- **订单分包** (orderSubPages)：订单管理相关页面
- **个人中心分包** (mySubPages)：用户个人信息相关页面
- **司机分包** (pageComDriver)：司机端功能页面

### 核心页面

#### 主包页面
- **首页/启动页**：first、index
- **发货页**：transport (货主发布运输需求)
- **订单页**：orders (订单管理)
- **个人中心**：my (用户信息管理)
- **登录页**：login
- **入口页**：entrance

#### 货主端页面
- 地址编辑
- 货物信息管理
- 车型选择
- 订单详情查看

#### 司机端页面
- 货物路线查看
- 常用路线管理
- 配送管理
- 任务管理

## 功能特点

- **多角色支持**：同时支持货主和司机两种角色
- **全流程管理**：从货物发布、接单、运输到交付的全流程管理
- **实时状态更新**：订单状态实时更新
- **路线规划**：智能路线规划功能
- **历史记录**：历史订单记录查询
- **个人信息管理**：用户个人信息维护

## 项目目录结构

```
danger-freight/
├── api/                 # API接口文件
├── components/          # 自定义组件
├── hooks/               # 自定义hooks
├── interceptors/        # 请求拦截器
├── pages/               # 主包页面
├── pageCom/             # 或柱端页面
├── pageComDriver/       # 司机端页面
├── static/              # 静态资源
│   ├── images/          # 图片资源
│   └── iconfont/        # 图标字体
├── transportSubPages/   # 货主端运输分包
├── orderSubPages/       # 订单分包
├── mySubPages/          # 个人中心分包
├── uni_modules/         # uni-app插件和组件
├── utils/               # 工具函数
├── App.vue              # 应用入口组件
├── main.js              # 应用入口文件
├── pages.json           # 页面路由配置
├── manifest.json        # 应用配置文件
├── uni.scss             # 全局样式变量
└── vite.config.js       # Vite构建配置
```

## 安装与运行

### 环境要求
- Node.js 12.0 或更高版本
- HBuilderX 3.0 或更高版本（推荐使用IDE开发）

### 安装依赖
```bash
npm install
```

### 开发模式运行
```bash
# 使用HBuilderX运行到浏览器
在HBuilderX中打开项目，点击"运行">"运行到浏览器"

# 或使用命令行
npm run dev
```

### 构建发布
```bash
# 使用HBuilderX发布
在HBuilderX中点击"发行"，选择目标平台

# 或使用命令行构建H5版本
npm run build
```

## 特别说明

- 项目采用自定义导航栏样式，提供统一的UI体验
- 使用分包加载策略，优化应用性能和首次启动速度
- 支持多端编译，一次开发，多端发布 