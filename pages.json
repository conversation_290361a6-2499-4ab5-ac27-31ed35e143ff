{
  "easycom": {
    "autoscan": true,
    "custom": {
      "uni-(.*)": "@/uni_modules/uni-$1/components/uni-$1/uni-$1.vue",
      "cu-(.*)": "@/components/cu-$1/cu-$1.vue",
      "^u--(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue"
    }
  },
  "tabBar": {
    "color": "#707070",
    "selectedColor": "#ea9518",
    "list": [
      {
        "pagePath": "pages/transport/transport",
        "iconPath": "static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo.png",
        "selectedIconPath": "static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo_selected.png",
        "text": "发货"
      },
      {
        "pagePath": "pages/orders/orders",
        "iconPath": "static/images/tabbar/dingdanjihe.png",
        "selectedIconPath": "static/images/tabbar/dingdanjihe_selected.png",
        "text": "订单"
      },
      {
        "pagePath": "pages/my/my",
        "iconPath": "static/images/tabbar/wode.png",
        "selectedIconPath": "static/images/tabbar/wode_selected.png",
        "text": "我的"
      }
    ]
  },
  "pages": [
    //    pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
        "path": "pages/first/first",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/transport/transport",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/orders/orders",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/my/my",
      "needLogin": true,
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/entrance/entrance",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/auth",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/id-card",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/drive-card",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/driving-card",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/detailed-list",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/transport-card",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/proxy-card",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/business-card",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/auth/in-review",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/agreement",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/agreement-detail",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/demo/demo",
      "style": {
        "navigationBarTitleText": "demo",
        "tabBar": false
      }
    },
    {
      "path": "pages/websocket-test",
      "style": {
        "navigationBarTitleText": "websocket"
      }
    }
  ],
  "subPackages": [
    // 货主端页面
    {
      "root": "pageCom",
      "pages": [
        {
          "path": "transport/edit_address",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "transport/cargo_info",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "transport/vehicle_model",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "transport/pending_order_detail",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "transport/history_detail",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/completed",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/cancel_detail",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/personal",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/editDetail",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/editPhone",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/setMessage",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
				{
					"path": "depositApply/depositApply",
					"needLogin": true,
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "depositApply/depositDestination",
					"needLogin": true,
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
          "path": "orders/orderInfo",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    },
    // 司机端运输页面
    {
      "root": "pageComDriver",
      "pages": [
        {
          "path": "home/goods_route",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "distribution/common_routes",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "distribution/router_detail",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "distribution/distribution",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "today/today",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "common/message",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "common/find_record",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "common/local_goods",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/my",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/carTeam",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/setPermission",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "my/setCheckDays",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "make_up/make_up",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pay/pay",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/orders",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/order_detail",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/order_info",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/cost_info",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orders/order_pic",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        }
			]
		},
    //配货站
    {
      "root": "pageComStation",
      "pages": [
        {
          "path": "orders/orders",
          "needLogin": true,
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "preloadRule": {},
  // "pages/index/index": { //要进行预加载时用户要进入的页面路径
  // 	"network": "all", // 什么网络下支持允许预加载，默认wifi: wifi/all
  // 	"packages": ["pages_Index"] // 要进行预加载的子包名
  // },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {},
  "condition": {
    //模式配置，仅开发期间生效
    "current": 3, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
