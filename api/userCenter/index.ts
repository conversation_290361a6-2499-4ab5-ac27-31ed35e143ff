import request from '../../utils/request';

// 查询用户修改手机号次数
export function getUserChangePhoneNum() {
	return request({
		url: `/changePhone/getUserChangePhoneNum`,
		method: 'GET',
		loading: true
	});
}

// 查询用户修改手机号次数
export function getUserPhone() {
	return request({
		url: `/changePhone/getUserPhone`,
		method: 'GET',
		loading: true
	});
}

//车队司机列表
export function getDriverList() {
	return request({
		url: `/carTeamInvite/driverList`,
		method: 'GET',
		loading: true
	});
}

//车队司机列表
export function selectDriver(data: any) {
	return request({
		url: `/carTeamInvite/selectDriver`,
		method: 'GET',
		data,
		loading: true
	});
}

//车队司机列表
export function inviteDriver(data: any) {
	return request({
		url: `/carTeamInvite/inviteDriver`,
		method: 'GET',
		data,
		loading: true
	});
}

//移除成员
export function removeInvite(data: any) {
	return request({
		url: `/carTeamInvite/removeInvite`,
		method: 'GET',
		data,
		loading: true
	});
}

//查询车队名称
export function getTeamName() {
	return request({
		url: `/carTeamInvite/getTeamName`,
		method: 'GET',
		loading: true
	});
}

//修改车队名称
export function updateTeamName(data: any) {
	return request({
		url: `/carTeamInvite/updateTeamName`,
		method: 'GET',
		data,
		loading: true
	});
}

//我加入的车队
export function myJoinTeam() {
	return request({
		url: `/carTeamInvite/myJoinTeam`,
		method: 'GET',
		loading: true
	});
}

//拒绝或加入车队
export function joinOrRefuseTeam(data: any) {
	return request({
		url: `/carTeamInvite/joinOrRefuseTeam`,
		method: 'GET',
		data,
		loading: true
	});
}


//车队权限管理
export function updateTeamDataScope(data: any) {
	return request({
		url: `/carTeamInvite/updateTeamDataScope`,
		method: 'GET',
		data,
		loading: true
	});
}
