import request from '../../utils/request'

// 登录
export function login(data: any) {
  return request({
    url: `/userLogin/login`,
    method: 'POST',
    data,
    loading: true,
    noToken: true,
  })
}
export function mobileLogin(data: any) {
  return request({
    url: `/userLogin/mobileLogin`,
    method: 'GET',
    data,
    loading: true,
    noToken: true,
  })
}

// 退出登录
export function clientLogout() {
  return request({
    url: `/userLogin/logout`,
    method: 'POST',
    loading: true,
  })
}

// 发送验证码短信
export function sendVerifyCodeSms(data: any) {
  return request({
    url: `/userLogin/sendVerifyCodeSms`,
    method: 'GET',
    data,
    loading: true,
    noToken: true,
  })
}

// 验证验证码短信（通用）
export function verifyCodeSms(data: any) {
  return request({
    url: `/userLogin/verifyCodeSms`,
    method: 'GET',
    data,
    loading: true,
    noToken: true,
  })
}

// 通用上传
export function upload(data: any) {
  return request({
    url: `/upload/uploadFile`,
    method: 'POST',
    data,
    loading: true,
    isFormData: true,
  })
}

// 校验新手机验证码并修改用户手机号
export function changePhone(data: any) {
  return request({
    url: `/changePhone/changePhone`,
    method: 'GET',
    data,
    loading: true,
  })
}

// SSE关闭订阅
export function closeSubscribe(data: any) {
  return request({
    url: `/message/closeSubscribe`,
    method: 'POST',
    data,
  })
}

// SSE消息订阅
export function onSubscribe(data: any) {
  return request({
    url: `/message/subscribe`,
    method: 'GET',
    data,
  })
}
