<template>
  <div></div>
</template>
<script>
import { ws } from './utils/websocket'

export default {
  data() {
    return {
      wsInitialized: false // WebSocket初始化标记
    }
  },

  // WebSocket连接管理说明：
  // 1. App启动时会尝试建立WebSocket连接，但会自动检查用户登录状态
  // 2. 如果用户未登录，连接会被跳过，等待用户登录后手动连接
  // 3. 用户登录成功后，需要调用 ws.onUserLogin() 来建立连接
  // 4. 用户退出登录时，需要调用 ws.onUserLogout() 来断开连接
  // 5. App切换到前台时会检查登录状态和连接状态，自动重连（如果需要）
  onLaunch: function () {
    console.log('App Launch');

    // 延迟初始化WebSocket连接，给系统足够时间完成初始化
    console.log('App启动，延迟初始化WebSocket连接');
    setTimeout(() => {
      console.log('🚀 App初始化完成，尝试建立WebSocket连接');
      this.wsInitialized = true;

      // 尝试连接WebSocket（会自动检查用户登录状态）
      // 如果用户未登录，连接会被跳过，等待用户登录后手动连接
      ws.connect();

      console.log('💡 提示：如果用户未登录，WebSocket连接将在用户登录后自动建立');
    }, 1000); // 延迟1秒
  },
  onShow: function () {
    console.log('App Show');

    // 检查WebSocket连接状态和用户登录状态
    if (this.wsInitialized) {
      const isLoggedIn = ws.checkLoginStatus();
      const isConnected = ws.isConnected();

      console.log('📊 App显示时状态检查:', {
        wsInitialized: this.wsInitialized,
        isLoggedIn: isLoggedIn,
        isConnected: isConnected
      });

      // 只有在已登录但未连接时才重新连接
      if (isLoggedIn && !isConnected) {
        console.log('🔄 用户已登录但WebSocket未连接，重新建立连接');
        ws.connect();
      } else if (!isLoggedIn) {
        console.log('👤 用户未登录，跳过WebSocket连接');
      } else if (isConnected) {
        console.log('✅ WebSocket已连接，无需重复连接');
      }
    } else {
      console.log('⏳ WebSocket尚未初始化，等待onLaunch完成');
    }
  },
  onHide: function () {
    console.log('App Hide');
    // WebSocket管理器会自动处理应用隐藏时的连接管理
  }
};
</script>

<style lang="scss">
@import './static/iconfont/iconfont.css';

/*每个页面公共css */
page {
  background-color: $uni-bg-page;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-container {
  padding: 0rpx $uni-padding-left-right-3;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 10rpx;
  margin-top: 20rpx;
}

// 禁止滚动
.prohibit-roll {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  overflow: hidden;
}

.global-popup {
  .uni-popup__wrapper {
    .popup-content {
      .title-box {
        width: 100%;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid $uni-text-color-grey;
        padding: 0rpx 30rpx;
        box-sizing: border-box;

        .btn {
          width: 80rpx;
          text-align: center;
          font-size: $uni-font-size-base-30;
          color: $uni-text-color-placeholder;
        }

        .confirm {
          color: #ff8c00;
        }

        .title {
          font-size: $uni-font-size-lg;
          color: #000;
        }
      }
    }
  }
}
</style>
