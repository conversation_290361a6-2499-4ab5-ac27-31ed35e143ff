import { gConfig } from '@/config'
import { isString } from '@/utils/common'
import { getStoragetItem, removeStoragetItem } from '@/utils/storage'

const request = (config: any) => {
  config = beforeRequest(config)
  if (!config) return
  // 如果是 FormData 上传，使用 uni.uploadFile API
  if (config.isFormData) {
    return new Promise((resolve, reject) => {
      // 从 FormData 中获取文件对象
      const formData = config.data

      try {
        // 尝试多种方式获取文件路径
        let filePath = ''
        let fileKey = 'file'

        // 检查formData是否直接包含必要信息
        if (formData.tempFilePaths && formData.tempFilePaths.length > 0) {
          // 1. 直接从chooseImage结果中获取
          filePath = formData.tempFilePaths[0]
        } else if (
          formData.tempFiles &&
          formData.tempFiles[0] &&
          formData.tempFiles[0].path
        ) {
          // 2. 从tempFiles获取
          filePath = formData.tempFiles[0].path
        } else {
          // 3. 尝试从FormData字段中获取
          try {
            fileKey = Object.keys(formData._map || formData)[0] || 'file'
            const fileObj = formData.get
              ? formData.get(fileKey)
              : formData[fileKey]

            if (fileObj) {
              if (typeof fileObj === 'string') {
                filePath = fileObj
              } else if (fileObj.path) {
                filePath = fileObj.path
              } else if (fileObj.tempFilePath) {
                filePath = fileObj.tempFilePath
              } else if (
                fileObj.tempFiles &&
                fileObj.tempFiles[0] &&
                fileObj.tempFiles[0].path
              ) {
                filePath = fileObj.tempFiles[0].path
              }
            }
          } catch (e) {
            console.warn('从FormData提取文件失败:', e)
          }
        }

        // 最终检查是否找到了文件路径
        if (!filePath) {
          reject(new Error('无法找到有效的文件路径'))
          return
        }

        // 确保 token 存在，避免后续可能的认证问题
        const token = getStoragetItem('x-token')

        const uploadTask = uni.uploadFile({
          url: config.url,
          filePath: filePath,
          name: fileKey,
          header: {
            ...config.header,
            // 确保显式设置token，避免可能的检查问题
            token: token || '',
          },
          formData: {}, // 其他表单数据
          success: (uploadRes) => {
            let data
            try {
              // 尝试解析 JSON 响应
              data = JSON.parse(uploadRes.data)
            } catch (e) {
              data = uploadRes.data
            }

            // 处理成功响应
            if (uploadRes.statusCode >= 200 && uploadRes.statusCode < 300) {
              resolve(data)
            } else {
              // 处理错误响应，但不执行任何页面导航
              if (uploadRes.statusCode == 500) {
                uni.showToast({
                  title: '服务器错误，请稍后重试',
                  icon: 'none',
                  duration: 2000,
                })
              }
              // 不要在这里导航到登录页面，避免多余的请求
              // 只返回错误信息
              reject({
                statusCode: uploadRes.statusCode,
                errMsg: uploadRes.errMsg || '上传失败',
              })
            }
          },
          fail: (err) => {
            uni.showToast({
              title: err.errMsg || '上传失败',
              icon: 'none',
            })
            reject(err)
          },
          complete: () => {
            ajaxHideLoading()
          },
        })

        // 监听上传进度
        uploadTask.onProgressUpdate((res) => {
          console.log('上传进度:', res.progress)
        })
      } catch (error) {
        reject(error)
        ajaxHideLoading()
      }
    })
  }

  // 普通请求使用原有的 uni.request API
  return new Promise((resolve, reject) => {
    uni.request({
      url: config.url,
      method: config.method,
      timeOut: config.timeout,
      data: config.data,
      header: config.header,
      responseType: config.responseType,
      success: (response: any) => {
        console.log(123,response)
        if (response.statusCode == 500) {
          uni.showToast({
            title: response.errMsg.toString(),
            icon: 'none',
            duration: 2000,
          })
          reject(response)
        } else if (response.data.code == 4001) {
          uni.navigateTo({
            url: '/pages/login/login',
          })
          reject(response)
        } else if (response.data.code == 7 && response.data.data.reload) {
          uni.showToast({
            title: response.data.msg.toString(),
            icon: 'none',
            duration: 2000,
          })
          removeStoragetItem('x-token')
          uni.navigateTo({
            url: '/pages/login/login',
          })
          reject(response)
        } else if (response.data.code == 7) {
          uni.showToast({
            title: response.data.msg.toString(),
            icon: 'none',
            duration: 2000,
          })
          reject(response)
        } else if (response.data.code == 4) {
          uni.showToast({
            title: response.data.msg.toString(),
            icon: 'none',
            duration: 2000,
          })
          reject(response)
        }
        resolve(response.data)
      },
      fail: (err: any) => {
        // 请求失败处理
        uni.showToast({
          title: err.errMsg,
          icon: 'none',
        })
        console.log('请求异常', err)
        reject(err)
      },
      complete: () => {
        ajaxHideLoading()
      },
    })
  })
}

// 请求拦截器
let ajaxTimes = 0
const ajaxHideLoading = () => {
  ajaxTimes--
  if (ajaxTimes === 0) uni.hideLoading()
}
const beforeRequest = (config: any) => {
  // 请求之前拦截操作
  if (config.loading) {
    ajaxTimes++
    uni.showLoading({
      title: isString(config.loading) ? config.loading : '拼命请求中',
      mask: true,
    })
  }
  // url  这里用来配置多个服务接口
  if (gConfig.env === 'dev') {
    // #ifdef APP-PLUS
    config.url = gConfig.baseUrl + config.url
    // #endif
    // #ifdef H5
    config.url = '/transport' + config.url
    // #endif
  } else if (gConfig.env === 'test') {
    // #ifdef APP-PLUS
    config.url = gConfig.testUrl + config.url
    // #endif
    // #ifdef H5
    config.url = '/transport' + config.url
    // #endif
  } else if (gConfig.env === 'pro') {
    // #ifdef APP-PLUS
    config.url = gConfig.proUrl + config.url
    // #endif
    // #ifdef H5
    config.url = '/transport' + config.url
    // #endif
  }

  config.timeout = config.timeout || 10000
  config.method = config.method || 'GET'
  config.data = config.data || {}
  config.responseType = config.requestType || undefined
  config.header = {}
  // 请求头中添加token
  if (getStoragetItem('x-token')) {
    // Authorization    Bearer   根据情况修改
    config.header['token'] = getStoragetItem('x-token')
  } else if (!config.noToken) {
    // 如果接口没有配置 noToken: true，则需要进行登录跳转
    uni.showToast({
      title: '登录信息失效',
      icon: 'none',
      duration: 2000,
    })
    // 未登陆则跳转登陆 重定向
    uni.navigateTo({
      url: '/pages/login/login',
    })
    ajaxHideLoading()
    return null
  }
  // 如果设置了 noToken: true，则跳过登录检查，继续请求
  return config
}

export default request
