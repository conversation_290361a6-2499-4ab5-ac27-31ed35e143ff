/**
 * WebSocket使用示例
 * 
 * 这个文件展示了如何在Vue组件中使用WebSocket连接工具
 */

import { ws, WSStatus, WSMessage } from './websocket'
import { ref, onMounted, onUnmounted } from 'vue'

// 在Vue组件中使用WebSocket的示例
export function useWebSocket() {
  const connectionStatus = ref<WSStatus>(WSStatus.DISCONNECTED)
  const messages = ref<WSMessage[]>([])
  const isLoading = ref(false)

  /**
   * 监听新订单消息
   */
  const listenNewOrders = () => {
    ws.on('new_order', (message: WSMessage) => {
      console.log('收到新订单:', message.data)
      
      // 添加到消息列表
      messages.value.unshift(message)
      
      // 显示新订单提示
      uni.showToast({
        title: '收到新订单',
        icon: 'success',
        duration: 2000
      })
      
      // 播放提示音
      // #ifdef APP-PLUS
      uni.playBackgroundAudio({
        src: '/static/sounds/new_order.mp3'
      })
      // #endif
    })
  }

  /**
   * 监听订单状态更新
   */
  const listenOrderUpdates = () => {
    ws.on('order_update', (message: WSMessage) => {
      console.log('订单状态更新:', message.data)
      
      // 添加到消息列表
      messages.value.unshift(message)
      
      // 显示更新提示
      if (message.data.message) {
        uni.showToast({
          title: message.data.message,
          icon: 'none',
          duration: 2000
        })
      }
    })
  }

  /**
   * 监听系统通知
   */
  const listenNotifications = () => {
    ws.on('notification', (message: WSMessage) => {
      console.log('收到系统通知:', message.data)
      
      // 添加到消息列表
      messages.value.unshift(message)
      
      // 显示通知
      uni.showModal({
        title: message.data.title || '系统通知',
        content: message.data.content || message.data.message,
        showCancel: false,
        confirmText: '知道了'
      })
    })
  }

  /**
   * 监听连接状态变化
   */
  const listenStatusChange = () => {
    ws.on('status_change', (message: WSMessage) => {
      const status = message.data.status
      connectionStatus.value = status
      console.log('WebSocket连接状态变化:', status)
      
      switch (status) {
        case WSStatus.CONNECTED:
          console.log('WebSocket已连接')
          uni.showToast({
            title: '连接成功',
            icon: 'success',
            duration: 1500
          })
          break
        case WSStatus.DISCONNECTED:
          console.log('WebSocket已断开')
          break
        case WSStatus.ERROR:
          console.log('WebSocket连接错误')
          uni.showToast({
            title: '连接异常',
            icon: 'none',
            duration: 2000
          })
          break
        case WSStatus.CONNECTING:
          console.log('WebSocket连接中...')
          break
      }
    })
  }

  /**
   * 监听所有消息（调试用）
   */
  const listenAllMessages = () => {
    ws.on('*', (message: WSMessage) => {
      console.log('收到WebSocket消息:', message)
      
      // 过滤心跳消息
      if (message.type !== 'heartbeat' && message.type !== 'ping' && message.type !== 'pong') {
        messages.value.unshift(message)
        
        // 限制消息数量
        if (messages.value.length > 100) {
          messages.value = messages.value.slice(0, 100)
        }
      }
    })
  }

  /**
   * 发送消息到服务器
   */
  const sendMessage = (type: string, data: any) => {
    if (!ws.isConnected()) {
      uni.showToast({
        title: '连接未建立',
        icon: 'none',
        duration: 2000
      })
      return
    }

    ws.send({
      type: type,
      data: data,
      timestamp: Date.now()
    })
  }

  /**
   * 发送心跳消息
   */
  const sendHeartbeat = () => {
    sendMessage('ping', { timestamp: Date.now() })
  }

  /**
   * 初始化所有监听器
   */
  const initListeners = () => {
    listenNewOrders()
    listenOrderUpdates()
    listenNotifications()
    listenStatusChange()
    
    // 开发环境下监听所有消息
    // #ifdef APP-PLUS-NVUE || H5
    if (process.env.NODE_ENV === 'development') {
      listenAllMessages()
    }
    // #endif
    
    // 更新当前连接状态
    connectionStatus.value = ws.getStatus()
  }

  /**
   * 清理监听器
   */
  const cleanup = () => {
    ws.off('new_order')
    ws.off('order_update')
    ws.off('notification')
    ws.off('status_change')
    ws.off('*')
  }

  /**
   * 清空消息列表
   */
  const clearMessages = () => {
    messages.value = []
  }

  /**
   * 获取连接状态文本
   */
  const getStatusText = () => {
    switch (connectionStatus.value) {
      case WSStatus.DISCONNECTED:
        return '未连接'
      case WSStatus.CONNECTING:
        return '连接中'
      case WSStatus.CONNECTED:
        return '已连接'
      case WSStatus.ERROR:
        return '连接错误'
      default:
        return '未知状态'
    }
  }

  /**
   * 手动重连
   */
  const reconnect = async () => {
    isLoading.value = true
    try {
      await ws.disconnect()
      await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
      await ws.connect()
    } catch (error) {
      console.error('重连失败:', error)
      uni.showToast({
        title: '重连失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    connectionStatus,
    messages,
    isLoading,
    
    // 方法
    initListeners,
    cleanup,
    sendMessage,
    sendHeartbeat,
    clearMessages,
    getStatusText,
    reconnect,
    
    // 计算属性
    isConnected: () => ws.isConnected(),
    messageCount: () => messages.value.length
  }
}

// Vue组件使用示例
export const WebSocketComponentExample = {
  setup() {
    const {
      connectionStatus,
      messages,
      isLoading,
      initListeners,
      cleanup,
      sendMessage,
      getStatusText,
      reconnect,
      isConnected,
      messageCount
    } = useWebSocket()

    // 组件挂载时初始化监听器
    onMounted(() => {
      initListeners()
    })

    // 组件卸载时清理监听器
    onUnmounted(() => {
      cleanup()
    })

    // 发送测试消息
    const sendTestMessage = () => {
      sendMessage('test', {
        content: '这是一条测试消息',
        timestamp: Date.now()
      })
    }

    return {
      // 状态
      connectionStatus,
      messages,
      isLoading,
      
      // 方法
      sendTestMessage,
      getStatusText,
      reconnect,
      
      // 计算属性
      isConnected,
      messageCount
    }
  }
}

// 简单的页面级使用示例
export const simplePageExample = () => {
  // 在页面的onLoad中调用
  const onPageLoad = () => {
    // 监听新订单
    ws.on('new_order', (message: WSMessage) => {
      console.log('页面收到新订单:', message.data)
      
      // 刷新页面数据
      // refreshPageData()
    })

    // 监听订单状态更新
    ws.on('order_update', (message: WSMessage) => {
      console.log('页面收到订单更新:', message.data)
      
      // 更新页面中的订单状态
      // updateOrderStatus(message.data)
    })
  }

  // 在页面的onUnload中调用
  const onPageUnload = () => {
    // 移除监听器
    ws.off('new_order')
    ws.off('order_update')
  }

  // 发送消息示例
  const sendOrderAction = (orderId: string, action: string) => {
    ws.send({
      type: 'order_action',
      data: {
        orderId: orderId,
        action: action,
        timestamp: Date.now()
      }
    })
  }

  return {
    onPageLoad,
    onPageUnload,
    sendOrderAction
  }
}
