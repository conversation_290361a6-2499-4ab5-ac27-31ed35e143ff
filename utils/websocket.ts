import { gConfig } from '@/config'
import { getStoragetItem } from '@/utils/storage'
import { generateRandomId } from '@/utils/common'

// WebSocket连接状态
export enum WSStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

// WebSocket消息事件
export interface WSMessage {
  type: string
  data: any
  timestamp: number
}

// WebSocket管理器类
class WebSocketManager {
  private socket: any = null
  private status: WSStatus = WSStatus.DISCONNECTED
  private wsUrl: string = ''
  private heartbeatSendTimer: any = null // 心跳发送定时器
  private heartbeatCheckTimer: any = null // 心跳超时检测定时器
  private reconnectTimer: any = null
  private locationTimer: any = null
  private connectionCheckTimer: any = null // 连接状态检查定时器
  private messageHandlers: Map<string, Function[]> = new Map()
  private isAppActive: boolean = true
  private reconnectAttempts: number = 0
  private listenersSetup: boolean = false // 标记监听器是否已设置
  private lastConnectAttempt: number = 0 // 上次连接尝试时间

  constructor() {
    this.setupWebSocketUrl()
    this.setupAppLifecycle()
    this.setupNetworkListener()
    // 全局监听器将在第一次连接时设置，避免过早设置导致的错误
  }

  /**
   * 设置WebSocket URL
   */
  private setupWebSocketUrl(): void {
    if (gConfig.env === 'dev') {
      // #ifdef APP-PLUS
      this.wsUrl = gConfig.testUrl.replace('http', 'ws') + '/business/websocket'
      // #endif
      // #ifdef H5
      // H5测试环境，根据当前页面协议和域名构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      this.wsUrl =
        protocol +
        '//' +
        window.location.host +
        '/websocketApi/transport/business/websocket'
      // #endif
    } else if (gConfig.env === 'test') {
      // #ifdef APP-PLUS
      this.wsUrl = gConfig.testUrl.replace('http', 'ws') + '/business/websocket'
      // #endif
      // #ifdef H5
      // H5测试环境，根据当前页面协议和域名构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      this.wsUrl =
        protocol +
        '//' +
        window.location.host +
        '/websocketApi/transport/business/websocket'
      // #endif
    } else if (gConfig.env === 'pro') {
      // #ifdef APP-PLUS
      this.wsUrl = gConfig.testUrl.replace('http', 'ws') + '/business/websocket'
      // #endif
      // #ifdef H5
      // H5测试环境，根据当前页面协议和域名构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      this.wsUrl =
        protocol +
        '//' +
        window.location.host +
        '/websocketApi/transport/business/websocket'
      // #endif
    }
  }

  /**
   * 监听应用生命周期
   */
  private setupAppLifecycle(): void {
    // #ifdef APP-PLUS
    // App环境监听应用前后台切换
    uni.onAppShow(() => {
      console.log('应用进入前台，重新连接WebSocket')
      this.isAppActive = true
      if (this.status === WSStatus.DISCONNECTED) {
        this.connect()
      }
    })

    uni.onAppHide(() => {
      console.log('应用进入后台，断开WebSocket连接')
      this.isAppActive = false
      this.disconnect()
    })
    // #endif

    // #ifdef H5
    // H5环境监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('页面隐藏，断开WebSocket连接')
        this.isAppActive = false
        this.disconnect()
      } else {
        console.log('页面显示，重新连接WebSocket')
        this.isAppActive = true
        this.connect()
      }
    })
    // #endif
  }

  /**
   * 监听网络状态
   */
  private setupNetworkListener(): void {
    uni.onNetworkStatusChange((res) => {
      if (
        res.isConnected &&
        this.isAppActive &&
        this.status === WSStatus.ERROR
      ) {
        console.log('网络恢复，重新连接WebSocket')
        this.connect()
      } else if (!res.isConnected) {
        console.log('网络断开，WebSocket连接中断')
        this.setStatus(WSStatus.ERROR)
      }
    })
  }

  /**
   * 检查网络状态
   */
  private async checkNetworkStatus(): Promise<{
    isConnected: boolean
    networkType: string
  }> {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          console.log(
            `📶 网络状态检查: ${res.networkType}, 连接状态: ${isConnected}`
          )
          resolve({
            isConnected,
            networkType: res.networkType,
          })
        },
        fail: () => {
          console.log('📶 网络状态检查失败，假设网络可用')
          resolve({
            isConnected: true,
            networkType: 'unknown',
          })
        },
      })
    })
  }

  /**
   * 检查用户登录状态
   */
  private checkUserLoginStatus(): {isLoggedIn: boolean, userInfo: any, token: string, userType: string} {
    try {
      const token = getStoragetItem('x-token')
      const userInfo = getStoragetItem('userInfo')
      const userType = getStoragetItem('userType')

      // 检查token是否存在且不为空
      const hasValidToken = token && token.trim() !== ''

      // 检查userInfo是否存在且包含必要字段
      const hasValidUserInfo = userInfo && userInfo.id

      // 检查userType是否存在且不为空
      const hasValidUserType = userType && userType.trim() !== ''

      // 只有token、userInfo和userType都有效时才认为已登录
      const isLoggedIn = hasValidToken && hasValidUserInfo && hasValidUserType

      console.log('👤 用户登录状态检查:', {
        hasToken: !!hasValidToken,
        hasUserInfo: !!hasValidUserInfo,
        hasUserType: !!hasValidUserType,
        isLoggedIn: isLoggedIn,
        userId: userInfo?.id || 'N/A',
        userType: userType || 'N/A'
      })

      return {
        isLoggedIn,
        userInfo: userInfo || null,
        token: token || '',
        userType: userType || ''
      }
    } catch (error) {
      console.error('👤 检查用户登录状态失败:', error)
      return {
        isLoggedIn: false,
        userInfo: null,
        token: '',
        userType: ''
      }
    }
  }

  /**
   * 设置全局WebSocket事件监听器（只设置一次）
   */
  private setupGlobalWebSocketListeners(): void {
    if (this.listenersSetup) {
      console.log('⚠️ 全局监听器已设置，跳过重复设置')
      return
    }

    console.log('🔧 设置全局WebSocket事件监听器')

    // 连接打开
    uni.onSocketOpen((res) => {
      console.log('🎉 全局监听器：WebSocket连接已建立！')
      console.log('🎉 当前重连次数:', this.reconnectAttempts)
      console.log('🎉 连接详情:', res)

      // 立即发送ping消息确认连接可用性
      try {
        uni.sendSocketMessage({
          data: JSON.stringify({
            type: 'ping',
            timestamp: Date.now(),
          }),
          success: () => {
            console.log('✅ 连接确认：ping消息发送成功，连接已建立')
            this.setStatus(WSStatus.CONNECTED)
            this.reconnectAttempts = 0

            // 先停止旧的定时器，再启动新的，确保不会有重复定时器
            console.log('🔄 清理旧定时器并启动新的定时器')
            this.stopHeartbeat()
            this.stopLocationReporting()

            this.startHeartbeat()

            // 连接建立后发送订阅消息
            this.sendSubscribeMessage()

            // 设置全局消息处理器
            this.setupGlobalHandlers()

            // 开始定时发送位置信息
            this.startLocationReporting()
          },
          fail: (error: any) => {
            console.error('❌ 连接确认：ping消息发送失败，连接可能无效:', error)
            this.setStatus(WSStatus.ERROR)
            this.scheduleReconnect()
          },
        })
      } catch (error) {
        console.error('❌ 连接确认：发送ping消息异常:', error)
        this.setStatus(WSStatus.ERROR)
        this.scheduleReconnect()
      }
    })

    // 接收消息
    uni.onSocketMessage((res) => {
      console.log('📨 全局监听器：收到WebSocket消息')
      this.handleMessage(res.data)
    })

    // 连接错误
    uni.onSocketError((res) => {
      console.error('❌ 全局监听器：WebSocket连接错误')
      console.error('❌ 错误详情:', res)
      console.error('❌ 当前状态:', this.status)

      // 只有在实际尝试连接时才处理错误，避免初始化时的无效错误
      if (
        this.status === WSStatus.CONNECTING ||
        this.status === WSStatus.CONNECTED
      ) {
        console.error('❌ 连接过程中发生错误，触发重连')
        this.setStatus(WSStatus.ERROR)
        this.scheduleReconnect()
      } else {
        console.log('⚠️ 非连接状态下的错误，忽略处理')
      }
    })

    // 连接关闭
    uni.onSocketClose((res) => {
      console.log('🔌 全局监听器：WebSocket连接已关闭')
      console.log('🔌 关闭详情:', res)
      console.log('🔌 当前状态:', this.status)
      if (this.status !== WSStatus.DISCONNECTED) {
        console.log('🔄 非主动断开，准备重连')
        this.setStatus(WSStatus.ERROR)
        this.scheduleReconnect()
      }
    })

    this.listenersSetup = true
    console.log('✅ 全局WebSocket事件监听器设置完成')
  }

  /**
   * 开始连接WebSocket
   */
  async connect(): Promise<void> {
    // 防止短时间内重复连接调用（5秒内的重复调用直接忽略）
    const now = Date.now()
    if (now - this.lastConnectAttempt < 5000) {
      console.log(
        `⚠️ 距离上次连接尝试仅${
          (now - this.lastConnectAttempt) / 1000
        }秒，忽略重复调用`
      )
      return
    }
    this.lastConnectAttempt = now

    // 确保全局监听器已设置（只设置一次）
    if (!this.listenersSetup) {
      console.log('🔧 首次连接，设置全局WebSocket监听器')
      this.setupGlobalWebSocketListeners()
    }

    // 检查网络状态
    const networkStatus = await this.checkNetworkStatus()
    if (!networkStatus.isConnected) {
      console.log('❌ 网络不可用，跳过WebSocket连接')
      this.setStatus(WSStatus.ERROR)
      return
    }

    // 检查用户登录状态
    const loginStatus = this.checkUserLoginStatus()
    if (!loginStatus.isLoggedIn) {
      console.log('❌ 用户未登录，跳过WebSocket连接')
      console.log('💡 提示：用户登录后WebSocket将自动连接')
      this.setStatus(WSStatus.DISCONNECTED)
      return
    }

    console.log('✅ 用户已登录，继续建立WebSocket连接')

    // 强制关闭现有连接，确保重连时没有残留连接
    if (this.socket) {
      console.log('🔄 强制关闭现有WebSocket连接')
      uni.closeSocket()
      this.socket = null
    }

    // 如果状态是CONNECTING，重置为DISCONNECTED以允许重连
    if (this.status === WSStatus.CONNECTING) {
      console.log('🔄 检测到CONNECTING状态，重置为DISCONNECTED以允许重连')
      this.setStatus(WSStatus.DISCONNECTED)
    }

    // 如果已经连接，直接返回
    if (this.status === WSStatus.CONNECTED) {
      console.log('✅ WebSocket已连接，跳过重复连接')
      return
    }

    try {
      this.setStatus(WSStatus.CONNECTING)
      console.log('🚀 开始建立WebSocket连接...')

      // 直接建立WebSocket连接
      await this.establishWebSocketConnection()
    } catch (error) {
      console.error('❌ WebSocket连接失败:', error)
      this.setStatus(WSStatus.ERROR)
      this.scheduleReconnect()
    }
  }

  /**
   * 建立WebSocket连接
   */
  private async establishWebSocketConnection(): Promise<void> {
    const wsUrl = this.buildWebSocketUrl()

    console.log('🔗 连接WebSocket:', wsUrl)

    // 设置连接超时机制（10秒）
    const connectionTimeout = setTimeout(() => {
      if (this.status === WSStatus.CONNECTING) {
        console.log('⏰ WebSocket连接超时，强制触发重连')
        this.setStatus(WSStatus.ERROR)
        this.scheduleReconnect()
      }
    }, 10000)

    this.socket = uni.connectSocket({
      url: wsUrl,
      // header: {
      //   token: getStoragetItem('x-token') || '',
      //   userId: getStoragetItem('userId') || 'mock_user_123',
      // },
      success: () => {
        console.log('✅ WebSocket连接请求发送成功，等待全局监听器响应')
      },
      fail: (error: any) => {
        console.error('❌ WebSocket连接请求失败:', error)
        clearTimeout(connectionTimeout)
        this.setStatus(WSStatus.ERROR)
        this.scheduleReconnect()
      },
    })

    console.log('📱 WebSocket连接对象创建完成，socket:', this.socket)

    // 连接成功后清除超时定时器
    this.on('status_change', (message: WSMessage) => {
      if (message.data.status === WSStatus.CONNECTED) {
        clearTimeout(connectionTimeout)
      }
    })

    // 添加备用检查机制，防止监听器失效
    this.startConnectionCheck()
  }

  /**
   * 开始连接状态检查
   * 备用机制：如果监听器失效，通过轮询检查连接状态
   */
  private startConnectionCheck(): void {
    // 先停止现有的连接检查，避免重复检查
    this.stopConnectionCheck()

    console.log('🔍 开始连接状态检查')

    let checkCount = 0
    const maxChecks = 10 // 最多检查10次

    const checkConnection = () => {
      checkCount++
      console.log(
        `🔍 连接状态检查 ${checkCount}/${maxChecks}，当前状态:`,
        this.status
      )

      if (this.status === WSStatus.CONNECTED) {
        console.log('✅ 连接状态检查：已连接，停止检查')
        this.connectionCheckTimer = null // 清理定时器引用
        return
      }

      if (checkCount >= maxChecks) {
        console.log('⚠️ 连接状态检查：达到最大检查次数，可能连接失败')
        if (this.status === WSStatus.CONNECTING) {
          console.log('🔄 强制触发重连')
          this.setStatus(WSStatus.ERROR)
          this.scheduleReconnect()
        }
        this.connectionCheckTimer = null // 清理定时器引用
        return
      }

      // 继续检查，保存定时器ID
      this.connectionCheckTimer = setTimeout(checkConnection, 2000)
    }

    // 5秒后开始检查，保存定时器ID
    this.connectionCheckTimer = setTimeout(checkConnection, 5000)
  }

  /**
   * 停止连接状态检查
   */
  private stopConnectionCheck(): void {
    if (this.connectionCheckTimer) {
      clearTimeout(this.connectionCheckTimer)
      this.connectionCheckTimer = null
      console.log('🔍 连接状态检查已停止')
    }
  }

  /**
   * 构建WebSocket URL
   */
  private buildWebSocketUrl(): string {
    try {
      const userInfo = getStoragetItem('userInfo')
      const userId = userInfo?.id || 'mock_user_123'
      const randomId = generateRandomId(10)
      const finalUrl = `${this.wsUrl}/${randomId}/${userId}`

      console.log('构建WebSocket URL:', {
        baseUrl: this.wsUrl,
        userId: userId,
        randomId: randomId,
        finalUrl: finalUrl,
      })

      return finalUrl
    } catch (error) {
      console.error('构建WebSocket URL失败:', error)
      // 降级方案
      const randomId = generateRandomId(10)
      const fallbackUrl = `${this.wsUrl}/${randomId}/mock_user_123`
      console.log('使用降级URL:', fallbackUrl)
      return fallbackUrl
    }
  }

  /**
   * 发送订阅消息
   */
  private sendSubscribeMessage(): void {
    const subscribeData = {
      type: 'subscribe',
      // data: {
      //   userId: getStoragetItem('userId') || 'mock_user_123',
      //   deviceId: getStoragetItem('deviceId') || 'mock_device_456',
      //   platform: 'app',
      //   timestamp: Date.now(),
      // },
    }

    this.send(subscribeData)
    console.log('发送订阅消息:', subscribeData)
  }

  /**
   * 设置全局消息处理器
   * 每次连接成功后都会调用，确保处理器始终有效
   */
  private setupGlobalHandlers(): void {
    console.log('设置全局WebSocket消息处理器')

    // 清除之前的全局处理器，避免重复注册
    this.off('new_order')
    this.off('system_maintenance')
    this.off('force_update')

    // 监听新订单 - 全局处理
    this.on('new_order', (message: WSMessage) => {
      console.log('全局处理新订单:', message.data)

      // 播放提示音
      // #ifdef APP-PLUS
      try {
        uni.playBackgroundAudio({
          src: '/static/sounds/new_order.mp3',
        })
      } catch (error) {
        console.log('播放提示音失败:', error)
      }
      // #endif

      // 发送本地通知
      // #ifdef APP-PLUS
      try {
        plus.push.createMessage('收到新订单', 'new_order', {
          title: '新订单提醒',
          content: `从${message.data.startCity || '未知'}到${
            message.data.endCity || '未知'
          }的订单`,
        })
      } catch (error) {
        console.log('发送本地通知失败:', error)
      }
      // #endif
    })

    // 监听系统维护通知
    this.on('system_maintenance', (message: WSMessage) => {
      console.log('收到系统维护通知:', message.data)
      uni.showModal({
        title: '系统维护通知',
        content: message.data.content || '系统即将进行维护',
        showCancel: false,
        confirmText: '知道了',
      })
    })

    // 监听强制更新通知
    this.on('force_update', (message: WSMessage) => {
      console.log('收到强制更新通知:', message.data)
      uni.showModal({
        title: '版本更新',
        content: '检测到新版本，请更新后继续使用',
        showCancel: false,
        confirmText: '立即更新',
        success: (res) => {
          if (res.confirm && message.data.downloadUrl) {
            // 跳转到应用商店或下载页面
            // #ifdef APP-PLUS
            try {
              plus.runtime.openURL(message.data.downloadUrl)
            } catch (error) {
              console.log('打开下载链接失败:', error)
            }
            // #endif
          }
        },
      })
    })
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data)
      // 检查是否是心跳消息
      if (
        message.type === 'heartbeat' ||
        message.type === 'ping' ||
        message.type === 'pong'
      ) {
        console.log('📨 收到心跳响应消息')
        // 收到后端心跳响应，重置心跳检测（作为额外保障）
        this.resetHeartbeat()
        return
      }

      // 处理业务消息
      const wsMessage: WSMessage = {
        type: message.type || 'message',
        data: message.data || message,
        timestamp: Date.now(),
      }
      // wsMessage.type = 'force_update'
      console.log('收到WebSocket消息:', wsMessage)
      this.notifyHandlers(wsMessage)
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  /**
   * 通知消息处理器
   */
  private notifyHandlers(message: WSMessage): void {
    const handlers = this.messageHandlers.get(message.type) || []
    const allHandlers = this.messageHandlers.get('*') || []

    // 合并处理器数组并执行
    const allMessageHandlers = handlers.concat(allHandlers)
    allMessageHandlers.forEach((handler) => {
      try {
        handler(message)
      } catch (error) {
        console.error('消息处理器执行失败:', error)
      }
    })
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    // 先停止现有的心跳发送定时器
    this.stopHeartbeat()

    // 启动心跳超时检测
    this.resetHeartbeat()

    // 每30秒发送一次心跳
    const sendHeartbeat = () => {
      if (this.status === WSStatus.CONNECTED) {
        // 直接使用uni.sendSocketMessage来检测发送状态
        uni.sendSocketMessage({
          data: JSON.stringify({
            type: 'ping',
            timestamp: Date.now(),
          }),
          success: () => {
            console.log('💓 发送心跳保活消息成功 (30秒间隔)')
            // ping发送成功，重置心跳检测定时器，说明连接正常
            this.resetHeartbeat()
            // 继续下一次心跳发送
            this.heartbeatSendTimer = setTimeout(sendHeartbeat, 30000)
          },
          fail: (error: any) => {
            console.error('💓 发送心跳消息失败，连接可能异常:', error)
            // ping发送失败，说明连接有问题，立即触发重连
            this.setStatus(WSStatus.ERROR)
            this.scheduleReconnect()
          },
        })
      } else {
        console.log('💓 连接已断开，停止心跳发送')
      }
    }

    // 30秒后开始第一次心跳发送
    this.heartbeatSendTimer = setTimeout(sendHeartbeat, 30000)
    console.log('💓 心跳发送定时器已启动，30秒后开始发送')
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatSendTimer) {
      clearTimeout(this.heartbeatSendTimer)
      this.heartbeatSendTimer = null
      console.log('💓 心跳发送定时器已停止')
    }
    if (this.heartbeatCheckTimer) {
      clearTimeout(this.heartbeatCheckTimer)
      this.heartbeatCheckTimer = null
      console.log('💓 心跳检测定时器已停止')
    }
  }

  /**
   * 开始位置上报
   * 每分钟发送一次当前坐标
   */
  private startLocationReporting(): void {
    // 先停止现有的位置上报定时器
    this.stopLocationReporting()

    console.log('📍 开始位置上报')

    // 立即发送一次位置
    this.sendLocationData()

    // 每分钟发送一次位置
    const sendLocation = () => {
      if (this.status === WSStatus.CONNECTED) {
        this.sendLocationData()
        // 保存定时器ID，确保可以正确清理
        this.locationTimer = setTimeout(sendLocation, 60000) // 60秒 = 1分钟
        console.log('📍 位置上报定时器已设置，60秒后发送下次位置')
      } else {
        console.log('📍 连接已断开，停止位置上报')
      }
    }

    // 60秒后开始定期位置上报
    this.locationTimer = setTimeout(sendLocation, 60000)
    console.log('📍 位置上报定时器已启动，60秒后开始定期发送')
  }

  /**
   * 停止位置上报
   */
  private stopLocationReporting(): void {
    if (this.locationTimer) {
      clearTimeout(this.locationTimer)
      this.locationTimer = null
      console.log('📍 位置上报定时器已停止')
    }
  }

  /**
   * 发送位置数据
   */
  private sendLocationData(): void {
    // 获取当前位置
    uni.getLocation({
      type: 'gcj02', // 国测局坐标系
      success: (res) => {
        const userInfo = getStoragetItem('userInfo')
        const userId = userInfo?.id || 'mock_user_123'
        const locationData = {
          type: 'location_report',
          data: {
            userId: userId,
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            altitude: res.altitude,
            speed: res.speed,
            timestamp: Date.now(),
            address: res.address || '未知位置',
          },
        }

        this.send(locationData)
        console.log('发送位置信息:', locationData.data)
      },
      fail: (error) => {
        console.error('获取位置失败:', error)

        // 发送位置获取失败的消息
        const errorData = {
          type: 'location_report',
          data: {
            error: '位置获取失败',
            errorCode: error.errCode,
            errorMsg: error.errMsg,
            timestamp: Date.now(),
          },
        }

        this.send(errorData)
      },
    })
  }

  /**
   * 重置心跳超时检测定时器
   */
  private resetHeartbeat(): void {
    if (this.heartbeatCheckTimer) {
      clearTimeout(this.heartbeatCheckTimer)
    }

    // 5分钟内没有任何心跳活动则认为连接异常（作为最后保障）
    // 主要依赖ping发送成功来判断连接状态，这里只是兜底机制
    this.heartbeatCheckTimer = setTimeout(() => {
      console.log('💓 长时间无心跳活动，重新连接 (5分钟超时)')
      this.setStatus(WSStatus.ERROR)
      this.scheduleReconnect()
    }, 300000) // 5分钟 = 300秒
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (!this.isAppActive) {
      console.log('应用不活跃，跳过重连')
      return
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectAttempts++
    const delay = Math.min(5000 * this.reconnectAttempts, 30000) // 递增延迟，最大30秒

    console.log(
      `🔄 安排重连，第${this.reconnectAttempts}次，${delay / 1000}秒后执行`
    )

    this.reconnectTimer = setTimeout(() => {
      if (this.isAppActive) {
        console.log(`🚀 开始执行重连 (第${this.reconnectAttempts}次)`)

        // 重连前重置状态，确保connect()方法能正常执行
        if (this.status === WSStatus.CONNECTING) {
          console.log('🔄 重连前重置CONNECTING状态')
          this.setStatus(WSStatus.DISCONNECTED)
        }

        this.connect()
      } else {
        console.log('重连时应用已不活跃，取消重连')
      }
    }, delay)
  }

  /**
   * 设置连接状态
   */
  private setStatus(status: WSStatus): void {
    if (this.status !== status) {
      const oldStatus = this.status
      this.status = status
      console.log(`📊 WebSocket状态变更: ${oldStatus} → ${status}`)
      console.log(`📊 当前重连次数: ${this.reconnectAttempts}`)
      console.log(`📊 应用活跃状态: ${this.isAppActive}`)

      // 通知状态变化
      this.notifyHandlers({
        type: 'status_change',
        data: { status, oldStatus },
        timestamp: Date.now(),
      })
    }
  }

  /**
   * 发送消息
   */
  send(message: any): void {
    if (this.status !== WSStatus.CONNECTED) {
      console.warn('WebSocket未连接，无法发送消息')
      return
    }

    try {
      const data =
        typeof message === 'string' ? message : JSON.stringify(message)
      uni.sendSocketMessage({
        data: data,
        success: () => {
          console.log('消息发送成功:', message)
        },
        fail: (error: any) => {
          console.error('消息发送失败:', error)
        },
      })
    } catch (error) {
      console.error('消息序列化失败:', error)
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    console.log('断开WebSocket连接')

    // 发送取消订阅消息
    if (this.status === WSStatus.CONNECTED) {
      this.sendUnsubscribeMessage()
      // 等待消息发送完成
      await new Promise((resolve) => setTimeout(resolve, 500))
    }

    this.setStatus(WSStatus.DISCONNECTED)

    // 清理所有定时器
    console.log('🧹 清理所有定时器')
    this.stopHeartbeat()
    this.stopLocationReporting()
    this.stopConnectionCheck()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
      console.log('🔄 重连定时器已清理')
    }

    // 关闭WebSocket连接
    if (this.socket) {
      uni.closeSocket({
        success: () => {
          console.log('WebSocket连接已关闭')
        },
      })
      this.socket = null
    }
  }

  /**
   * 发送取消订阅消息
   */
  private sendUnsubscribeMessage(): void {
    const unsubscribeData = {
      type: 'unsubscribe',
      // data: {
      //   userId: getStoragetItem('userId') || 'mock_user_123',
      //   deviceId: getStoragetItem('deviceId') || 'mock_device_456',
      //   timestamp: Date.now(),
      // },
    }

    this.send(unsubscribeData)
    console.log('发送取消订阅消息:', unsubscribeData)
  }

  /**
   * 添加消息监听器
   */
  on(type: string, handler: Function): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type)!.push(handler)
  }

  /**
   * 移除消息监听器
   */
  off(type: string, handler?: Function): void {
    if (!handler) {
      this.messageHandlers.delete(type)
      return
    }

    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): WSStatus {
    return this.status
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.status === WSStatus.CONNECTED
  }

  /**
   * 用户登录后调用此方法建立WebSocket连接
   */
  onUserLogin(): void {
    console.log('👤 用户登录，尝试建立WebSocket连接')
    this.connect()
  }

  /**
   * 用户退出登录时调用此方法断开WebSocket连接
   */
  onUserLogout(): void {
    console.log('👤 用户退出登录，断开WebSocket连接')
    this.disconnect()
  }

  /**
   * 检查当前用户登录状态（公开方法）
   */
  checkLoginStatus(): boolean {
    const loginStatus = this.checkUserLoginStatus()
    return loginStatus.isLoggedIn
  }
}

// 创建全局WebSocket实例
const wsManager = new WebSocketManager()

// WebSocket将通过App.vue中的onLaunch手动初始化
// 这样可以确保在正确的时机建立连接，避免时机问题

console.log('WebSocket管理器已创建，等待手动初始化')

// 导出WebSocket管理器
export default wsManager

// 导出便捷方法
export const ws = {
  // 监听消息
  on: (type: string, handler: Function) => wsManager.on(type, handler),

  // 移除监听
  off: (type: string, handler?: Function) => wsManager.off(type, handler),

  // 发送消息
  send: (message: any) => wsManager.send(message),

  // 获取状态
  getStatus: () => wsManager.getStatus(),

  // 是否连接
  isConnected: () => wsManager.isConnected(),

  // 手动连接
  connect: () => wsManager.connect(),

  // 手动断开
  disconnect: () => wsManager.disconnect(),

  // 用户登录后连接
  onUserLogin: () => wsManager.onUserLogin(),

  // 用户退出登录时断开
  onUserLogout: () => wsManager.onUserLogout(),

  // 检查登录状态
  checkLoginStatus: () => wsManager.checkLoginStatus(),
}
