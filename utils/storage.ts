let storageNodeKeys = [
  'x-token',
  'shipmentsAddress',
  'placeAddress',
  'userType',
  'userInfo'
]

export const setStoragetItem = (key: any, value: any) => {
  try {
    if (storageNodeKeys.indexOf(key) != -1) {
      uni.setStorageSync(key, value)
      return true
    } else {
      uni.showToast({
        title: '缓存key不在配置内',
        icon: 'none',
      })
      return false
    }
  } catch (e) {
    console.error('Error setting item:', e)
    return false
  }
}

export const getStoragetItem = (key: any) => {
  try {
    const value = uni.getStorageSync(key)
    if (value) {
      return value
    }
    return null
  } catch (e) {
    console.error('Error getting item:', e)
  }
}

export const removeStoragetItem = (key: any) => {
  try {
    uni.removeStorageSync(key)
  } catch (e) {
    console.error('Error removing item:', e)
  }
}

export const clearStorage = () => {
  try {
    uni.clearStorageSync()
  } catch (e) {
    console.error('Error clearing storage:', e)
  }
}

export const getAllStorageKeys = () => {
  const keys = []
  try {
    const info = uni.getStorageInfoSync()
    keys.push(...info.keys)
  } catch (e) {
    console.error('Error getting keys:', e)
  }
  return keys
}
