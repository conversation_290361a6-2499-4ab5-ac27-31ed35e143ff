export const isString = (value : any) => {
	return typeof value == 'string' && value.constructor == String;
}

/**
 * 生成简短的UUID (去掉连字符)
 * @returns {string} 32位的UUID字符串
 */
export const generateShortUUID = (): string => {
	return generateUUID().replace(/-/g, '')
}

/**
 * 生成指定长度的随机ID
 * @param {number} length 长度，默认8位
 * @returns {string} 随机ID字符串
 */
export const generateRandomId = (length: number = 8): string => {
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
	let result = ''
	for (let i = 0; i < length; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length))
	}
	return result
}

/**
 * 生成数字ID
 * @param {number} length 长度，默认10位
 * @returns {string} 数字ID字符串
 */
export const generateNumericId = (length: number = 10): string => {
	let result = ''
	for (let i = 0; i < length; i++) {
		result += Math.floor(Math.random() * 10).toString()
	}
	return result
}
import dayjs from 'dayjs';
/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param date - 要格式化的日期，可以是字符串、数字或 Date 对象
 * @returns 格式化后的日期字符串，如果输入为空则返回空字符串
 */
export const formatDate = (date: string | number | Date) => {
	if (!date) return '';
	return dayjs(date).format('YYYY-MM-DD');
};
export const formatDateTime = (date: string | number | Date) => {
	if (!date) return '';
	return dayjs(date).format('YYYY-MM-DD HH:MM');
};