/**
 * 电话相关工具函数
 */

/**
 * 验证电话号码格式
 * @param phoneNumber 电话号码
 * @returns 是否为有效的电话号码
 */
export const validatePhoneNumber = (phoneNumber: string): boolean => {
  if (!phoneNumber) return false
  
  // 移除所有空格、连字符等特殊字符
  const cleanNumber = phoneNumber.replace(/[\s-()]/g, '')
  
  // 中国大陆手机号码正则：1开头，第二位是3-9，总共11位
  const mobileRegex = /^1[3-9]\d{9}$/
  
  // 中国大陆固定电话正则：区号+号码，支持带区号的格式
  const landlineRegex = /^(0\d{2,3}-?)?\d{7,8}$/
  
  return mobileRegex.test(cleanNumber) || landlineRegex.test(cleanNumber)
}

/**
 * 拨打电话功能
 * @param phoneNumber 电话号码
 * @param showToast 是否显示提示信息，默认为true
 */
export const makeCall = (phoneNumber: string, showToast: boolean = true): void => {
  console.log('准备拨打电话:', phoneNumber)

  // 验证电话号码
  if (!validatePhoneNumber(phoneNumber)) {
    console.error('电话号码格式不正确:', phoneNumber)
    if (showToast) {
      uni.showToast({
        title: '电话号码格式不正确',
        icon: 'none'
      })
    }
    return
  }

  // 检查平台支持
  // #ifdef APP-PLUS
  console.log('当前平台: APP-PLUS')
  // #endif

  // #ifdef H5
  console.log('当前平台: H5')
  uni.showModal({
    title: '提示',
    content: 'H5环境不支持直接拨打电话，请手动拨打: ' + phoneNumber,
    showCancel: false
  })
  return
  // #endif

  // #ifdef MP
  console.log('当前平台: 小程序')
  uni.showModal({
    title: '提示',
    content: '小程序环境不支持直接拨打电话，请手动拨打: ' + phoneNumber,
    showCancel: false
  })
  return
  // #endif

  // 拨打电话
  uni.makePhoneCall({
    phoneNumber: phoneNumber,
    success: () => {
      console.log('拨打电话成功:', phoneNumber)
    },
    fail: (err) => {
      console.error('拨打电话失败:', err)
      console.error('错误详情:', JSON.stringify(err))

      let errorMsg = '拨打电话失败'
      if (err.errMsg) {
        if (err.errMsg.includes('permission')) {
          errorMsg = '没有拨打电话权限，请在设置中开启'
        } else if (err.errMsg.includes('cancel')) {
          errorMsg = '用户取消拨打电话'
        } else {
          errorMsg = `拨打失败: ${err.errMsg}`
        }
      }

      uni.showModal({
        title: '拨打电话失败',
        content: errorMsg + '\n\n请手动拨打: ' + phoneNumber,
        showCancel: false
      })
    }
  })
}

/**
 * 快速拨打电话（不显示验证失败提示）
 * @param phoneNumber 电话号码
 */
export const quickCall = (phoneNumber: string): void => {
  makeCall(phoneNumber, false)
}

/**
 * 格式化电话号码显示
 * @param phoneNumber 电话号码
 * @returns 格式化后的电话号码
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return ''
  
  const cleanNumber = phoneNumber.replace(/[\s-()]/g, '')
  
  // 手机号码格式化：138 0013 8000
  if (/^1[3-9]\d{9}$/.test(cleanNumber)) {
    return cleanNumber.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  
  // 固定电话格式化保持原样
  return phoneNumber
}
