// vite.config.js
import {
	defineConfig
} from 'vite'
import uni from '@dcloudio/vite-plugin-uni' //官方内置的,不用下载,下面代码粘贴直接用即可

export default defineConfig({
	plugins: [
		uni()
	],
	server: {
		port: 8080, // 设置开发服务器端口，与WebSocket URL匹配
		proxy: {
			'/testApi': {
				// target: 'http://192.168.3.154:8888', // 目标服务
				target: 'http://192.168.18.6:8088',
				changeOrigin: true,
				rewrite: path => path.replace(/^\/testApi/, ''),
			},
			// WebSocket代理配置
			'/websocketApi': {
				target: 'ws://47.93.77.128:18099', // 使用manifest.json中的目标地址
				ws: true, // 启用WebSocket代理
				changeOrigin: true,
				rewrite: path => path.replace(/^\/websocketApi/, ''),
			},
			// HTTP接口代理
			// '/transport': {
			// 	target: 'http://47.93.77.128:18099', // 使用manifest.json中的目标地址
			// 	changeOrigin: true,
			// 	rewrite: path => path.replace(/^\/transport/, ''),
			// }
		}
	}
})