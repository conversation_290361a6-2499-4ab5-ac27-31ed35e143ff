<template>
  <view class="orders-container">
    <!-- 背景渐变层 -->
    <view class="header-bg"></view>

    <!-- 标题栏区域 -->
    <view class="header-region" :style="{ top: systemInfo.safeAreaInsets.top * 2 + 'rpx' }">
      <!-- 返回按钮 -->
      <view class="back-btn" @click="goBack">
        <view class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON>jiant<PERSON>"></view>
      </view>

      <!-- 用户类型切换区域 -->
      <view class="user-type-switch">
        <!-- 自定义切换按钮，参照cu-payment-dialog组件实现 -->
        <view class="tab-container">
          <view 
            class="tab-item" 
            :class="{ active: currentUserType === 0 }" 
            @click="handleUserTypeChange(0)"
          >
            <text>货主</text>
          </view>
          <view 
            class="tab-item" 
            :class="{ active: currentUserType === 1 }" 
            @click="handleUserTypeChange(1)"
          >
            <text>司机</text>
          </view>
        </view>
      </view>

      <!-- 右侧占位，保持居中 -->
      <view class="placeholder"></view>
    </view>

    <!-- 使用动态组件替代条件渲染 -->
    <component 
      :is="currentComponent" 
      :key="currentUserType" 
      :hasTopSwitch="true" 
    />
  </view>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import Orders from '@/pageCom/orders/orders.vue'
import OrderDriver from '@/pageComDriver/orders/orders.vue'

// 获取系统信息
const systemInfo: any = uni.getSystemInfoSync()

// 当前选中的用户类型 (0: 货主, 1: 司机)
const currentUserType = ref(0)

// 计算属性：根据currentUserType决定显示哪个组件
const currentComponent = computed(() => {
  return currentUserType.value === 0 ? Orders : OrderDriver
})

// 处理用户类型切换
const handleUserTypeChange = (index: number) => {
  currentUserType.value = index
  console.log('切换到:', index === 0 ? '货主' : '司机')
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>
<style scoped lang="scss">
.orders-container {
  position: relative;
  min-height: 100vh;
}

.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(v-bind('systemInfo.safeAreaInsets.top * 2 + "rpx"') + 90rpx);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

.header-region {
  position: fixed;
  left: 0;
  width: 100%;
  height: 90rpx;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 10rpx;

  .iconfont {
    font-size: 34rpx;
    color: #333;
  }
}

.user-type-switch {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  
  // 自定义切换按钮样式
  .tab-container {
    display: flex;
    width: 280rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    padding: 8rpx;
    overflow: hidden;

    .tab-item {
      flex: 1;
      height: 64rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      transition: all 0.3s;

      text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }

      &.active {
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        text {
          color: #333;
          font-weight: 600;
        }
      }
    }
  }
}

.placeholder {
  width: 80rpx;
  height: 70rpx;
}
</style>