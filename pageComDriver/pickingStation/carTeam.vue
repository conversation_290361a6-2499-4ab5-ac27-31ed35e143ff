<template>
  <div>配货站-我的车队</div>
  <view class="car-team-page">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text></template>
			<template #title>
        <text>我的车队</text>
        <text>{{ carTeamName }}</text>
      </template>
			<template #rightBtn>
				{{ '' }}
			</template>
		</cu-header>
  </view>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue';
const carTeamName = ref("车队名称")
</script>