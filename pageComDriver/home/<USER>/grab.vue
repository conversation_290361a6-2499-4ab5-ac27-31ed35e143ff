<template>
	<z-paging
		ref="paging"
		:show-scrollbar="false"
		:auto-show-back-to-top="true"
		:use-page-scroll="true"
		auto-show-system-loading
		cell-height-mode="dynamic"
		v-model="dataList"
		@query="queryList"
	>
		<view class="wait-items">
			<uni-swipe-action>
				<uni-swipe-action-item
					v-for="(dataItem, dataIndex) in dataList"
					:key="dataIndex"
					:isMoreLine="true"
					:right-options="options"
					:show="dataItem.show"
					:disabled="dataItem.disabled"
					@click.top="itemHandleClick(dataItem)"
				>
					<view class="item-box" @tap="itemHandleContentClick(dataItem)">
						<view class="item-left">
							<view class="address">
								<text class="address-text">北京 昌平</text>
								<view class="iconfont icon-owner-a-xiangyoujiantou"></view>
								<text class="address-text">北京 丰台</text>
								<view class="tag-type">拼车</view>
							</view>
							<view class="car-info">1.8米｜高栏/面包车｜0.1吨</view>
							<view class="goods-info">零食食品 纸箱 明天 6-12点装货</view>
							<view class="cargo-owner">
								<image src="/static/logo.png" mode=""></image>
								<text class="rating">信用 4.0星</text>
								<text class="rate">交易8 好评率100%</text>
							</view>
						</view>
						<view class="item-right">
							<view class="item-right-top">
								<text class="price">***** 元/趟</text>
								<text class="price-tip">认证后查看运价</text>
							</view>
							<view class="item-right-btn">去抢单</view>
						</view>
					</view>
				</uni-swipe-action-item>
			</uni-swipe-action>
		</view>
	</z-paging>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw } from 'vue';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js';

const paging = ref(null);
// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
const dataList = ref([]);
let dataListVirtual: any = ref([]);

// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用paging.value.reload()即可
const queryList = (pageNo, pageSize) => {
	// 此处请求仅为演示，请替换为自己项目中的请求
	setTimeout(() => {
		const nnn = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
		if (dataListVirtual.value.length < 30) {
			for (let item of nnn) {
				dataListVirtual.value.push(item);
			}
			paging.value.complete([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
		} else {
			paging.value.complete();
		}
	}, 2000);
};
// 类似mixins，如果是页面滚动务必要写这一行，并传入当前ref绑定的paging，注意此处是paging，而非paging.value
useZPaging(paging);

const options = reactive([
	{
		text: `今天`,
		text2: `不再展示`,
		style: {
			backgroundColor: 'gray',
			color: '#fff',
			fontSize: '26rpx'
		}
	}
]);
const itemHandleClick = (data: any) => {
	console.log(data);
};
const itemHandleContentClick = (data: any) => {
	console.log(213, data);
};
</script>

<style lang="scss" scoped>
:deep(.zp-paging-touch-view) {
	.item-title {
		height: 100rpx;
		line-height: 100rpx;
	}
}
.wait-items {
	:deep(.uni-swipe) {
		margin-top: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;
		.right-content {
			background-color: gray;
			color: #fff;
			font-size: 26rpx;
			padding: 0rpx 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
	}
	.item-box {
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		box-sizing: border-box;
		.item-left {
			flex: 1;
			.address {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				.address-text {
					font-size: 38rpx;
					font-weight: 700;
					color: #000;
				}
				.icon-owner-a-xiangyoujiantou {
					font-size: 32rpx;
					font-weight: 600;
					margin: 0 10rpx;
				}
				.tag-type {
					font-size: 28rpx;
					color: #8ab66e;
					padding: 0rpx 10rpx;
					border: 1px solid #8ab66e;
					border-radius: 8rpx;
					margin-left: 10rpx;
				}
			}
			.car-info {
				font-size: 34rpx;
				color: #333;
				font-weight: 600;
				margin-top: 18rpx;
			}
			.goods-info {
				font-size: 34rpx;
				color: #333;
				font-weight: 600;
			}
			.cargo-owner {
				display: flex;
				align-items: center;
				margin-top: 10rpx;
				image {
					width: 50rpx;
					height: 50rpx;
					margin-right: 10rpx;
				}
				.rating {
					font-size: 28rpx;
					color: #000;
					font-weight: 600;
					margin-right: 10rpx;
				}
				.rate {
					font-size: 26rpx;
					color: $uni-text-color-grey;
				}
			}
		}
		.item-right {
			width: 240rpx;
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			justify-content: space-between;
			padding-top: 10rpx;
			.item-right-top {
				text-align: right;
				display: flex;
				flex-direction: column;
				.price {
					font-size: 28rpx;
					color: red;
				}
				.price-tip {
					font-size: 27rpx;
					color: #b4b4b4;
					margin-top: 20rpx;
				}
			}
			.item-right-btn {
				font-size: 30rpx;
				color: #fff;
				padding: 16rpx 30rpx;
				box-sizing: border-box;
				background-color: #e65147;
				border-radius: 20rpx;
			}
		}
	}
}
</style>
