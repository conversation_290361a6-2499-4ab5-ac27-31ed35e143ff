<template>
  <view class="home-page">
    <cu-header-free :titleBarHeight="80" :isScroll="true" :titleBarBg="titleBarBg" :style="{
      opacity: transparency
    }">
      <template #leftContainer>
        <view class="items">
          <view class="item">
            <up-icon name="bell" size="32rpx"></up-icon>
            <text>消息</text>
          </view>
        </view>
      </template>
      <!-- <template #rightContainer>
        <view class="items">
          <view class="item">
            <up-icon name="bell" size="32rpx"></up-icon>
            <text>消息</text>
          </view>
        </view>
      </template> -->
    </cu-header-free>
    <div class="aaa" v-if="transparency == 0"></div>
    <view class="tabs-box">
      <view class="tabs-box-rain">
        <view class="tabs">
          <view class="tab-item" v-for="(item, index) of tabList" :key="index" @click="tabClick(item)">
            {{ item.label }}
            <view class="line" v-if="currentTab == item.value"></view>
          </view>
        </view>
      </view>
      <!-- <image src="@/static/images/car.jpeg" class="car-img"></image> -->
    </view>
    <recommend v-if="currentTab == 1"></recommend>
    <subscribe v-if="currentTab == 2"></subscribe>
    <!-- <grab v-if="currentTab == 3"></grab> -->
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { onPageScroll } from '@dcloudio/uni-app';
import recommend from './components/recommend.vue';
import subscribe from './components/subscribe.vue';
// import grab from './components/grab.vue';

const systemInfo: any = uni.getSystemInfoSync();
const defaultHeight = systemInfo.safeAreaInsets.top * 2 + 'rpx';

const currentTab = ref(1);
const tabList = ref([
  {
    label: '推荐',
    value: 1,
    name: 'recommend'
  },
  {
    label: '订阅',
    value: 2,
    name: 'subscribe'
  },
  // {
  //   label: '秒抢',
  //   value: 3,
  //   name: 'grab'
  // }
]);
const tabClick = (item: any) => {
  currentTab.value = item.value;
};
const transparency = ref(1);
const titleBarBg = computed(() => {
  return `linear-gradient(to right, rgba(247,237,235,${transparency.value}), rgba(245,234,235,${transparency.value}), rgba(241,202,196,${transparency.value}))`;
});
onPageScroll((res) => {
  let scrollTop = res.scrollTop;
  if (scrollTop <= 20) {
    transparency.value = 1;
  } else if (scrollTop > 20 && scrollTop < 80) {
    transparency.value = 1 - scrollTop / 120;
  } else {
    transparency.value = 0;
  }
});
</script>

<style lang="scss" scoped>
.items {
  display: flex;

  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    text {
      color: #000;
      font-size: $uni-font-size-sm;
    }
  }
}

.aaa {
  width: 100%;
  height: v-bind(defaultHeight);
  background-color: #fafafc;
  position: fixed;
  top: 0;
  z-index: 9;
}

.tabs-box {
  position: sticky;
  z-index: 9;
  top: v-bind(defaultHeight);
  background-color: #fafafc;
  display: flex;
  align-content: space-between;

  .tabs-box-rain {
    width: 100%;
    height: 90rpx;
    display: flex;
    align-items: center;

    .tabs {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding-left: 28rpx;

      .tab-item {
        margin-right: 40rpx;
        position: relative;

        .line {
          width: 60rpx;
          height: 8rpx;
          background-color: #df7f4f;
          position: absolute;
          bottom: 0rpx;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .car-img {
    width: 200rpx;
    height: 100rpx;
    margin-top: -10rpx;
  }
}

.nnn {
  width: 100%;
  height: 3000rpx;
  background-color: blue;
}
</style>
