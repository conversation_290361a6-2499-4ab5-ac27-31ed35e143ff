<template>
  <view class="container">
    <map v-if="!mapLoadError" id="map" class="map" :latitude="latitude" :longitude="longitude" :scale="scale" :markers="markers" :polyline="polyline" :show-location="true" :enable-zoom="true" :enable-scroll="true" :include-points="includePoints" @regionchange="onRegionChange" @tap="onMapTap" @markertap="onMarkerTap" @callouttap="onCalloutTap" @error="onMapError">

      <!-- 地图内部导航按钮 -->
      <cover-view class="map-navigation-buttons">
        <!-- 背景板 -->
        <cover-view class="navigation-background">
          <cover-view class="background-content"></cover-view>
        </cover-view>

        <!-- 按钮区域 -->
        <cover-view class="buttons-container">
          <cover-view class="nav-btn-cover start-nav-btn-cover" @tap="startNavigation">
            <cover-view class="btn-icon-cover" @tap="startNavigation">🧭</cover-view>
            <cover-view class="btn-text-cover" @tap="startNavigation">开始导航</cover-view>
          </cover-view>
          <cover-view class="nav-btn-cover back-btn-cover" @tap="goBack">
            <cover-view class="btn-icon-cover"  @tap="goBack">↩️</cover-view>
            <cover-view class="btn-text-cover"  @tap="goBack">返回</cover-view>
          </cover-view>
        </cover-view>
      </cover-view>

    </map>

    <!-- 地图加载出错时的显示 -->
    <view v-if="mapLoadError" class="map-error">
      <view class="error-content">
        <text class="error-icon">!</text>
        <text class="error-text">地图加载失败</text>
        <button class="retry-btn" hover-class="btn-hover" @tap="retryLoadMap">重试</button>
      </view>
    </view>

    <!-- 加载中的显示 -->
    <view v-if="isLoading && !mapLoadError" class="loading-mask">
      <view class="loading-content">
        <text class="loading-text">地图加载中...</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { gConfig } from '../../config';

// 高德地图API Key - 从配置文件获取
const AMAP_KEY = gConfig.gdApiKey;

// 位置坐标数据
const startCoord = ref({
  latitude: 39.9042,
  longitude: 116.4074,
  name: '起点'
});

const endCoord = ref({
  latitude: 39.0842,
  longitude: 117.2009,
  name: '终点'
});

// 地图相关数据
const latitude = ref(39.5); // 初始位置设为中间点，后续会更新
const longitude = ref(116.8);
const scale = ref(8); // 初始缩放级别
const markers = ref<any[]>([]);
const polyline = ref<any[]>([]);
const mapContext = ref<any>(null);
const mapInitialized = ref(false);
const mapLoadError = ref(false);
const isLoading = ref(true);
const retryCount = ref(0);
const MAX_RETRY = 3;
const routeInfo = ref<any>(null);
// 用于自动调整缩放级别的点集合
const includePoints = ref<any[]>([]);
// 路线规划状态
const routeStatus = ref('未开始');

// 计算两点之间的距离（使用Haversine公式）
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // 地球半径，单位公里
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // 距离，单位公里
  return distance;
};

// 根据距离计算合适的缩放级别
const calculateScale = (distance: number): number => {
  // 这里的映射关系需要根据实际情况调整
  if (distance > 1000) return 6;
  if (distance > 500) return 7;
  if (distance > 300) return 8;
  if (distance > 100) return 9;
  if (distance > 50) return 10;
  if (distance > 20) return 11;
  if (distance > 10) return 12;
  if (distance > 5) return 13;
  if (distance > 2) return 14;
  if (distance > 1) return 15;
  if (distance > 0.5) return 16;
  return 17;
};

// 计算两点的中心点和合适的缩放级别
const calculateViewSettings = () => {
  const lat1 = startCoord.value.latitude;
  const lon1 = startCoord.value.longitude;
  const lat2 = endCoord.value.latitude;
  const lon2 = endCoord.value.longitude;

  // 计算中心点
  latitude.value = (lat1 + lat2) / 2;
  longitude.value = (lon1 + lon2) / 2;

  // 计算距离并设置合适的缩放级别
  const distance = calculateDistance(lat1, lon1, lat2, lon2);
  scale.value = calculateScale(distance);

  console.log(`距离: ${distance.toFixed(2)}公里, 缩放级别: ${scale.value}`);

  // 更新包含点集合
  updateIncludePoints();
};

// 获取当前位置
const getCurrentPosition = () => {
  return new Promise<{ latitude: number, longitude: number }>((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02', // 使用高德地图坐标系
      success: (res) => {
        console.log('获取当前位置成功:', res);
        resolve({
          latitude: res.latitude,
          longitude: res.longitude
        });
      },
      fail: (err) => {
        console.error('获取当前位置失败:', err);
        uni.showToast({
          title: '获取位置失败，请检查定位权限',
          icon: 'none',
          duration: 3000
        });
        reject(err);
      }
    });
  });
};

// 地图错误处理
const onMapError = (e: any) => {
  console.error('地图加载错误', e);
  mapLoadError.value = true;
  isLoading.value = false;

  // 显示错误提示
  uni.showToast({
    title: '地图加载失败，请检查网络连接',
    icon: 'none',
    duration: 3000
  });
};

// 重试加载地图
const retryLoadMap = () => {
  if (retryCount.value >= MAX_RETRY) {
    uni.showToast({
      title: '多次加载失败，请稍后再试',
      icon: 'none'
    });
    return;
  }

  retryCount.value++;
  mapLoadError.value = false;
  isLoading.value = true;

  // 重新初始化地图
  setTimeout(() => {
    initMap();
  }, 1000);
};

// 区域变化事件处理
const onRegionChange = (e: any) => {
  if (e.type === 'end' && e.causedBy === 'drag') {
    // 用户拖动地图结束时的处理逻辑
    console.log('地图区域变化', e);

    // 获取当前缩放级别
    getMapScale();
  }
};

// 获取当前缩放级别
const getMapScale = () => {
  if (!mapContext.value) return;

  mapContext.value.getScale({
    success: (res: any) => {
      console.log('当前缩放级别:', res.scale);
      scale.value = res.scale;
    },
    fail: (err: any) => {
      console.error('获取缩放级别失败:', err);
    }
  });
};

// 地图点击事件处理
const onMapTap = (e: any) => {
  console.log('地图点击', e);

  // 示例：点击地图添加标记点
  const { latitude: lat, longitude: lng } = e.detail;

  // 创建新标记
  const newMarker = {
    id: Date.now(),
    latitude: lat,
    longitude: lng,
    width: 32,
    height: 32,
    callout: {
      content: `位置 (${lat.toFixed(6)}, ${lng.toFixed(6)})`,
      color: '#000000',
      fontSize: 14,
      borderRadius: 4,
      bgColor: '#ffffff',
      padding: 8,
      display: 'BYCLICK'
    },
    iconPath: '/static/images/start_dot.png',
  };

  markers.value = [...markers.value, newMarker];

  // 更新包含点集合
  updateIncludePoints();
};

// 标记点击事件
const onMarkerTap = (e: any) => {
  const markerId = e.detail.markerId;
  console.log('标记点击', markerId);

  // 找到对应标记
  const marker = markers.value.find(item => item.id === markerId);
  if (marker) {
    // 处理标记点击事件，例如显示气泡
    uni.showToast({
      title: `选中位置: ${marker.name || '未命名位置'}`,
      icon: 'none'
    });
  }
};

// 气泡点击事件
const onCalloutTap = (e: any) => {
  const markerId = e.detail.markerId;
  console.log('气泡点击', markerId);

  // 找到对应标记
  const marker = markers.value.find(item => item.id === markerId);
  if (marker) {
    // 处理气泡点击事件
    uni.showModal({
      title: '位置信息',
      content: `${marker.name || '未命名位置'}\n纬度: ${marker.latitude.toFixed(6)}\n经度: ${marker.longitude.toFixed(6)}`,
      showCancel: false
    });
  }
};

// 初始化起点和终点的标记点
const initRouteMarkers = () => {
  const startMarker = {
    id: 1,
    latitude: startCoord.value.latitude,
    longitude: startCoord.value.longitude,
    name: startCoord.value.name,
    width: 50,
    height: 50,
    callout: {
      content: startCoord.value.name,
      color: '#000000',
      fontSize: 16,
      borderRadius: 4,
      bgColor: '#ffffff',
      padding: 10,
      display: 'ALWAYS'
    },
    iconPath: '/static/images/start_dot.png',
  };

  const endMarker = {
    id: 2,
    latitude: endCoord.value.latitude,
    longitude: endCoord.value.longitude,
    name: endCoord.value.name,
    width: 50,
    height: 50,
    callout: {
      content: endCoord.value.name,
      color: '#000000',
      fontSize: 16,
      borderRadius: 4,
      bgColor: '#ffffff',
      padding: 10,
      display: 'ALWAYS'
    },
    iconPath: '/static/images/end_dot.png',
  };

  console.log('初始化起终点标记:', startMarker, endMarker);

  markers.value = [startMarker, endMarker];

  // 更新包含点集合
  updateIncludePoints();
};

// 更新包含点集合
const updateIncludePoints = () => {
  // 添加所有标记点到包含点集合
  includePoints.value = markers.value.map(marker => ({
    latitude: marker.latitude,
    longitude: marker.longitude
  }));

  // 如果有路线点，也添加到包含点集合中
  if (polyline.value.length > 0 && polyline.value[0].points) {
    // 为了不让地图缩放过度，只添加路线的起点、终点和几个关键点
    const points = polyline.value[0].points;
    if (points.length > 0) {
      // 添加起点
      includePoints.value.push({
        latitude: points[0].latitude,
        longitude: points[0].longitude
      });

      // 添加终点
      includePoints.value.push({
        latitude: points[points.length - 1].latitude,
        longitude: points[points.length - 1].longitude
      });

      // 如果路线点超过10个，每隔几个点添加一个到包含点集合
      if (points.length > 10) {
        const step = Math.floor(points.length / 5);
        for (let i = step; i < points.length; i += step) {
          includePoints.value.push({
            latitude: points[i].latitude,
            longitude: points[i].longitude
          });
        }
      }
    }
  }

  console.log('更新包含点集合:', includePoints.value);
};

// 调整地图视野以包含所有点
const adjustMapView = () => {
  if (!mapContext.value || !includePoints.value.length) return;

  console.log('调整地图视野');

  // 使用地图上下文的includePoints方法动态调整视野
  mapContext.value.includePoints({
    points: includePoints.value,
    padding: [80, 80, 80, 80], // 设置内边距，确保标记点不会太靠近地图边缘
    success: () => {
      console.log('地图视野调整成功');
    },
    fail: (error: any) => {
      console.error('地图视野调整失败', error);
    }
  });
};

// 使用高德地图API获取真实路线规划
const fetchRealRoute = async () => {
  try {
    isLoading.value = true;
    routeStatus.value = '正在规划路线...';

    // 构建高德地图驾车路线规划API请求URL
    const origin = `${startCoord.value.longitude},${startCoord.value.latitude}`;
    const destination = `${endCoord.value.longitude},${endCoord.value.latitude}`;
    const url = `https://restapi.amap.com/v3/direction/driving?origin=${origin}&destination=${destination}&extensions=all&output=json&key=${AMAP_KEY}`;

    console.log('路线规划请求:', url);

    // 发起请求
    const response = await new Promise<any>((resolve, reject) => {
      uni.request({
        url: url,
        method: 'GET',
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });

    // 处理响应
    const data = response.data;
    if (data && data.status === '1' && data.route && data.route.paths && data.route.paths.length > 0) {
      const path = data.route.paths[0];

      // 解析路线点
      const steps = path.steps;
      const routePoints: { latitude: number, longitude: number }[] = [];

      // 从每个step中提取polyline坐标点
      steps.forEach((step: any) => {
        if (step.polyline) {
          const pointsArray = step.polyline.split(';');
          pointsArray.forEach((point: string) => {
            const [lng, lat] = point.split(',');
            routePoints.push({
              longitude: parseFloat(lng),
              latitude: parseFloat(lat)
            });
          });
        }
      });

      console.log('解析到路线点数量:', routePoints.length);

      // 设置路线
      polyline.value = [{
        points: routePoints,
        color: '#0066FF',
        width: 8,
        dottedLine: false,
        arrowLine: true,
        borderColor: '#0044CC',
        borderWidth: 1
      }];

      // 添加辅助路线，模拟导航的阴影效果
      polyline.value.push({
        points: routePoints,
        color: 'rgba(0, 102, 255, 0.3)',
        width: 12,
        dottedLine: false,
        arrowLine: false,
        borderWidth: 0
      });

      // 设置路线信息
      const distance = path.distance;
      const duration = path.duration;
      const distanceKm = (parseInt(distance) / 1000).toFixed(1);
      const durationHour = Math.floor(parseInt(duration) / 3600);
      const durationMin = Math.floor((parseInt(duration) % 3600) / 60);

      routeInfo.value = `全程约${distanceKm}公里，预计行车时间${durationHour}小时${durationMin}分钟\n\n主要途经：京津高速 - G2京沪高速`;
      routeStatus.value = '路线规划成功';

      console.log('高德地图路线规划成功', routePoints);

      // 计算并设置地图视图
      calculateViewSettings();

      // 更新包含点集合，用于自动调整视野
      updateIncludePoints();

      // 使用地图上下文的includePoints方法动态调整视野
      adjustMapView();
    } else {
      console.error('高德地图路线规划失败', data);
      routeStatus.value = '路线规划失败，使用备用路线';
      // 如果API调用失败，使用备用路线数据
      createBackupRoute();
    }
  } catch (error) {
    console.error('获取高德地图路线规划出错', error);
    routeStatus.value = '路线规划出错，使用备用路线';
    // 如果API调用出错，使用备用路线数据
    createBackupRoute();
  } finally {
    isLoading.value = false;
  }
};

// 创建备用路线（当API调用失败时使用）
const createBackupRoute = () => {
  console.log('创建备用路线');
  // 模拟导航路线的路径点
  const routePoints = [
    { latitude: startCoord.value.latitude, longitude: startCoord.value.longitude }, // 起点
    // 根据起点和终点生成一些中间点
    {
      latitude: startCoord.value.latitude * 0.8 + endCoord.value.latitude * 0.2,
      longitude: startCoord.value.longitude * 0.8 + endCoord.value.longitude * 0.2
    },
    {
      latitude: startCoord.value.latitude * 0.6 + endCoord.value.latitude * 0.4,
      longitude: startCoord.value.longitude * 0.6 + endCoord.value.longitude * 0.4
    },
    {
      latitude: startCoord.value.latitude * 0.4 + endCoord.value.latitude * 0.6,
      longitude: startCoord.value.longitude * 0.4 + endCoord.value.longitude * 0.6
    },
    {
      latitude: startCoord.value.latitude * 0.2 + endCoord.value.latitude * 0.8,
      longitude: startCoord.value.longitude * 0.2 + endCoord.value.longitude * 0.8
    },
    { latitude: endCoord.value.latitude, longitude: endCoord.value.longitude } // 终点
  ];

  polyline.value = [{
    points: routePoints,
    color: '#0066FF',
    width: 8,
    dottedLine: false,
    arrowLine: true,
    borderColor: '#0044CC',
    borderWidth: 1
  }];

  // 添加辅助路线，模拟导航的阴影效果
  polyline.value.push({
    points: routePoints,
    color: 'rgba(0, 102, 255, 0.3)',
    width: 12,
    dottedLine: false,
    arrowLine: false,
    borderWidth: 0
  });

  // 计算距离
  let totalDistance = 0;
  for (let i = 0; i < routePoints.length - 1; i++) {
    totalDistance += calculateDistance(
      routePoints[i].latitude, routePoints[i].longitude,
      routePoints[i + 1].latitude, routePoints[i + 1].longitude
    );
  }

  // 设置默认路线信息
  const distanceKm = totalDistance.toFixed(1);
  const durationHour = Math.floor(totalDistance / 60);
  const durationMin = Math.floor((totalDistance % 60) * 60);

  routeInfo.value = `全程约${distanceKm}公里，预计行车时间${durationHour}小时${durationMin}分钟\n\n(备用路线数据)`;

  // 计算并设置地图视图
  calculateViewSettings();

  // 更新包含点集合，用于自动调整缩放级别
  updateIncludePoints();

  // 使用地图上下文的includePoints方法动态调整视野
  adjustMapView();
};

// 初始化地图
const initMap = async () => {
  try {
    console.log('开始初始化地图');
    // 获取地图上下文
    mapContext.value = uni.createMapContext('map');
    console.log('地图上下文创建成功');

    // 初始化地图
    setTimeout(async () => {
      mapInitialized.value = true;
      console.log('地图初始化完成，当前起点:', startCoord.value, '终点:', endCoord.value);

      // 初始化标记点
      initRouteMarkers();
      console.log('标记点初始化完成');

      // 计算并设置初始视图
      calculateViewSettings();
      console.log('地图视图设置完成');

      // 获取真实路线规划
      console.log('开始获取路线规划');
      fetchRealRoute();

      isLoading.value = false;
    }, 500);
  } catch (error) {
    console.error('初始化地图失败', error);
    mapLoadError.value = true;
    isLoading.value = false;
  }
};

// 解析路由参数
const parseRouteParams = async (options: any) => {
  console.log('开始解析路由参数:', options);

  // 如果有终点参数
  if (options.end_latitude && options.end_longitude) {
    console.log('发现终点参数:', options.end_latitude, options.end_longitude, options.end_name);
    endCoord.value = {
      latitude: parseFloat(options.end_latitude),
      longitude: parseFloat(options.end_longitude),
      name: options.end_name || '终点'
    };
    console.log('设置终点坐标:', endCoord.value);
  } else if (options.latitude && options.longitude) {
    // 兼容旧版参数格式
    console.log('发现旧版终点参数:', options.latitude, options.longitude, options.name);
    endCoord.value = {
      latitude: parseFloat(options.latitude),
      longitude: parseFloat(options.longitude),
      name: options.name || '终点'
    };
    console.log('设置终点坐标:', endCoord.value);
  } else {
    console.log('未找到终点参数，使用默认终点');
  }

  // 如果有起点参数
  if (options.start_latitude && options.start_longitude) {
    console.log('发现起点参数:', options.start_latitude, options.start_longitude, options.start_name);
    startCoord.value = {
      latitude: parseFloat(options.start_latitude),
      longitude: parseFloat(options.start_longitude),
      name: options.start_name || '起点'
    };
    console.log('设置起点坐标:', startCoord.value);

    // 如果提供了起点和终点，则不需要获取当前位置
    routeStatus.value = '准备规划装货地到卸货地路线';
    return;
  } else {
    console.log('未找到起点参数，尝试获取当前位置');
    // 如果没有起点参数，则获取当前位置作为起点
    try {
      isLoading.value = true;
      routeStatus.value = '获取当前位置中...';
      const currentPos = await getCurrentPosition();
      startCoord.value = {
        latitude: currentPos.latitude,
        longitude: currentPos.longitude,
        name: '当前位置'
      };
      console.log('成功获取当前位置作为起点:', startCoord.value);
      routeStatus.value = '准备规划当前位置到装货地路线';
    } catch (error) {
      console.error('获取当前位置失败，使用默认起点', error);
      // 使用默认起点（保持原有值）
      console.log('使用默认起点:', startCoord.value);
      routeStatus.value = '获取当前位置失败，使用默认起点';
    } finally {
      isLoading.value = false;
    }
  }

  console.log('路由参数解析完成，起点:', startCoord.value, '终点:', endCoord.value);
};

// 监听路线和标记点的变化，动态调整视野
watch([polyline, markers], () => {
  if (mapInitialized.value && mapContext.value) {
    calculateViewSettings();
    updateIncludePoints();
    adjustMapView();
  }
}, { deep: true });

// 生命周期钩子
onMounted(() => {
  console.log('组件挂载 onMounted 被调用');
  // 如果没有通过 onLoad 初始化，则在这里初始化
  if (!mapInitialized.value) {
    console.log('地图未初始化，在 onMounted 中初始化');
    // 获取当前页面参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage ? currentPage.$page?.options : {};
    console.log('从当前页面获取的参数:', options);

    // 解析路由参数并初始化地图
    parseRouteParams(options).then(() => {
      initMap();
    });
  }
});

onUnmounted(() => {
  // 清理工作
  mapContext.value = null;
  mapInitialized.value = false;
});

// 页面加载
const onLoad = async (options: any) => {
  console.log('页面加载 onLoad 函数被调用，参数:', options);

  // 解析路由参数
  await parseRouteParams(options);

  // 初始化地图
  initMap();
};

// 开始导航功能
const startNavigation = () => {
  console.log('开始导航，起点:', startCoord.value, '终点:', endCoord.value);

  // 检查是否有有效的起点和终点
  if (!startCoord.value.latitude || !startCoord.value.longitude ||
    !endCoord.value.latitude || !endCoord.value.longitude) {
    uni.showToast({
      title: '路线信息不完整，无法开始导航',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 构建高德地图导航URL Scheme
  // 格式：amapuri://route/plan/?sid=&slat=&slon=&sname=&did=&dlat=&dlon=&dname=&dev=0&t=0
  const startLat = startCoord.value.latitude;
  const startLon = startCoord.value.longitude;
  const startName = encodeURIComponent(startCoord.value.name || '起点');
  const endLat = endCoord.value.latitude;
  const endLon = endCoord.value.longitude;
  const endName = encodeURIComponent(endCoord.value.name || '终点');

  // 高德地图URL Scheme
  const amapUrl = `amapuri://route/plan/?slat=${startLat}&slon=${startLon}&sname=${startName}&dlat=${endLat}&dlon=${endLon}&dname=${endName}&dev=0&t=0`;

  console.log('高德地图导航URL:', amapUrl);

  // 直接跳转到高德地图
  // #ifdef APP-PLUS
  plus.runtime.openURL(amapUrl, (error) => {
    console.error('打开高德地图失败:', error);
    // 如果打开失败，尝试打开应用商店下载高德地图
    uni.showModal({
      title: '提示',
      content: '未检测到高德地图APP，是否前往下载？',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 跳转到应用商店
          const downloadUrl = uni.getSystemInfoSync().platform === 'ios'
            ? 'https://apps.apple.com/cn/app/id461703208'
            : 'https://www.amap.com/';
          plus.runtime.openURL(downloadUrl);
        }
      }
    });
  });
  // #endif

  // #ifdef H5
  window.location.href = amapUrl;
  // #endif

  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
  // 小程序环境下复制链接到剪贴板
  uni.setClipboardData({
    data: amapUrl,
    success: () => {
      uni.showToast({
        title: '导航链接已复制，请在高德地图中打开',
        icon: 'none',
        duration: 3000
      });
    }
  });
  // #endif
};

// 返回上一页功能
const goBack = () => {
  console.log('返回上一页');

  // 获取页面栈
  const pages = getCurrentPages();

  if (pages.length > 1) {
    // 如果有上一页，则返回
    uni.navigateBack({
      delta: 1,
      success: () => {
        console.log('成功返回上一页');
      },
      fail: (error) => {
        console.error('返回上一页失败:', error);
        uni.showToast({
          title: '返回失败',
          icon: 'none'
        });
      }
    });
  } else {
    // 如果没有上一页，显示提示或跳转到首页
    uni.showModal({
      title: '提示',
      content: '当前已是首页，是否退出应用？',
      success: (res) => {
        if (res.confirm) {
          // #ifdef APP-PLUS
          plus.runtime.quit();
          // #endif

          // #ifndef APP-PLUS
          uni.showToast({
            title: '无法退出应用',
            icon: 'none'
          });
          // #endif
        }
      }
    });
  }
};

// 导出页面生命周期函数
defineExpose({
  onLoad
});
</script>

<style lang="scss">
/* 全局样式，确保在APP端也能正确显示 */
.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  box-sizing: border-box;
}

.map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* 地图内部导航按钮区域 */
.map-navigation-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 200rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* 导航背景板 */
.navigation-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: rgba(255, 255, 255, 1);
  border-radius: 40rpx 40rpx 0 0;
  z-index: 1;
}

/* 背景内容 */
.background-content {
  width: 100%;
  height: 100%;
  opacity: 0;
}

/* APP端特殊处理 */
/* #ifdef APP-PLUS */
.navigation-background {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.3);
}

/* #endif */

/* 按钮容器 */
.buttons-container {
  position: relative;
  z-index: 2;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}

/* cover-view 导航按钮样式 */
.nav-btn-cover {
  flex: 1;
  height: 100rpx;
  background: rgba(79, 172, 254, 0.9);
  border-radius: 50rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 开始导航按钮特殊样式 */
.start-nav-btn-cover {
  background: rgba(255, 107, 107, 0.9);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
}

/* 返回按钮样式 */
.back-btn-cover {
  background: rgba(116, 185, 255, 0.9);
  box-shadow: 0 8rpx 24rpx rgba(116, 185, 255, 0.4);
}

/* cover-view 按钮图标 */
.btn-icon-cover {
  font-size: 36rpx;
  line-height: 1;
  margin-right: 12rpx;
  pointer-events: none;
  /* 让点击事件穿透到父元素 */
}

/* cover-view 按钮文字 */
.btn-text-cover {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  pointer-events: none;
  /* 让点击事件穿透到父元素 */
}

/* cover-view 按钮点击效果 */
.nav-btn-cover:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .map-navigation-buttons {
    height: 180rpx;
  }

  .navigation-background {
    height: 180rpx;
    border-radius: 30rpx 30rpx 0 0;
  }

  .buttons-container {
    padding: 20rpx;
    gap: 16rpx;
  }

  .nav-btn-cover {
    height: 88rpx;
    border-radius: 44rpx;
  }

  .btn-icon-cover {
    font-size: 32rpx;
    margin-right: 10rpx;
  }

  .btn-text-cover {
    font-size: 26rpx;
  }
}

/* 地图加载出错时的显示 */
.map-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  z-index: 2;

  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .error-icon {
      font-size: 70rpx;
      width: 100rpx;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      background: #ff5a5a;
      color: #fff;
      border-radius: 50%;
      margin-bottom: 20rpx;
    }

    .error-text {
      font-size: 32rpx;
      color: #666;
      margin-bottom: 40rpx;
    }

    .retry-btn {
      background: #3c9cff;
      color: #fff;
      font-size: 28rpx;
      padding: 10rpx 40rpx;
      border-radius: 40rpx;
    }
  }
}

/* 加载遮罩 */
.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 5;

  .loading-content {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 20rpx 40rpx;
    border-radius: 10rpx;

    .loading-text {
      color: #FFFFFF;
      font-size: 28rpx;
    }
  }
}

/* 安全区域适配 */
/* #ifdef APP-PLUS */
.map-navigation-buttons {
  height: calc(200rpx + env(safe-area-inset-bottom));
}

.navigation-background {
  height: calc(200rpx + env(safe-area-inset-bottom));
}

.buttons-container {
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

/* #endif */
</style>

<!-- 添加全局样式，不使用scoped，确保样式能正确应用到cover-view -->
<style>
/* APP端特殊处理 */
/* #ifdef APP-PLUS */
/* 移除路线按钮APP特殊样式 */
/* .route-btn {
  background-color: #ffffff !important;
} */
/* #endif */
</style>
