<template>
  <view class="message-page">
    <!-- 顶部栏 -->
    <view class="header-container">
      <!-- 主标题栏 -->
      <view class="header" :style="{ paddingTop: statusBarHeight * 2 + 'rpx' }">
        <view class="back-icon" @tap="goBack">
          <text class="iconfont icon-owner-a-fanhuizu<PERSON>jiantou"></text>
        </view>
        <!-- 使用按钮形式的tabs -->
        <view class="tabs">
          <view class="tab-btn" :class="{ active: activeTab === 0 }" @tap="switchTab(0)">货源消息</view>
          <view class="tab-btn" :class="{ active: activeTab === 1 }" @tap="switchTab(1)">系统通知</view>
        </view>
        <view class="clear-btn" @tap="clearUnread">
          <text class="iconfont icon-owner-shanchu"></text>
          <text class="text">清空未读</text>
        </view>
      </view>

      <!-- 通知提示条 -->
      <view class="notification-info" v-if="!notificationEnabled">
        <view class="info-icon">
          <text class="iconfont icon-owner-tishi"></text>
        </view>
        <view class="info-text">未开启系统通知，App外无法听单找货</view>
        <view class="info-arrow">
          <text class="iconfont icon-owner-youjiantou"></text>
        </view>
      </view>
    </view>

    <!-- 使用z-paging和z-tabs集成 -->
    <z-paging ref="pagingRef" v-model="dataList" @query="handlePagingQuery" :fixed="false" :show-scrollbar="false" :auto-show-back-to-top="true" :use-page-scroll="true" auto-show-system-loading cell-height-mode="dynamic" :loading-more-enabled="true" :show-loading-more-no-more-view="true" :show-empty-view="true" :empty-view-text="'暂无新货'" :style="{ paddingTop: topBarTotalHeight + 'rpx' }">
      <template #top>
        <!-- 子标签页 -->
        <view v-if="activeTab === 0" class="sub-tabs-container">
          <z-tabs v-model="activeCategory" :list="categories" :show-bar="true" :fixed="false" style-inactive-color="#666" style-active-color="#ff4d4f" style-bar-color="#ff4d4f" @change="onCategoryChange" class="sub-tabs" />
        </view>
      </template>

      <template #default>
        <!-- 货源消息内容 -->
        <template v-if="activeTab === 0">
          <view class="message-item" v-for="(item, index) in dataList" :key="index">
            <!-- 货源信息 -->
            <view class="source-info">
              <view class="route">
                <text class="city">{{ item.fromCity }}</text>
                <text class="city-divider">{{ item.fromDistinct }}</text>
                <text class="arrow">→</text>
                <text class="city">{{ item.toCity }}</text>
                <text class="city-divider">{{ item.toDistinct }}</text>
                <text class="tag" :class="item.tagType">{{ item.tag }}</text>
              </view>
              <view class="price">{{ item.price }}元/趟</view>
            </view>
            <!-- 货物信息和按钮放在同一行 -->
            <view class="cargo-container">
              <view class="cargo-info-wrapper">
                <view class="cargo-info">
                  <text>{{ item.cargoType }}</text>
                  <text>{{ item.loadTime }}</text>
                  <text>{{ item.loadUnloadInfo }}</text>
                </view>
                <view class="cargo-details">
                  {{ item.cargoSize }}
                </view>
              </view>
              <!-- 操作按钮 -->
              <view class="action-btn">
                <button class="grab-btn" @tap="grabOrder(item)">去抢单</button>
              </view>
            </view>
          </view>
        </template>

        <!-- 系统通知内容 -->
        <template v-else>
          <view class="notification-list">
            <view class="notification-item" v-for="(item, index) in dataList" :key="index" @tap="navigateToDetail(item)">
              <view class="notification-icon" :class="'bg-' + item.type">
                <text class="iconfont" :class="'icon-' + item.icon"></text>
              </view>
              <view class="notification-content">
                <view class="notification-title">{{ item.title }}</view>
                <view class="notification-message">{{ item.message }}</view>
              </view>
              <view class="notification-time" v-if="item.time">{{ item.time }}</view>
            </view>
          </view>
        </template>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
// @ts-ignore
import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js';

// 系统状态栏高度
const statusBarHeight = ref();
const headerHeight = 44; // 顶部栏高度（rpx单位）
const notificationHeight = 44; // 通知栏高度（rpx单位）

// 计算顶部总高度，用于z-paging的paddingTop
const topBarTotalHeight = computed(() => {
  if (activeTab.value === 0) {
    // 状态栏高度 + 主标题栏高度 + (是否显示通知栏 ? 通知栏高度 : 0)
    return statusBarHeight.value * 2 + headerHeight * 2 + (!notificationEnabled.value ? notificationHeight * 2 : 0);
  } else {
    return statusBarHeight.value * 2;
  }
});

// 状态管理
const activeTab = ref(0);
const activeCategory = ref('all');
const notificationEnabled = ref(false);
const pagingRef = ref<any>(null);

// 列表数据
const dataList = ref<any[]>([]);

// 分类数据 - 为z-tabs格式化
const categories = [
  { name: '全部', value: 'all' },
  { name: '订阅播报', value: 'subscribe' },
  { name: '推荐播报', value: 'recommend' },
  { name: '重要通知', value: 'important' }
];

// 获取系统信息
onMounted(() => {
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight || 0;
});

// 切换主标签
const switchTab = (index: number) => {
  activeTab.value = index;
  setTimeout(() => {
    if (pagingRef.value) {
      pagingRef.value.reload();
    }
  }, 100);
};

watch(() => activeTab.value, (newVal) => {
  if (newVal === 1) {
    notificationEnabled.value = true;
  } else {
    notificationEnabled.value = false;
  }
});

// 切换分类
const onCategoryChange = () => {
  // z-tabs会自动更新activeCategory的值
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 清空未读
const clearUnread = () => {
  uni.showToast({
    title: '已清空未读消息',
    icon: 'none'
  });
};

// 模拟异步分页请求
const queryData = async (page: number, pageSize: number) => {
  return new Promise<any[]>((resolve) => {
    setTimeout(() => {
      if (activeTab.value === 0) {
        // 货源消息
        let newData = Array.from({ length: pageSize }, (_, index) => {
          // 创建基础数据
          const item = {
            id: `msg_${Date.now()}_${index}`,
            fromCity: '北京',
            fromDistinct: '通州',
            toCity: '北京',
            toDistinct: '顺义',
            tagType: 'direct',
            tag: '一口价',
            price: '*****',
            cargoType: '食品五谷 纸箱',
            loadTime: '今天6-12点装货',
            loadUnloadInfo: '一装一卸',
            cargoSize: '5/6.2/6.8米 | 平板/高栏/厢式 | 5~7吨 | 2...'
          };

          // 添加更多样式的示例
          if (index % 3 === 1) {
            item.fromCity = '北京';
            item.fromDistinct = '朝阳';
            item.toCity = '北京';
            item.toDistinct = '通州';
            item.tagType = 'carpool';
            item.tag = '拼车';
          }

          return item;
        });

        // 模拟最后一页
        if (page > 3) newData = [];
        resolve(newData);
      } else {
        // 系统通知
        let newData = [
          {
            id: 'sys_todo',
            type: 'todo',
            icon: 'owner-file',
            title: '我的待办',
            message: '暂无您需关注的消息'
          },
          {
            id: 'sys_payment',
            type: 'payment',
            icon: 'owner-money',
            title: '钱款变动',
            message: '暂无您需关注的消息'
          },
          {
            id: 'sys_service',
            type: 'service',
            icon: 'owner-service',
            title: '在线客服',
            message: '暂无您需关注的消息'
          },
          {
            id: 'sys_marketing',
            type: 'marketing',
            icon: 'owner-mail',
            title: '营销活动',
            message: '油价大降！来领限时加油券！⏰',
            time: '10:06'
          },
          {
            id: 'sys_notice',
            type: 'notice',
            icon: 'owner-notify',
            title: '系统消息',
            message: '暂无您需关注的消息'
          },
          {
            id: 'sys_finance',
            type: 'finance',
            icon: 'owner-insurance',
            title: '金融保险',
            message: '暂无您需关注的消息'
          },
          {
            id: 'sys_etc',
            type: 'etc',
            icon: 'owner-etc',
            title: 'ETC消息',
            message: '暂无您需关注的消息'
          }
        ];

        // 只返回第一页有数据，其他页为空
        if (page > 1) newData = [];
        resolve(newData);
      }
    }, 1000);
  });
};

// 查询消息列表（统一处理货源消息和系统通知）
const handlePagingQuery = async (page: number, pageSize: number) => {
  console.log('查询数据', activeTab.value, activeCategory.value, page, pageSize);
  try {
    const data = await queryData(page, pageSize);
    console.log('data', data);
    pagingRef.value.complete(data);
  } catch (error) {
    console.error('查询数据出错', error);
    pagingRef.value.complete(false);
  }
};

// 抢单
const grabOrder = (item: any) => {
  uni.showToast({
    title: '抢单成功',
    icon: 'success'
  });
};

// 跳转到通知详情
const navigateToDetail = (item: any) => {
  uni.showToast({
    title: `点击了${item.title}`,
    icon: 'none'
  });
};

// 使用useZPaging钩子
useZPaging(pagingRef);
</script>

<style lang="scss" scoped>
.message-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.header {
  height: 88rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;

  .back-icon {
    width: 60rpx;

    .iconfont {
      font-size: 40rpx;
      color: #333;
    }
  }

  .tabs {
    display: flex;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: #f5f5f5;
    border-radius: 8rpx;
    padding: 4rpx;

    .tab-btn {
      padding: 10rpx 32rpx;
      font-size: 28rpx;
      color: #666;
      border-radius: 6rpx;
      transition: all 0.3s;

      &.active {
        color: #ff4d4f;
        background-color: #fff;
        font-weight: 500;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }
    }
  }

  .clear-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;
    min-width: 80rpx;

    .iconfont {
      font-size: 40rpx;
      margin-bottom: 4rpx;
      line-height: 1;
    }

    .text {
      font-size: 20rpx;
      line-height: 1;
    }
  }
}

.notification-info {
  width: 100%;
  height: 88rpx;
  background-color: #fff9f0;
  display: flex;
  align-items: center;
  padding: 0 30rpx;

  .info-icon {
    margin-right: 20rpx;

    .iconfont {
      font-size: 36rpx;
      color: #fa8c16;
    }
  }

  .info-text {
    flex: 1;
    font-size: 28rpx;
    color: #666;
  }

  .info-arrow {
    .iconfont {
      font-size: 32rpx;
      color: #999;
    }
  }
}

:deep(.zp-page-top) {
  top: v-bind(topBarTotalHeight)+'rpx' !important;
}

// :deep(.zp-view-super) {
//   margin-top: v-bind(listTop)+'rpx' !important;
// }

.sub-tabs-container {
  background-color: #fff;
  border-bottom: 2rpx solid #f0f0f0;

  .sub-tabs {
    height: 80rpx;
  }
}

.message-item {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .source-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .route {
      display: flex;
      align-items: center;
      flex: 1;
      flex-wrap: wrap;

      .city {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      .city-divider {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-right: 20rpx;
      }

      .arrow {
        margin: 0 20rpx;
        color: #999;
      }

      .tag {
        padding: 4rpx 12rpx;
        font-size: 24rpx;
        border-radius: 4rpx;
        margin-left: 16rpx;

        &.carpool {
          color: #52c41a;
          background-color: #f6ffed;
          border: 2rpx solid #b7eb8f;
        }

        &.direct {
          color: #ff4d4f;
          background-color: #fff1f0;
          border: 2rpx solid #ffa39e;
        }
      }
    }

    .price {
      font-size: 32rpx;
      color: #ff4d4f;
      font-weight: 500;
      text-align: right;
      min-width: 140rpx;
    }
  }

  .cargo-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .cargo-info-wrapper {
    flex: 1;
    margin-right: 20rpx;
    overflow: hidden;
  }

  .cargo-info {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
    font-size: 26rpx;
    color: #666;
    white-space: nowrap;
    overflow: hidden;

    text {
      margin-right: 20rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 30%;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .cargo-details {
    font-size: 26rpx;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .action-btn {
    align-self: center;
    margin-left: 10rpx;

    .grab-btn {
      display: inline-block;
      font-size: 28rpx;
      color: #fff;
      background-color: #ff4d4f;
      border-radius: 8rpx;
      border: none;
      white-space: nowrap;
      min-width: 120rpx;
    }
  }
}

.notification-list {
  background-color: #fff;

  .notification-item {
    display: flex;
    align-items: flex-start;
    padding: 30rpx;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      left: 30rpx;
      right: 30rpx;
      bottom: 0;
      height: 1rpx;
      background-color: #f0f0f0;
    }

    .notification-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 24rpx;
      flex-shrink: 0;

      .iconfont {
        font-size: 40rpx;
        color: #fff;
      }
    }

    .notification-content {
      flex: 1;
      overflow: hidden;

      .notification-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 10rpx;
      }

      .notification-message {
        font-size: 28rpx;
        color: #999;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    .notification-time {
      font-size: 26rpx;
      color: #999;
      margin-left: 20rpx;
      flex-shrink: 0;
    }
  }
}

// 图标背景颜色
.bg-todo {
  background-color: #4cd964;
}

.bg-payment {
  background-color: #ffcc33;
}

.bg-service {
  background-color: #ff7733;
}

.bg-marketing {
  background-color: #ff6666;
}

.bg-notice {
  background-color: #ff6666;
}

.bg-finance {
  background-color: #33ccaa;
}

.bg-etc {
  background-color: #33ccaa;
}
</style>
