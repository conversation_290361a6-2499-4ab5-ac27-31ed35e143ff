<template>
  <view class="find-record-page">
    <!-- 顶部栏 -->
    <view class="header-container">
      <!-- 主标题栏 -->
      <view class="header" :style="{ paddingTop: statusBarHeight * 2 + 'rpx' }">
        <view class="back-icon" @tap="goBack">
          <text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
        </view>
        <view class="title">找货记录</view>
      </view>

      <!-- 筛选标签区域 -->
      <view class="filter-container">
        <view class="filter-label">筛选:</view>
        <scroll-view class="tag-scroll-view" scroll-x :show-scrollbar="false" enhanced @scroll="onScroll">
          <view class="tag-list">
            <view v-for="(tag, index) in filterTags" :key="index" class="tag-item" :class="{ 'tag-active': selectedTags.includes(tag.value) }" @tap="toggleTag(tag.value)">
              {{ tag.label }}
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 使用z-paging组件实现列表 -->
    <z-paging ref="pagingRef" v-model="dataList" @query="queryFreightList" :fixed="false" :show-scrollbar="false" :auto-show-back-to-top="true" :use-page-scroll="true" auto-show-system-loading cell-height-mode="dynamic" :loading-more-enabled="true" :show-loading-more-no-more-view="true" :show-empty-view="true" :empty-view-text="'暂无找货记录'" :style="{ paddingTop: topBarTotalHeight + 'rpx' }">
      <template #default>
        <!-- 货源列表 -->
        <view class="freight-list">
          <view class="freight-item" v-for="(item, index) in dataList" :key="index">
            <view class="freight-item-container">
              <view class="goods-title">
                <text class="from">{{ item.from }}</text>
                <text class="arrow">→</text>
                <text class="to">{{ item.to }}</text>
                <text class="type" :class="item.carpool ? 'carpool' : 'fixed'">{{ item.type }}</text>
                <text v-if="!item.carpool" class="tag blue">满运宝</text>
                <text v-if="!item.carpool" class="tag blue">企业</text>
              </view>
              <view class="goods-desc">{{ item.desc }}</view>
              <view class="goods-info">{{ item.info }}</view>
              <view class="goods-footer">
                <view class="credit"><u-icon name="account" size="18"></u-icon>信用 {{ item.credit }}星 交易{{ item.deals }}</view>
                <view class="price-action">
                  <text class="price">*****元/趟</text>
                  <u-button type="error" size="mini" shape="circle" text="去抢单"></u-button>
                </view>
              </view>
            </view>
            <view class="freight-item-bottom">
              <view class="freight-item-bottom-left">
                <text class="freight-item-bottom-left-text">刚刚 浏览</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
// @ts-ignore
import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js';

// 系统状态栏高度
const statusBarHeight = ref(0);
const headerHeight = 88; // 顶部栏高度（rpx单位）
const filterHeight = 100; // 筛选栏高度（rpx单位）

// 计算顶部总高度，用于内容区域的paddingTop
const topBarTotalHeight = computed(() => {
  return statusBarHeight.value * 2 + headerHeight + filterHeight;
});

// 定义筛选标签
const filterTags = [
  { label: '货主已加价', value: 'ownerPriceUp' },
  { label: '我已加价', value: 'myPriceUp' },
  { label: '秒抢好货', value: 'flashGood' },
  { label: '货源', value: 'goodsSource' },
  { label: '距离最近', value: 'nearestDistance' },
  { label: '价格最高', value: 'highestPrice' },
  { label: '最新发布', value: 'latestPublish' },
  { label: '热门货源', value: 'hotGoods' },
  { label: '长途运输', value: 'longDistance' },
  { label: '短途运输', value: 'shortDistance' },
  { label: '特殊货物', value: 'specialGoods' },
  { label: '紧急配送', value: 'urgentDelivery' },
];

// 选中的标签
const selectedTags = ref<string[]>([]);
// 数据列表
const dataList = ref<any[]>([]);
// z-paging组件引用
const pagingRef = ref<any>(null);

// 获取系统信息
onMounted(() => {
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight || 0;
});

// 切换标签选中状态
const toggleTag = (tagValue: string) => {
  const index = selectedTags.value.indexOf(tagValue);
  if (index > -1) {
    selectedTags.value.splice(index, 1);
  } else {
    selectedTags.value.push(tagValue);
  }

  // 标签变化后重新加载数据
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

// 查询货源列表
const queryFreightList = async (pageNo: number, pageSize: number) => {
  console.log('查询货源列表', selectedTags.value, pageNo, pageSize);

  // 模拟异步请求
  const data = await queryList(pageNo, pageSize)
  pagingRef.value.complete(data)
};

// 模拟异步分页请求
const queryList = async (page: number, pageSize: number) => {
  return new Promise<any[]>((resolve) => {
    setTimeout(() => {
      let newData = Array.from({ length: pageSize }, (_, i) => ({
        id: (page - 1) * pageSize + i + 1,
        from: '北京 昌平',
        to: '张家口 张北',
        type: i % 2 === 0 ? '拼车' : '一口价',
        carpool: i % 2 === 0,
        desc: i % 2 === 0 ? '电动车 裸装 今天装货' : '电子产品及配件 托盘 明天8-9点装货...',
        info: i % 2 === 0 ? '2.7米及以上 | 面包车 | 1.5方' : '2.7/3.8米 | 厢式/高栏/平板 | 0.7吨 | 3.3方',
        credit: '4.5',
        deals: i % 2 === 0 ? '882' : '86',
      }))
      // 模拟最后一页
      if (page > 3) newData = []
      resolve(newData)
    }, 2000)
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 滚动事件处理
const onScroll = (e: any) => {
  // 可以根据需要处理滚动事件
};

// 抢单
const grabOrder = (item: any) => {
  console.log('抢单', item);
  uni.showToast({
    title: '抢单成功',
    icon: 'success'
  });
};

// 使用useZPaging钩子
useZPaging(pagingRef);
</script>

<style lang="scss" scoped>
.find-record-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
}

.header {
  height: 88rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 30rpx;
  position: relative;

  .back-icon {
    position: absolute;
    left: 30rpx;

    .iconfont {
      font-size: 40rpx;
      color: #333;
    }
  }

  .title {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.filter-container {
  height: 100rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 0 30rpx;
  border-bottom: 1px solid #f0f0f0;

  .filter-label {
    font-size: 28rpx;
    color: #333;
    margin-right: 20rpx;
    flex-shrink: 0;
  }

  .tag-scroll-view {
    flex: 1;
    white-space: nowrap;

    /* 隐藏滚动条 */
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
    }
  }

  .tag-list {
    display: inline-flex;
    padding-right: 30rpx;
    /* 确保最后一个标签完全可见 */

    .tag-item {
      display: inline-block;
      padding: 10rpx 24rpx;
      margin-right: 20rpx;
      font-size: 26rpx;
      color: #666;
      background-color: #f7f7f7;
      border-radius: 30rpx;
      transition: all 0.2s ease;

      &:last-child {
        margin-right: 30rpx;
        /* 确保最后一个标签有足够的右边距 */
      }

      &.tag-active {
        color: #fff;
        background-color: #2979ff;
      }
    }
  }
}

/* 货源列表 */
.freight-list {
  padding: 20rpx;
}

.freight-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 24rpx;
}

.freight-item-container {
  .goods-title {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: bold;
    color: #333;

    .arrow {
      margin: 0 12rpx;
      color: #999;
    }

    .type {
      margin-left: 12rpx;
      font-size: 24rpx;
      border-radius: 6rpx;
      padding: 2rpx 12rpx;

      &.carpool {
        background: #e8f5e9;
        color: #7ac251;
      }

      &.fixed {
        background: #fff2f0;
        color: #e84138;
      }
    }

    .tag {
      margin-left: 8rpx;
      font-size: 20rpx;
      background: #e6f0ff;
      color: #3a7afe;
      border-radius: 4rpx;
      padding: 2rpx 8rpx;

      &.blue {
        background: #e6f0ff;
        color: #3a7afe;
      }
    }
  }

  .goods-desc {
    font-size: 26rpx;
    color: #666;
    margin: 12rpx 0 0 0;
  }

  .goods-info {
    font-size: 24rpx;
    color: #999;
    margin: 8rpx 0 0 0;
  }

  .goods-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 18rpx;

    .credit {
      font-size: 22rpx;
      color: #999;
      display: flex;
      align-items: center;

      u-icon {
        margin-right: 4rpx;
      }
    }

    .price-action {
      display: flex;
      align-items: center;

      .price {
        color: #e84138;
        font-size: 28rpx;
        font-weight: bold;
        margin-right: 12rpx;
      }
    }
  }
}

.freight-item-bottom {
  border-top: 1px solid #f0f0f0;
  padding-top: 12rpx;

  .freight-item-bottom-left {
    .freight-item-bottom-left-text {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>