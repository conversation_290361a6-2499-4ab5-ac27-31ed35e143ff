<template>
  <view class="message-page">
		<view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn>取消</template>
			<template #title>允许配货站查看订单的范围</template>
			<template #rightBtn>
				<u-button class="btn-confirm" @click="confirm">完成</u-button>
			</template>
		</cu-header>
    <view class="main-container">
      <view class="message-box">
        <view class="days-item" v-for="item in dayList" :key="item.id" @click="chooseItem(item)">
          <view class="message-text">{{ item.label }}</view>
          <view v-if="item.choose" class="iconfont icon-owner-duigou"></view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref, reactive} from 'vue';
import {updateTeamDataScope} from "../../api/userCenter";
const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const permissionDays = ref('')
permissionDays.value = "最近三天"
const dayList = ref([
  {
    id:1,
    label: '全部',
    scope: 1,
    choose: false
  },
  {
    id:2,
    label: '最近半年',
    scope: 2,
    choose: false
  },
  {
    id:3,
    label: '最近一个月',
    scope: 3,
    choose: false
  },
  {
    id:4,
    label: '最近三天',
    scope: 4,
    choose: true
  }
])
// 点击某一条
const chooseItem = (item:any) => {
  dayList.value.forEach((child:any) => {
    child.choose = false
  })
  item.choose = true
}
// 点击完成
const confirm = async() => {
   const item = dayList.value.filter((item) =>item.choose===true)
   if(item.length>0){
      try {
        const response:any = await updateTeamDataScope({"dataScope":item[0].dataScope});
        uni.hideLoading();
        if (response.code === 700) {
               uni.showToast({
                title: '修改成功',
                icon: 'none',
                duration: 2000
              });
        } else {
          console.error('拒绝失败:', response.code);
        }
      } catch (error) {
        console.error('拒绝失败:', error);
      }
   }else{
      uni.showToast({
        title: '请勾选！',
        icon: 'none',
        duration: 2000
      });
   }

}
</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
}
.main-container {
  width: 100%;
	position: relative;
	z-index: 1;
	padding-top: v-bind(heightHeader);
  .message-box {
    width: 100%;
    background-color: #fff;
  }
  .days-item {
    width: 90%;
    padding:0 5%;
    height: 80rpx;
    line-height: 80rpx;
    border-bottom: 1px solid #e6e1e1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .iconfont {
      font-size: 40rpx;
      font-weight: 600;
      color: #67c23a;
    }
  }
}
:deep(.btn-confirm) {
  background-color: #67c23a;
  color: #fff;
}

</style>