<template>
  <view class="message-page">
		<view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-fanhuizu<PERSON>jiant<PERSON>"></text></template>
			<template #title>车队权限管理</template>
			<template #rightBtn>
				{{ '' }}
			</template>
		</cu-header>
    <view class="main-container">
      <view class="message-box">
        <view class="message-text">允许配货站查看订单的范围</view>
        <view>{{ permissionDays }}</view>
        <view class="iconfont icon-owner-youjiantou" @click="toSet"></view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref, reactive} from 'vue';
const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const permissionDays = ref('')
permissionDays.value = "最近三天"
const toSet = () => {
  uni.redirectTo({
    url: '/pageComDriver/my/setCheckDays'
  })
  
}
</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
}
.main-container {
  width: 100%;
	position: relative;
	z-index: 1;
	padding-top: v-bind(heightHeader);
  .message-box {
    width: 90%;
    padding:0 5%;
    height: 120rpx;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

</style>