<template>
  <!-- <div>司机端-我的车队</div> -->
  <view class="car-team-page">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text></template>
			<template #title>
        <text>我加入的车队</text>
      </template>
			<template #rightBtn>
				<text class="iconfont icon-owner-shezhi" @click="setPage"></text>
			</template>
		</cu-header>
    <view class="main-container">
      <view class="tip-content">
        <text class="welcome-text">欢迎使用 车队系统</text>
        <view class="item-con" v-for="item in textList" :key="item.id">
          <view class="iconfont icon-owner-duigouxiao"></view>
          <view class="con-text">{{ item.content }}</view>
        </view>
      </view>
      <view class="invite-content">
        <view>有{{ num }}个配货站邀请您加入车队</view>
        <view class="team-list">
          <view class="team-item" v-for="item in inviteList" :key="item.id">
            <view class="team-img">
              <image :src="item.avatarUrl"></image>
            </view>
            <view class="info-con">
              <view class="info-name">{{ item.teamName }}</view>
              <view class="info-phone">{{item.teamLeaderMobile}}</view>
            </view>
            <view class="btns">
              <u-button class="btn1" @click="refuseBtn(item)">拒绝</u-button>
              <u-button class="btn2" @click="joinBtn(item)">加入</u-button>
            </view>
          </view>
        </view>
      </view>
      <view class="invite-content join-content">
        <view>已加入的车队</view>
        <view>
          <uni-swipe-action class="team-list">
            <uni-swipe-action-item v-for="item in joinedList" :key="item.id" :right-options="options" :show="item.show" @click="clickOption">
              <view class="team-img">
                <image :src="item.avatarUrl"></image>
              </view>
              <view class="info-con">
                <view class="info-name">{{ item.teamName }}</view>
                <view class="info-phone">{{item.teamLeaderMobile}}</view>
              </view>
              <view class="btns">
                <u-button class="btn1" @click="callBtn(item)">打电话</u-button>
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>
        </view>
      </view>
    </view>
    <!-- 退出车队弹窗 -->
    <uni-popup ref="exitTeamRef" background-color="#fff" class="global-popup exit-team-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="content-text">
          <view class="text">退出车队后，配货站将无权查看您的订单和位置</view>
        </view>
        <view class="confirm-btn">
          <button class="btn-com" @click="cancel">我在想想</button>
          <button class="btn-com confirm-btn" @click="confirm">确认退出</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref} from 'vue';
import {myJoinTeam,joinOrRefuseTeam,removeInvite,updateTeamDataScope} from "../../api/userCenter";

const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const carTeamName = ref("车队名称")
const textList = [
  {
    id:1,
    content: '您可联系配货站将您纳入车队管理'
  },
  {
    id:2,
    content: '加入车队后，配货站可查看您的位置和订单'
  },
  {
    id:3,
    content: '配货站将会增加您的收入，促进订单成交'
  }
]
const num = ref(10)
const inviteList = ref([])
const joinedList = ref([])
const options = ref([
  {
    text: `退出车队`,
    style: {
      backgroundColor: 'red',
      color: '#fff',
      fontSize: '26rpx'
    }
  }
])
// 点击设置
const setPage = () => {
  uni.redirectTo({
    url: '/pageComDriver/my/setPermission'
  })
} 
// 点击拒绝
const refuseBtn = async(data) => {
  try {
    const response:any = await joinOrRefuseTeam({"inviteId":data.id,"status":2});
    uni.hideLoading();
    if (response.code === 700) {
       initData();
    } else {
      console.error('拒绝失败:', response.code);
    }
  } catch (error) {
    console.error('拒绝失败:', error);
  }

}
// 点击加入
const joinBtn = async(data) => {
  try {
    const response:any = await joinOrRefuseTeam({"inviteId":data.id,"status":3});
    uni.hideLoading();
    if (response.code === 700) {
       initData();
    } else {
      console.error('加入失败:', response.code);
    }
  } catch (error) {
    console.error('加入失败:', error);
  }
}
const callBtn = (item:any) => {
  uni.makePhoneCall({phoneNumber:item.teamLeaderMobile})
}
// 点击退出车队-打开弹窗
const exitTeamRef = ref(null)
const clickIndex = ref(0)
const clickOption = (data:any) => {
  console.log('index', data)
  clickIndex.value = data.index
  exitTeamRef.value.open('bottom');
}
// 确认退出
const confirm = async () => {
   try {
      const response:any = await removeInvite({"id":joinedList.value[clickIndex.value].id});
      uni.hideLoading();
      if (response.code === 700) {
        initData();
        exitTeamRef.value.close();
      } else {
        console.error('加入失败:', response.code);
      }
  } catch (error) {
      console.error('加入失败:', error);
  }
}
// 我在想想
const cancel = () => {
  joinedList[clickIndex.value].show = false;
  exitTeamRef.value.close();
}

const initData = async () => {
  try {
    const response:any = await myJoinTeam();
    uni.hideLoading();
    if (response.code === 700) {
         inviteList.value = response.result.inviteList
         joinedList.value = response.result.hasJoinTeam

    } else {
      console.error('获取司机列表失败:', response.code);
    }
  } catch (error) {
    console.error('获取司机列表失败:', error);
  }
};

onMounted(() => {
	initData();
});
</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding-top: v-bind(heightHeader);
  .tip-content {
    background-color: #fff2f2;
    color: #333;
    padding: 20rpx;
    // height: 100rpx;
    .tip-text {
      padding-left: 20rpx;
    }
    .item-con {
      margin-top: 20rpx;
      padding-left: 20rpx;
      .iconfont {
        display: inline-block;
        color: #e58814;
        margin-right: 20rpx;
        font-size: 24rpx;
      }
      .con-text {
        display: inline-block;
        font-size: 24rpx;
      }
    }
    
  }
  .welcome-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
  .invite-content {
    padding: 20rpx;
    .team-list {
      .team-item {
        padding: 20rpx 0;
        display: flex;
        flex-direction: row;
        .team-img {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
          image {
            width: 80rpx;
            height: 80rpx;
            border-radius:40rpx;
            display: inline-block;
          }
        }
        .info-con {
          flex: 1;
          font-size: 28rpx;
          .info-phone {
            font-size: 24rpx;
          }
        }
        .btns{
          // width: 200rpx;
          :deep(.u-button) {
            display: inline-block;
            width: 100rpx;
            background-color: transparent;
            border: none;
          }
          :deep(.u-button:after){
            border: none;
          }
          .btn1 {
            color: #e58814;
            height: 60rpx;
            line-height: 60rpx;
            vertical-align: middle;
          }
          .btn2 {
            color: red;
            height: 60rpx;
            line-height: 60rpx;
            vertical-align: middle;
          }
        }
      }
      :deep(.uni-swipe_text--center){
        padding: 20rpx 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        .team-img {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
          image {
            width: 80rpx;
            height: 80rpx;
            border-radius:40rpx;
            display: inline-block;
          }
        }
        .info-con {
          flex: 1;
          font-size: 28rpx;
          .info-phone {
            font-size: 24rpx;
          }
        }
        .btns{
          .btn1 {
            display: inline-block;
            background-color: transparent;
            border: none;
            color: #e58814;
            height: 60rpx;
            line-height: 60rpx;
            vertical-align: middle;
          }
          .btn1:after {
            border: none;
          }
        }
      }
      :deep(.uni-swipe_button-group) {
        align-items: center;
      }
      :deep(.uni-swipe_button) {
        height: 60rpx;
        padding: 0 15rpx;
      }
    }
  }
}
:deep(.exit-team-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: 0;
    .popup-content {
      height: 300rpx;
      background-color: #fff;
      .content-text {
        padding: 40rpx;
        .text {
          text-align: center;
        }
      }
      .confirm-btn {
        width: 90%;
        margin-left: 5%;
        position: absolute;
        bottom: 20rpx;
        // height: 60rpx;
        line-height: 60rpx;
        .btn-com {
          display: inline-block;
          width: 40%;
          margin: 0 5%;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          background-color: #8d8585;
          color: #fff;
          border-radius: 5rpx;
        }
        .confirm-btn {
          background-color: #3c9cff;
          color: #fff;
        }
      }
    }
  }
}
</style>