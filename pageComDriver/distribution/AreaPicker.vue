<template>
  <view v-if="show" class="area-picker-mask">
    <view class="area-picker-popup">
      <view class="area-picker-header">
        <text class="area-picker-title">请选择{{ title }}区县</text>
        <u-icon name="close" size="22" @click="$emit('close')" />
      </view>
      <view v-if="selectedDistricts.length" class="area-picker-selected">
        <text>已选地区（最多选3个）</text>
        <view class="selected-list">
          <view v-for="(item, idx) in selectedDistricts" :key="item.code" class="selected-item">
            <text>{{ item.name }}</text>
            <text v-if="showCoordinates" class="coordinates-text">({{ item.coordinates.lat.toFixed(4) }}, {{ item.coordinates.lng.toFixed(4) }})</text>
            <text class="remove-btn" @click.stop="removeDistrict(idx)">×</text>
          </view>
        </view>
      </view>
      <view class="area-picker-search">
        <view class="search-input-wrap">
          <input class="area-picker-input" placeholder="输入省/市/区县名称搜索" v-model="searchText" />
          <u-icon v-if="searchText" name="close" size="20" class="input-clear" @click="searchText=''" />
        </view>
      </view>
      <view class="area-picker-tabs">
        <text :class="tabIndex===0?'active':''" @click="tabIndex=0">省份</text>
        <text :class="tabIndex===1?'active':''" @click="tabIndex=1" v-if="selectedProvince">城市</text>
        <text :class="tabIndex===2?'active':''" @click="tabIndex=2" v-if="selectedCity">区县</text>
        <view class="coordinates-toggle" @click="toggleCoordinatesDisplay">
          <text>{{ showCoordinates ? '隐藏坐标' : '显示坐标' }}</text>
        </view>
      </view>
      <view class="area-picker-list">
        <template v-if="tabIndex===0">
          <template v-if="filteredProvinces.length">
            <view v-for="province in filteredProvinces" :key="province.code" class="area-picker-item" :class="selectedProvince?.code===province.code?'selected':''" @click="selectProvince(province)">
              <span v-html="highlightKeyword(province.name)"></span>
            </view>
          </template>
          <view v-else class="no-result">无结果</view>
        </template>
        <template v-else-if="tabIndex===1">
          <template v-if="filteredCities.length">
            <view v-for="city in filteredCities" :key="city.code" class="area-picker-item" :class="selectedCity?.code===city.code?'selected':''" @click="selectCity(city)">
              <span v-html="highlightKeyword(city.name)"></span>
            </view>
          </template>
          <view v-else class="no-result">无结果</view>
        </template>
        <template v-else>
          <template v-if="filteredDistricts.length">
            <view v-for="district in filteredDistricts" :key="district.code" class="area-picker-item" :class="selectedDistricts.some(d=>d.code===district.code)?'selected':''" @click="toggleDistrict(district)">
              <span v-html="highlightKeyword(district.name)"></span>
            </view>
          </template>
          <view v-else class="no-result">无结果</view>
        </template>
      </view>
      <view class="area-picker-footer">
        <button class="area-picker-btn" :disabled="selectedDistricts.length===0" @click="confirm">下一步</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { areaData } from './area-data'
const props = defineProps({
  show: Boolean,
  title: String,
  modelValue: Array
})
const emit = defineEmits(['update:modelValue', 'confirm', 'close'])
const tabIndex = ref(0)
const searchText = ref('')
const selectedProvince = ref<any>(null)
const selectedCity = ref<any>(null)
const selectedDistricts = ref<any[]>([])
const showCoordinates = ref(false)

watch(() => props.show, (val) => {
  if (!val) {
    tabIndex.value = 0
    searchText.value = ''
    selectedProvince.value = null
    selectedCity.value = null
    selectedDistricts.value = []
  }
})

const filteredProvinces = computed(() => {
  if (!searchText.value) return areaData
  // 搜索省、市、区县
  const keyword = searchText.value.trim()
  return areaData
    .map(province => {
      // 省匹配
      if (province.name.includes(keyword)) return province
      // 市匹配
      const matchedCities = (province.cities || []).filter(city => city.name.includes(keyword))
      if (matchedCities.length) return { ...province, cities: matchedCities }
      // 区县匹配
      const matchedCitiesByDistrict = (province.cities || []).map(city => {
        const matchedDistricts = (city.districts || []).filter(d => d.name.includes(keyword))
        if (matchedDistricts.length) return { ...city, districts: matchedDistricts }
        return null
      }).filter(Boolean)
      if (matchedCitiesByDistrict.length) return { ...province, cities: matchedCitiesByDistrict }
      return null
    })
    .filter(Boolean)
})
const filteredCities = computed(() => {
  if (!selectedProvince.value) return []
  if (!searchText.value) return selectedProvince.value.cities
  const keyword = searchText.value.trim()
  // 市匹配
  return (selectedProvince.value.cities || [])
    .map(city => {
      if (city.name.includes(keyword)) return city
      const matchedDistricts = (city.districts || []).filter(d => d.name.includes(keyword))
      if (matchedDistricts.length) return { ...city, districts: matchedDistricts }
      return null
    })
    .filter(Boolean)
})
const filteredDistricts = computed(() => {
  if (!selectedCity.value) return []
  if (!searchText.value) return selectedCity.value.districts
  const keyword = searchText.value.trim()
  return (selectedCity.value.districts || []).filter(d => d.name.includes(keyword))
})

// 切换坐标显示
function toggleCoordinatesDisplay() {
  showCoordinates.value = !showCoordinates.value
}

function selectProvince(province: any) {
  selectedProvince.value = province
  selectedCity.value = null
  tabIndex.value = 1
}
function selectCity(city: any) {
  selectedCity.value = city
  tabIndex.value = 2
}
function toggleDistrict(district: any) {
  const idx = selectedDistricts.value.findIndex(d => d.code === district.code)
  if (idx > -1) {
    selectedDistricts.value.splice(idx, 1)
  } else {
    if (selectedDistricts.value.length >= 3) {
      uni.showToast({ title: '最多选择3个', icon: 'none' })
      return
    }
    selectedDistricts.value.push(district)
  }
}
function confirm() {
  emit('confirm', {
    province: selectedProvince.value,
    city: selectedCity.value,
    districts: selectedDistricts.value
  })
  emit('close')
}
function removeDistrict(idx: number) {
  selectedDistricts.value.splice(idx, 1)
}
function highlightKeyword(str: string) {
  if (!searchText.value) return str
  return str.replace(new RegExp(searchText.value, 'gi'), match => `<span style='color:#e84138;font-weight:bold;'>${match}</span>`)
}
</script>

<style scoped lang="scss">
.area-picker-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.area-picker-popup {
  width: 100vw;
  max-width: 750rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: 32rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.06);
}
.area-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx 0 24rpx;
}
.area-picker-title {
  font-size: 32rpx;
  font-weight: bold;
}
.area-picker-search {
  padding: 16rpx 24rpx;
  .search-input-wrap {
    position: relative;
    display: flex;
    align-items: center;
  }
}
.area-picker-input {
  width: 100%;
  height: 64rpx;
  border-radius: 12rpx;
  background: #f7f7f7;
  border: none;
  padding: 0 40rpx 0 20rpx;
  font-size: 28rpx;
}
.input-clear {
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #bbb;
  z-index: 2;
  cursor: pointer;
}
.area-picker-tabs {
  display: flex;
  padding: 0 24rpx;
  margin-top: 8rpx;
  gap: 24rpx;
  .active {
    color: #e84138;
    border-bottom: 4rpx solid #e84138;
    font-weight: bold;
  }
}
.area-picker-list {
  max-height: 400rpx;
  overflow-y: auto;
  padding: 16rpx 24rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.area-picker-item {
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  background: #f7f7f7;
  color: #222;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}
.area-picker-item.selected {
  background: #fff0f0;
  color: #e84138;
  border: 2rpx solid #e84138;
}
.area-picker-footer {
  padding: 24rpx;
  display: flex;
  justify-content: center;
}
.area-picker-btn {
  width: 100%;
  height: 80rpx;
  background: #e84138;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  font-weight: bold;
}
.area-picker-selected {
  padding: 0 24rpx 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  .selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-top: 8rpx;
  }
  .selected-item {
    display: flex;
    align-items: center;
    background: #fff0f0;
    color: #e84138;
    border: 1rpx solid #ffd6d6;
    border-radius: 12rpx;
    padding: 0 16rpx;
    height: 40rpx;
    font-size: 24rpx;
    margin-bottom: 8rpx;
  }
  .remove-btn {
    margin-left: 6rpx;
    font-size: 28rpx;
    color: #e84138;
    cursor: pointer;
  }
}
.no-result {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  padding: 32rpx 0;
}
.coordinates-toggle {
  margin-left: auto;
  color: #3a7afe;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f0f7ff;
  cursor: pointer;
}

.coordinates-text {
  font-size: 20rpx;
  color: #666;
  margin-left: 4rpx;
}
</style> 