<template>
  <view class="common-routes-page">
    <!-- 顶部标题栏 -->
    <view class="nav-bar" :style="{ paddingTop: statusBarHeightRpx + 'rpx', height: navBarHeightRpx + 'rpx' }">
      <u-icon name="arrow-left" size="22" @click="handleBack" />
      <text class="nav-title">{{ isEdit ? '编辑常用线路' : '添加常用线路' }}</text>
    </view>
    <!-- 内容区 -->
    <scroll-view class="content" scroll-y :style="{ marginTop: navBarTotalHeightRpx + 'rpx' }">
      <!-- 装卸货地选择 -->
      <view class="section">
        <view class="row">
          <text class="label label-z">装</text>
          <view class="select-box" @click="choosePlace('from')">
            <template v-if="fromArea && fromArea.districts && fromArea.districts.length">
              <view class="selected-tag-list">
                <view v-for="(item, idx) in fromArea.districts" :key="item.code" class="selected-tag" @click.stop>
                  <text>{{ item.name }}</text>
                  <text class="remove-btn" @click.stop="removeSelectedDistrict('from', idx)">×</text>
                </view>
              </view>
            </template>
            <template v-else>
              <text class="select-text placeholder">请选择装货地</text>
            </template>
            <u-icon name="arrow-right" size="20" color="#bbb" />
          </view>
        </view>
        <view class="row">
          <text class="label label-x">卸</text>
          <view class="select-box" @click="choosePlace('to')">
            <template v-if="toArea && toArea.districts && toArea.districts.length">
              <view class="selected-tag-list">
                <view v-for="(item, idx) in toArea.districts" :key="item.code" class="selected-tag" @click.stop>
                  <text>{{ item.name }}</text>
                  <text class="remove-btn" @click.stop="removeSelectedDistrict('to', idx)">×</text>
                </view>
              </view>
            </template>
            <template v-else>
              <text class="select-text placeholder">请选择卸货地</text>
            </template>
            <u-icon name="arrow-right" size="20" color="#bbb" />
          </view>
        </view>
      </view>
      <!-- 用车类型 -->
      <view class="section">
        <view class="section-title">
          <u-icon name="car" size="26" color="#7ac251" style="margin-right: 8rpx;" />用车类型
        </view>
        <view class="car-type-list">
          <view v-for="(type, idx) in carTypeList" :key="type" class="car-type-item" :class="carType === type ? 'active' : ''" @click="carType = type">
            {{ type }}
          </view>
        </view>
      </view>
      <!-- 已选区域 -->
      <view v-if="selectedList.length" class="selected-card">
        <view class="selected-card-title">
          <u-icon name="checkbox-mark" size="20" color="#bbb" style="margin-right: 8rpx;" />
          <text class="selected-title">已选（{{ selectedList.length }}个）</text>
        </view>
        <view class="selected-list">
          <view v-for="item in selectedList" :key="item" class="selected-item">
            <text>{{ item }}</text>
            <text class="remove-btn" @click.stop="removeSelected(item)">×</text>
          </view>
        </view>
      </view>
      <!-- 车长选择 -->
      <view class="section">
        <view class="section-title">
          <u-icon name="grid" size="26" color="#3a7afe" style="margin-right: 8rpx;" />车长（可多选）
          <view class="clear-btn" @click.stop="clearLength">
            <u-icon name="trash" size="20" color="#bbb" style="margin-right: 2rpx;" />
            <text>清空</text>
          </view>
        </view>
        <view class="option-list">
          <view v-for="(len, idx) in carLengthList" :key="len" class="option-item" :class="carLengthSelected.includes(len) ? 'selected' : ''" @click="toggleLength(len)">
            {{ len }}
          </view>
        </view>
      </view>
      <!-- 车型选择 -->
      <view class="section">
        <view class="section-title">
          <u-icon name="grid" size="26" color="#3a7afe" style="margin-right: 8rpx;" />车型（可多选）
          <view class="clear-btn" @click.stop="clearModel">
            <u-icon name="trash" size="20" color="#bbb" style="margin-right: 2rpx;" />
            <text>清空</text>
          </view>
        </view>
        <view class="option-list">
          <view v-for="(model, idx) in carModelList" :key="model" class="option-item" :class="carModelSelected.includes(model) ? 'selected' : ''" @click="toggleModel(model)">
            {{ model }}
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- AreaPicker 弹窗 -->
    <AreaPicker :show="showAreaPicker" :title="areaPickerType==='from'?'装货地':'卸货地'" @close="showAreaPicker=false" @confirm="handleAreaConfirm" />
    <!-- 底部确认按钮 -->
    <view class="footer-bar">
      <button class="confirm-btn" @click="handleConfirm">{{ isEdit ? '确认修改' : '确认添加' }}</button>
      <!-- <text class="footer-tip">同时为您播报订阅线路</text> -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import AreaPicker from './AreaPicker.vue'
import { editDriverSubscribe } from '@/api/transport/index'

const statusBarHeightRpx = ref(0)
const navBarHeightRpx = 96
const navBarTotalHeightRpx = ref(navBarHeightRpx)
const isEdit = ref(false)
const editIndex = ref(-1)

onMounted(() => {
  // #ifdef APP-PLUS
  const sysInfo = uni.getSystemInfoSync()
  statusBarHeightRpx.value = sysInfo.statusBarHeight * 2
  navBarTotalHeightRpx.value = statusBarHeightRpx.value + navBarHeightRpx
  // #endif

  // 检查是否是编辑模式
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}
  
  if (options.edit === '1') {
    isEdit.value = true
    // 获取传递的数据
    const eventChannel = currentPage.getOpenerEventChannel()
    eventChannel.on('editLineData', (data) => {
      const { data: line, index } = data
      editIndex.value = index
      
      // 填充表单数据
      if (line.from) {
        fromArea.value = {
          province: { name: line.from.split(' ')[0] },
          city: { name: line.from.split(' ')[1] || '' },
          districts: line.from.split(' ').slice(2).map(name => ({ name }))
        }
      }
      if (line.to) {
        toArea.value = {
          province: { name: line.to.split(' ')[0] },
          city: { name: line.to.split(' ')[1] || '' },
          districts: line.to.split(' ').slice(2).map(name => ({ name }))
        }
      }
      if (line.carType) carType.value = line.carType
      if (line.carLength) carLengthSelected.value = line.carLength.split(',')
      if (line.carModel) carModelSelected.value = line.carModel.split(',')
    })
  }
})

const showAreaPicker = ref(false)
const areaPickerType = ref<'from'|'to'>('from')
const fromArea = ref<any>(null)
const toArea = ref<any>(null)

const fromPlace = computed(() => {
  if (!fromArea.value) return ''
  const { province, city, districts } = fromArea.value
  if (!province || !city || !districts?.length) return ''
  return province.name + ' ' + city.name + ' ' + districts.map((d:any)=>d.name).join('、')
})
const toPlace = computed(() => {
  if (!toArea.value) return ''
  const { province, city, districts } = toArea.value
  if (!province || !city || !districts?.length) return ''
  return province.name + ' ' + city.name + ' ' + districts.map((d:any)=>d.name).join('、')
})

const carTypeList = ['不限类型', '整车'] // , '零担'
const carType = ref('不限类型')
const carLengthList = ['1.8米', '2.7米', '3.3米', '3.8米', '4.2米', '5米', '6.2米', '6.8米', '7.7米', '8.2米', '8.7米', '9.6米', '11.7米', '12.5米', '13米', '13.7米', '15米', '16米', '17.5米']
const carLengthSelected = ref<string[]>([])
const carModelList = ['平板', '高栏', '厢式', '集装箱', '自卸', '冷藏', '厢式保温', '高低板', '面包车', '棉被车', '爬梯车', '飞翼车', '依维柯']
const carModelSelected = ref<string[]>([])

const selectedList = computed(() => {
  return [...carModelSelected.value, ...carLengthSelected.value]
})

const handleBack = () => {
  uni.navigateBack()
}

function choosePlace(type: 'from' | 'to') {
  areaPickerType.value = type
  showAreaPicker.value = true
}
function handleAreaConfirm(val: any) {
  if (areaPickerType.value === 'from') {
    fromArea.value = val

    // 打印装货地选择的数据
    console.log('选择的装货地数据：', JSON.stringify(val, null, 2))
    console.log('省份：', val.province?.name)
    console.log('城市：', val.city?.name)
    console.log('区县：', val.districts?.map((d: any) => d.name).join('、'))
    console.log('坐标：', val.districts && val.districts.length > 0
      ? val.districts[0].coordinates
      : (val.city ? val.city.coordinates : val.province?.coordinates))

    // 选完装货地自动弹出卸货地选择
    areaPickerType.value = 'to'
    showAreaPicker.value = true
  } else {
    toArea.value = val
  }
}

const toggleLength = (len: string) => {
  const idx = carLengthSelected.value.indexOf(len)
  if (idx > -1) carLengthSelected.value.splice(idx, 1)
  else carLengthSelected.value.push(len)
}
const clearLength = () => {
  carLengthSelected.value = []
}
const toggleModel = (model: string) => {
  const idx = carModelSelected.value.indexOf(model)
  if (idx > -1) carModelSelected.value.splice(idx, 1)
  else carModelSelected.value.push(model)
}
const clearModel = () => {
  carModelSelected.value = []
}

const removeSelected = (item: string) => {
  const idx1 = carModelSelected.value.indexOf(item)
  if (idx1 > -1) carModelSelected.value.splice(idx1, 1)
  const idx2 = carLengthSelected.value.indexOf(item)
  if (idx2 > -1) carLengthSelected.value.splice(idx2, 1)
}

function removeSelectedDistrict(type: 'from'|'to', idx: number) {
  if (type === 'from' && fromArea.value && fromArea.value.districts) {
    fromArea.value.districts.splice(idx, 1)
    // 如果删空了，整体置空
    if (fromArea.value.districts.length === 0) fromArea.value = null
  }
  if (type === 'to' && toArea.value && toArea.value.districts) {
    toArea.value.districts.splice(idx, 1)
    if (toArea.value.districts.length === 0) toArea.value = null
  }
}

const handleConfirm = () => {
  if (!fromPlace.value || !toPlace.value) {
    uni.showToast({ title: '请选择装卸货地', icon: 'none' })
    return
  }
  // 可继续校验其他项
  // 获取坐标信息
  const fromCoordinates = fromArea.value && fromArea.value.districts && fromArea.value.districts.length > 0
    ? fromArea.value.districts[0].coordinates
    : (fromArea.value && fromArea.value.city ? fromArea.value.city.coordinates : { lat: 0, lng: 0 });

  const toCoordinates = toArea.value && toArea.value.districts && toArea.value.districts.length > 0
    ? toArea.value.districts[0].coordinates
    : (toArea.value && toArea.value.city ? toArea.value.city.coordinates : { lat: 0, lng: 0 });

  // 数据回传
  // const eventChannel = getOpenerEventChannel()
  let data = {
    startLineName: fromPlace.value,
    endLineName: toPlace.value,
    carType: carType.value,
    carLength: carLengthSelected.value.join(','),
    carModel: carModelSelected.value.join(','),
    id: editIndex.value,
    fromArea: JSON.stringify(fromArea.value.districts),
    toArea: JSON.stringify(toArea.value.districts),
  }

	editDriverSubscribe(data).then((response: any) => {
		if (response.code == 700) {
			data = response.result
			if (isEdit.value) {
			  // eventChannel.emit('updateLineData', data)
			  uni.showToast({ title: '修改成功', icon: 'success' })
			} else {
			  // eventChannel.emit('addLineData', data)
			  uni.showToast({ title: '添加成功', icon: 'success' })
			}
		}
	})
  
  // setTimeout(() => {
  //   uni.navigateBack()
  // }, 800)
}
</script>

<style scoped lang="scss">
.common-routes-page {
  min-height: 100vh;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  padding-left: 24rpx;
  padding-right: 24rpx;
  /* padding-top和height由内联样式动态控制 */

  .nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 36rpx;
    font-weight: bold;
    color: #222;
  }
}

.content {
  flex: 1;
  /* margin-top由内联样式动态控制 */
  margin-bottom: 0;
  padding-bottom: 200rpx;
  overflow-y: auto;
}

.section {
  background: #fff;
  border-radius: 18rpx;
  margin: 24rpx;
  padding: 32rpx 24rpx;
  font-size: 28rpx;
  color: #222;
  margin-bottom: 0;
}

.row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.label {
  width: 44rpx;
  height: 44rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.label-z {
  background: #3a7afe;
  color: #fff;
}

.label-x {
  background: #ff7a00;
  color: #fff;
}

.select-box {
  flex: 1;
  background: #f7f7f7;
  border-radius: 8rpx;
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.select-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder {
  color: #bbb;
}

.selected {
  color: #222;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 18rpx;
  color: #222;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clear-btn {
  color: #3a7afe;
  font-size: 24rpx;
  margin-left: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.car-type-list {
  display: flex;
  gap: 24rpx;
}

.car-type-item {
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  background: #f7f7f7;
  color: #222;
  font-size: 28rpx;
  border: 2rpx solid #f7f7f7;
}

.car-type-item.active {
  background: #fff0f0;
  color: #e84138;
  border: 2rpx solid #e84138;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
  margin-left: -12rpx;
  margin-right: -12rpx;
}

.option-item {
  flex: 1 0 22%;
  margin: 0 12rpx 18rpx 12rpx;
  max-width: calc(25% - 24rpx);
  min-width: 0;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  border-radius: 16rpx;
  background: #f7f7f7;
  color: #222;
  font-size: 28rpx;
  border: 2rpx solid #f7f7f7;
  box-sizing: border-box;
  transition: all 0.2s;
}

.option-item.selected {
  background: #fff0f0;
  color: #e84138;
  border: 2rpx solid #e84138;
}

.footer-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0 32rpx 0;
  z-index: 20;

  .confirm-btn {
    width: 90%;
    height: 88rpx;
    background: #ffb3b3;
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
    font-weight: bold;
    margin-bottom: 8rpx;
  }

  .footer-tip {
    color: #e84138;
    font-size: 22rpx;
  }
}

.selected-card {
  background: #fff;
  border-radius: 18rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  margin: 24rpx 24rpx 0 24rpx;
  padding: 24rpx 24rpx 12rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.selected-card-title {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.selected-title {
  font-size: 26rpx;
  color: #666;
}

.selected-list {
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  gap: 12rpx;
  max-width: 100%;
}

.selected-list-ellipsis {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.selected-item {
  display: inline-flex;
  align-items: center;
  background: #fff0f0;
  color: #e84138;
  border: 1rpx solid #ffd6d6;
  border-radius: 12rpx;
  padding: 0 16rpx;
  height: 40rpx;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  flex-shrink: 0;
  margin-right: 8rpx;
}

.remove-btn {
  margin-left: 6rpx;
  font-size: 28rpx;
  color: #e84138;
  cursor: pointer;
}

.selected-tag-list {
  display: flex;
  flex-wrap: wrap;
  max-width: 100%;
}

.selected-tag {
  display: inline-flex;
  align-items: center;
  background: #fff0f0;
  color: #e84138;
  border: 1rpx solid #ffd6d6;
  border-radius: 12rpx;
  padding: 0 16rpx;
  height: 40rpx;
  font-size: 24rpx;
  flex-shrink: 0;
  margin-right: 8rpx;
}
</style>
