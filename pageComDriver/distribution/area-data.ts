import { regionData } from 'element-china-area-data'  
import { provincesCoordinates, citiesCoordinates, districtsCoordinates } from './coordinates-full'

interface Coordinates {
  lat: number;
  lng: number;
}

// 创建完整的坐标映射
const coordinatesMap: Record<string, Coordinates> = {
  ...provincesCoordinates,
  ...citiesCoordinates,
  ...districtsCoordinates
};

function convert(data: any) {
  return data.map((province: any) => ({
    name: province.label,
    code: province.value,
    coordinates: coordinatesMap[province.value.substring(0, 2)] || { lat: 0, lng: 0 },
    cities: (province.children || []).map((city: any) => ({
      name: city.label,
      code: city.value,
      coordinates: coordinatesMap[city.value.substring(0, 4)] || coordinatesMap[province.value.substring(0, 2)] || { lat: 0, lng: 0 },
      districts: (city.children || []).map((district: any) => ({
        name: district.label,
        code: district.value,
        coordinates: coordinatesMap[district.value] || coordinatesMap[city.value.substring(0, 4)] || coordinatesMap[province.value.substring(0, 2)] || { lat: 0, lng: 0 }
      }))
    }))
  }))
}

export const areaData = convert(regionData)

// 提供获取特定区域坐标的辅助函数
export function getAreaCoordinates(code: string): Coordinates {
  // 直接查找精确的行政区划代码
  if (coordinatesMap[code]) {
    return coordinatesMap[code];
  }
  
  // 如果找不到区县级别的坐标，尝试使用所属市的坐标
  if (code.length === 6) {
    const cityCode = code.substring(0, 4);
    if (coordinatesMap[cityCode]) {
      return coordinatesMap[cityCode];
    }
    
    // 如果找不到市级坐标，则使用所属省的坐标
    const provinceCode = code.substring(0, 2);
    if (coordinatesMap[provinceCode]) {
      return coordinatesMap[provinceCode];
    }
  }
  
  // 默认返回北京的坐标
  return { lat: 39.904989, lng: 116.405285 };
}

// 根据名称查找区域及其坐标
export function findAreaByName(name: string): { code: string, coordinates: Coordinates } | null {
  // 在省级查找
  for (const province of areaData) {
    if (province.name === name) {
      return { code: province.code, coordinates: province.coordinates };
    }
    
    // 在市级查找
    for (const city of province.cities) {
      if (city.name === name) {
        return { code: city.code, coordinates: city.coordinates };
      }
      
      // 在区县级查找
      for (const district of city.districts) {
        if (district.name === name) {
          return { code: district.code, coordinates: district.coordinates };
        }
      }
    }
  }
  
  return null;
}

// 获取省市区完整名称和坐标
export function getFullAreaInfo(provinceCode: string, cityCode?: string, districtCode?: string): { 
  fullName: string, 
  coordinates: Coordinates 
} {
  let fullName = '';
  let coordinates: Coordinates = { lat: 0, lng: 0 };
  
  const province = areaData.find((p: { code: string }) => p.code === provinceCode);
  if (!province) return { fullName, coordinates };
  
  fullName = province.name;
  coordinates = province.coordinates;
  
  if (cityCode) {
    const city = province.cities.find((c: { code: string }) => c.code === cityCode);
    if (city) {
      fullName += ' ' + city.name;
      coordinates = city.coordinates;
      
      if (districtCode) {
        const district = city.districts.find((d: { code: string }) => d.code === districtCode);
        if (district) {
          fullName += ' ' + district.name;
          coordinates = district.coordinates;
        }
      }
    }
  }
  
  return { fullName, coordinates };
}