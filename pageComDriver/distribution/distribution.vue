<template>
  <view class="distribution-page">
    <!-- 顶部功能区 -->
    <view class="top-bar" :style="{ paddingTop: statusBarHeight * 2 + 'rpx' }">
      <view class="top-left">
        <view class="icon-btn"><u-icon name="chat" size="28"></u-icon><text>消息</text></view>
        <view class="icon-btn"><u-icon name="search" size="28"></u-icon><text>找货记录</text></view>
      </view>
      <view class="top-right">
        <!-- <view class="icon-btn"><u-icon name="volume-off" size="28"></u-icon><text>听单设置</text></view>
        <view class="listen-btn">去听单</view> -->
      </view>
    </view>
    <!-- 主体内容用z-paging包裹，下拉刷新整个区域 -->
    <z-paging ref="pagingRef" v-model="listGoods" @query="handlePagingQuery" :fixed="false" :show-scrollbar="false" :auto-show-back-to-top="true" :use-page-scroll="true" auto-show-system-loading cell-height-mode="dynamic" :loading-more-enabled="true" :show-loading-more-no-more-view="true" :show-empty-view="true" :empty-view-text="'暂无新货'" :style="{ paddingTop: topBarTotalHeight + 'rpx' }">
      <template #default>
        <!-- 红色通知横幅 -->
        <!-- <view class="notice-bar">开启系统通知，及时看新货！<text class="notice-link">去开启></text></view> -->
        <!-- 订阅线路卡片 -->
        <view class="card subscribe-card">
          <view class="card-header">
            <text class="card-title">订阅线路</text>
            <text class="card-count">{{ lineList.length }}/10</text>
            <view class="card-actions">
              <text class="edit" @tap="toggleEditMode">{{ isEditMode ? '完成' : '编辑' }}</text>
              <text class="add" @tap="handleAddLine">+ 添加线路</text>
            </view>
          </view>
          <view class="line-list">
            <view class="line-item" v-for="(item, index) in lineList" :key="index" @click="handleLineItemClick(item)">
              <u-icon name="volume-off" size="20" color="#ccc"></u-icon>
              <text class="line">{{ item.from }} → {{ item.to }}</text>
              <text class="tag" v-if="item.recommended">推荐</text>
              <view class="red-dot" v-if="item.newCount">{{ item.newCount }}</view>
              <view class="edit-actions" v-if="isEditMode">
                <view @tap.stop="handleEditLine(index)" style="display:inline-flex;align-items:center;">
                  <u-icon name="edit-pen" size="24" color="#666"></u-icon>
                </view>
                <view @tap.stop="handleDeleteLine(index, item)" style="display:inline-flex;align-items:center;">
                  <u-icon name="trash" size="24" color="#e84138"></u-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 订阅新货卡片 -->
        <view class="card new-goods-card">
          <view class="card-header">
            <text class="card-title">订阅新货</text>
            <!-- <text class="history">听单历史</text> -->
          </view>
          <!-- <view class="goods-list">
            <view class="goods-item" v-for="item in listGoods" :key="item.id"> -->
          <uni-swipe-action>
            <uni-swipe-action-item v-for="item in listGoods" :key="item.id" :isMoreLine="true" :right-options="options" :show="item.show" :disabled="item.disabled" @click.top="itemHandleClick(item)">
              <view class="goods-title">
                <text class="from">{{ item.loadingAddress.province }} {{ item.loadingAddress.city }}</text>
                <text class="arrow">→</text>
                <text class="to">{{ item.dischargeAddress.province }} {{ item.dischargeAddress.city }}</text>
<!--
                <text class="type" :class="item.carpool ? 'carpool' : 'fixed'">{{ item.type }}</text>
-->
<!--                <text v-if="!item.carpool" class="tag blue">满运宝</text>
                <text v-if="!item.carpool" class="tag blue">企业</text>-->
              </view>
              <view class="goods-desc">{{ item.desc }}</view>
              <view class="goods-info">{{ item.info }}</view>
              <view class="goods-footer">
<!--
                <view class="credit"><u-icon name="account" size="18"></u-icon>信用 {{ item.credit }}星 交易{{ item.deals }}</view>
-->
                <view class="price-action">
                  <text class="price">*****元/趟</text>
                  <u-button type="error" size="mini" shape="circle" text="去抢单" @click="grabOrder(item)"></u-button>
                </view>
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>
          <!-- </view> -->
          <!-- </view> -->
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
// @ts-ignore
import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js';
import { allDriverSubscribeList, deleteDriverSubscribe, getNewDistributionDriver, driverGrabOrders } from '@/api/transport/index'

const statusBarHeight = ref(0)
const topBarHeight = 80 // rpx
const topBarTotalHeight = computed(() => statusBarHeight.value * 2 + topBarHeight)

const listGoods = ref<any[]>([])
const pagingRef = ref()

// 订阅线路数据
const lineList = ref([
  { from: '北京', to: '北京', recommended: false, newCount: 0 },
  { from: '廊坊', to: '乌兰察布', recommended: true, newCount: 0 },
  { from: '天津', to: '大同', recommended: true, newCount: 3 }
])

// 编辑模式状态
const isEditMode = ref(false)

// 切换编辑模式
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value
}

// 添加线路
const handleAddLine = () => {
  uni.navigateTo({
    url: '/pageComDriver/distribution/common_routes'
  })
}

// 编辑线路
const handleEditLine = (index: number) => {
  const item: any = lineList.value[index]
  uni.navigateTo({
    url: '/pageComDriver/distribution/common_routes?edit=1',
    success: (res) => {
      res.eventChannel.emit('editLineData', { data: item, index: item.id })
    }
  })
}

// 删除线路
const handleDeleteLine = (index: number, item: any) => {
  uni.showModal({
    title: '删除线路',
    content: '确定要删除这条线路吗？',
    success: (res) => {
      if (res.confirm) {
				deleteDriverSubscribe({
					id: item.id
				}).then((response: any) => {
					if (response.code == 700) {
						lineList.value.splice(index, 1)
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						})
					}
				})
      }
    }
  })
}

// 处理线路点击事件
const handleLineItemClick = (item: any) => {
  uni.navigateTo({
    url: '/pageComDriver/distribution/router_detail',
    success: (res) => {
      // 传递线路信息到详情页
      res.eventChannel.emit('lineData', { data: item })
    }
  })
}

// 模拟异步分页请求
const queryList = async (page: number, pageSize: number) => {
  return new Promise<any[]>((resolve) => {
    getNewDistributionDriver().then((response: any) => {
      if (response.code == 700) {
        console.log('gwk=====response.code：', JSON.stringify(response.result))
        resolve(response.result)
      }
    })
   /* setTimeout(() => {
      let newData = Array.from({ length: pageSize }, (_, i) => ({
        id: (page - 1) * pageSize + i + 1,
        from: '北京 昌平',
        to: '张家口 张北',
        type: i % 2 === 0 ? '拼车' : '一口价',
        carpool: i % 2 === 0,
        desc: i % 2 === 0 ? '电动车 裸装 今天装货' : '电子产品及配件 托盘 明天8-9点装货...',
        info: i % 2 === 0 ? '2.7米及以上 | 面包车 | 1.5方' : '2.7/3.8米 | 厢式/高栏/平板 | 0.7吨 | 3.3方',
        credit: '4.5',
        deals: i % 2 === 0 ? '882' : '86',
      }))
      // 模拟最后一页
      if (page > 3) newData = []
      resolve(newData)
    }, 2000)*/
  })
}

const handlePagingQuery = async (page: number, pageSize: number, from: any) => {
  console.log(123, from)
  const data = await queryList(page, pageSize)
  pagingRef.value.complete(data)
}

// 类似mixins，如果是页面滚动务必要写这一行，并传入当前ref绑定的paging，注意此处是paging，而非paging.value
useZPaging(pagingRef);

const options = reactive([
  {
    text: `今天`,
    text2: `不再展示`,
    style: {
      backgroundColor: 'gray',
      color: '#fff',
      fontSize: '26rpx'
    }
  }
]);

const itemHandleClick = (data: any) => {
  console.log(data);
};

onMounted(() => {
  // #ifdef APP-PLUS
  const sysInfo = uni.getSystemInfoSync()
  statusBarHeight.value = sysInfo.statusBarHeight || 0
  // #endif
	
	getDriverSubscribeList()
	getNewDistributionDriverFun()
})

// 配货大厅查询订阅线路
const getDriverSubscribeList = () => {
	allDriverSubscribeList().then((response: any) => {
		if (response.code == 700) {
			lineList.value = response.result
		}
	})
}

// 配货大厅查询订阅新货
const getNewDistributionDriverFun = () => {
	getNewDistributionDriver().then((response: any) => {
		if (response.code == 700) {
			listGoods.value = response.result
		}
	})
}

// 去抢单
const grabOrder = (item: any) => {
	driverGrabOrders({
		id: item.id
	}).then((response: any) => {
		if (response.code == 700) {
			
		}
	})
}
</script>

<style lang="scss" scoped>
.distribution-page {
  background: #f7f7f7;
  min-height: 100vh;
}

.top-bar {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* 顶部padding由动态padding控制 */
  padding: 0 24rpx 0 24rpx;
  box-sizing: border-box;
  background: linear-gradient(90deg, #ffe6e6 0%, #fff 100%);
  box-shadow: 0 2rpx 8rpx rgba(232, 65, 56, 0.04);
}

.top-left,
.top-right {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 22rpx;
  color: #333;

  text {
    margin-top: 6rpx;
  }
}

.listen-btn {
  background: #fff;
  color: #e84138;
  border-radius: 40rpx;
  padding: 8rpx 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin-left: 16rpx;
  border: 2rpx solid #e84138;
}

.notice-bar {
  background: #fff0f0;
  color: #e84138;
  font-size: 24rpx;
  padding: 16rpx 24rpx;
  margin: 24rpx 0 0 0;

  .notice-link {
    color: #e84138;
    margin-left: 8rpx;
  }
}

.card {
  background: #fff;
  border-radius: 18rpx;
  margin: 24rpx 24rpx 0 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  padding: 0 0 24rpx 0;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx 0 24rpx;

  .card-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #e84138;
    margin-right: 12rpx;
  }

  .card-count {
    font-size: 24rpx;
    color: #999;
    margin-right: auto;
  }

  .card-actions {
    display: flex;
    gap: 24rpx;

    .edit {
      color: #e84138;
      font-size: 24rpx;
    }

    .add {
      color: #e84138;
      font-size: 24rpx;
    }
  }

  .history {
    margin-left: auto;
    color: #999;
    font-size: 24rpx;
  }
}

.line-list {
  padding: 0 24rpx;

  .line-item {
    display: flex;
    align-items: center;
    margin-top: 18rpx;
    font-size: 28rpx;
    color: #333;
    background: linear-gradient(90deg, #ffe6e6 0%, #fff 100%);
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 12rpx;
    position: relative;

    .edit-actions {
      position: absolute;
      right: 20rpx;
      display: flex;
      gap: 20rpx;
      align-items: center;

      .edit-text {
        color: #666;
        font-size: 24rpx;
      }

      .delete-text {
        color: #e84138;
        font-size: 24rpx;
      }
    }

    .line {
      margin-left: 12rpx;
      margin-right: 8rpx;
    }

    .tag {
      background: #f5f5f5;
      color: #e84138;
      font-size: 22rpx;
      border-radius: 6rpx;
      padding: 2rpx 12rpx;
      margin-left: 8rpx;
    }

    .red-dot {
      background: #e84138;
      color: #fff;
      border-radius: 50%;
      font-size: 20rpx;
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8rpx;
    }
  }
}

.new-goods-card {
  background-color: transparent;
  padding-bottom: 0px;

  :deep(.uni-swipe) {
    margin-top: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);

    .uni-swipe_text--center {
      padding: 24rpx;
    }

    .goods-title {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;

      .arrow {
        margin: 0 12rpx;
        color: #999;
      }

      .type {
        margin-left: 12rpx;
        font-size: 24rpx;
        border-radius: 6rpx;
        padding: 2rpx 12rpx;

        &.carpool {
          background: #e8f5e9;
          color: #7ac251;
        }

        &.fixed {
          background: #fff2f0;
          color: #e84138;
        }
      }

      .tag {
        margin-left: 8rpx;
        font-size: 20rpx;
        background: #e6f0ff;
        color: #3a7afe;
        border-radius: 4rpx;
        padding: 2rpx 8rpx;

        &.blue {
          background: #e6f0ff;
          color: #3a7afe;
        }
      }
    }

    .goods-desc {
      font-size: 26rpx;
      color: #666;
      margin: 12rpx 0 0 0;
    }

    .goods-info {
      font-size: 24rpx;
      color: #999;
      margin: 8rpx 0 0 0;
    }

    .goods-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18rpx;

      .credit {
        font-size: 22rpx;
        color: #999;
        display: flex;
        align-items: center;

        u-icon {
          margin-right: 4rpx;
        }
      }

      .price-action {
        display: flex;
        align-items: center;

        .price {
          color: #e84138;
          font-size: 28rpx;
          font-weight: bold;
          margin-right: 12rpx;
        }
      }
    }
  }
}
</style>