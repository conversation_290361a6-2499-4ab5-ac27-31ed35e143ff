/**
 * 使用高德地图API批量获取省市区县坐标
 * 
 * 使用方法:
 * 1. 申请高德地图开发者账号，获取API Key
 * 2. 安装依赖: npm install axios fs
 * 3. 替换下方的 API_KEY 变量
 * 4. 运行脚本: node fetch-coordinates.js
 * 5. 生成的坐标数据将保存到 coordinates-full.js 文件中
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { regionData } = require('element-china-area-data');

// 替换为你的高德地图API Key
const API_KEY = 'f771b56602350cd85ec928e0442c7512';

// 延迟函数，避免请求过于频繁
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// 将地区名称转换为完整地址
function getFullAddress(province, city = '', district = '') {
  let address = province;
  if (city) address += city;
  if (district) address += district;
  return address;
}

// 使用高德地图API获取坐标
async function getCoordinates(address) {
  try {
    const url = `https://restapi.amap.com/v3/geocode/geo?address=${encodeURIComponent(address)}&key=${API_KEY}`;
    const response = await axios.get(url);
    
    if (response.data.status === '1' && response.data.geocodes && response.data.geocodes.length > 0) {
      const [lng, lat] = response.data.geocodes[0].location.split(',');
      return { lat: parseFloat(lat), lng: parseFloat(lng) };
    }
    
    console.error(`未找到地址: ${address}`);
    return null;
  } catch (error) {
    console.error(`获取坐标失败: ${address}`, error.message);
    return null;
  }
}

// 主函数
async function fetchAllCoordinates() {
  const provincesCoordinates = {};
  const citiesCoordinates = {};
  const districtsCoordinates = {};
  
  // 处理进度
  let total = 0;
  let processed = 0;
  
  // 计算总数
  for (const province of regionData) {
    total++;
    if (province.children) {
      for (const city of province.children) {
        total++;
        if (city.children) {
          total += city.children.length;
        }
      }
    }
  }
  
  console.log(`开始获取坐标，共 ${total} 个地区`);
  
  // 处理省份
  for (const province of regionData) {
    console.log(`处理省份: ${province.label} (${++processed}/${total})`);
    
    const coords = await getCoordinates(province.label);
    if (coords) {
      provincesCoordinates[province.value] = coords;
    }
    
    await delay(300); // 避免请求过于频繁
    
    // 处理城市
    if (province.children) {
      for (const city of province.children) {
        console.log(`处理城市: ${province.label}${city.label} (${++processed}/${total})`);
        
        const cityCoords = await getCoordinates(getFullAddress(province.label, city.label));
        if (cityCoords) {
          citiesCoordinates[city.value] = cityCoords;
        }
        
        await delay(300);
        
        // 处理区县
        if (city.children) {
          for (const district of city.children) {
            console.log(`处理区县: ${province.label}${city.label}${district.label} (${++processed}/${total})`);
            
            const districtCoords = await getCoordinates(
              getFullAddress(province.label, city.label, district.label)
            );
            
            if (districtCoords) {
              districtsCoordinates[district.value] = districtCoords;
            }
            
            await delay(300);
          }
        }
      }
    }
  }
  
  // 生成坐标映射文件
  const result = `// 中国行政区划代码到坐标的映射
// 数据来源: 高德地图API
// 坐标系统: GCJ-02（高德坐标系）
// 生成时间: ${new Date().toISOString()}

// 坐标数据类型定义
export interface Coordinates {
  lat: number; // 纬度
  lng: number; // 经度
}

// 省级行政区坐标
export const provincesCoordinates = ${JSON.stringify(provincesCoordinates, null, 2)};

// 城市坐标
export const citiesCoordinates = ${JSON.stringify(citiesCoordinates, null, 2)};

// 区县坐标
export const districtsCoordinates = ${JSON.stringify(districtsCoordinates, null, 2)};

// 导出完整的坐标映射
export const coordinatesMap = {
  ...provincesCoordinates,
  ...citiesCoordinates,
  ...districtsCoordinates,
};

// 获取坐标的辅助函数
export function getCoordinates(code: string): Coordinates {
  // 直接查找精确的行政区划代码
  if (coordinatesMap[code]) {
    return coordinatesMap[code];
  }
  
  // 如果找不到区县级别的坐标，尝试使用所属市的坐标
  if (code.length === 6) {
    const cityCode = code.substring(0, 4) + '00';
    if (coordinatesMap[cityCode]) {
      return coordinatesMap[cityCode];
    }
    
    // 如果找不到市级坐标，则使用所属省的坐标
    const provinceCode = code.substring(0, 2) + '0000';
    if (coordinatesMap[provinceCode]) {
      return coordinatesMap[provinceCode];
    }
  }
  
  // 默认返回北京的坐标
  return { lat: 39.904989, lng: 116.405285 };
}
`;

  // 保存文件
  fs.writeFileSync(path.join(__dirname, 'coordinates-full.ts'), result);
  console.log('坐标数据已保存到 coordinates-full.ts');
}

// 执行主函数
fetchAllCoordinates().catch(console.error); 