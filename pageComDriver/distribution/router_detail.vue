<template>
  <view class="router-detail" @click="handlePageClick">
    <page-meta :page-style="pageStyle"></page-meta>
    <!-- 顶部工具栏 -->
    <view class="top-bar" :style="{ paddingTop: statusBarHeight * 2 + 'rpx', position: 'fixed', top: 0, left: 0, right: 0, zIndex: 1000 }">
      <view class="top-bar-row">
        <u-icon name="arrow-left" size="22" @click="goBack" />
        <view class="title-select" @click="handleTitleClick">
          <text class="title">{{ formattedRouteTitle }}</text>
          <u-icon name="arrow-down-fill" size="22" color="#e84138" class="dropdown-arrow" />
        </view>
        <view class="spacer"></view>
        <!-- <text class="setting">设置</text> -->
      </view>
      <view class="subscribed-row">
        <text class="subscribed-tip">已订阅：</text>
        <scroll-view class="subscribed-scroll" scroll-x show-scrollbar="false">
          <view class="subscribed-list">
            <view class="subscribed-item" v-for="(item, idx) in subscribedList" :key="idx" @click="selectSubscribed(item)">
              {{ item.from }} → {{ item.to }}
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="filter-row">
        <view class="listen-status">
          <!-- <u-icon name="volume-off" size="22" color="#ccc" />
          <text>未开启听单</text> -->
        </view>
        <view class="filter-dropdowns">
          <view class="filter-btn dropdown-btn" @click.stop="showSortDropdown = !showSortDropdown">
            {{ selectedSort.substring(0, 2) }}
            <u-icon name="arrow-down" size="16" color="#e84138" class="dropdown-arrow-mini" :class="{ 'rotate': showSortDropdown }" />
          </view>
          <view class="filter-btn dropdown-btn" @click.stop="showFilterDropdown = !showFilterDropdown">
            筛选
            <u-icon name="arrow-down" size="18" color="#e84138" class="dropdown-arrow-mini" :class="{ 'rotate': showFilterDropdown }" />
          </view>
        </view>
      </view>
      <!-- 智能排序下拉弹窗和遮罩层 -->
      <view v-if="showSortDropdown">
        <view class="dropdown-mask" :style="{ top: dropdownTop + 'rpx' }" @click.stop="showSortDropdown = false"></view>
        <view class="dropdown-popup" :style="{ top: dropdownTop + 'rpx' }">
          <view v-for="option in sortOptions" :key="option" class="dropdown-popup-item" @click.stop="selectSort(option)">
            <text :class="['dropdown-popup-label', selectedSort === option ? 'active' : '']">{{ option }}</text>
            <u-icon v-if="selectedSort === option" name="checkmark" size="22" color="#e84138" class="dropdown-popup-check" />
          </view>
        </view>
      </view>
      <!-- 筛选下拉弹窗和遮罩层 -->
      <view v-if="showFilterDropdown">
        <view class="dropdown-mask" :style="{ top: dropdownTop + 'rpx' }" @click.stop="showFilterDropdown = false"></view>
        <view class="dropdown-popup" :style="{ top: dropdownTop + 'rpx' }">
          <view class="filter-section">
            <view class="filter-group" v-for="(group, gIdx) in filterOptions" :key="group.label">
              <view class="filter-group-title">{{ group.label }}</view>
              <view class="filter-group-list">
                <view
                  v-for="(option, oIdx) in group.options"
                  :key="option"
                  class="filter-option"
                  :class="{ selected: filterSelected[group.key] && filterSelected[group.key].includes(option) }"
                  @click.stop="toggleFilterOption(group.key, option)"
                >
                  {{ option }}
                </view>
              </view>
            </view>
          </view>
          <view class="filter-footer">
            <view class="filter-clear" @click.stop="clearFilter">清空条件</view>
            <view class="filter-confirm" @click.stop="confirmFilter">确定</view>
          </view>
        </view>
      </view>
      <view class="tags-row">
        <scroll-view class="tags-scroll" scroll-x show-scrollbar="false">
          <view class="tags-list">
            <view class="tag" v-for="(tag, idx) in tags" :key="tag">
              {{ tag }}
              <u-icon name="close" size="18" color="#bbb" class="tag-close" @click="removeTag(idx)" />
            </view>
          </view>
        </scroll-view>
        <view class="clear-btn" v-if="tags.length" @click="clearTags">清空</view>
      </view>
    </view>
    <!-- 主体内容用z-paging包裹，下拉刷新整个区域 -->
    <z-paging ref="pagingRef" v-model="listGoods" @query="handlePagingQuery" :fixed="false" :show-scrollbar="false" :auto-show-back-to-top="true" :use-page-scroll="true" auto-show-system-loading cell-height-mode="dynamic" :loading-more-enabled="true" :show-loading-more-no-more-view="true" :show-empty-view="true" :empty-view-text="'暂无新货'" :style="{ paddingTop: topBarTotalHeight + 'rpx' }">
      <template #default>
        <!-- 抢光提示和推荐货源 -->
        <view class="empty-recommend-box">
          <view class="empty-top">
            <image class="empty-img" src="/static/images/empty.png" mode="widthFix" />
            <view class="empty-title">该线路货源已被抢光</view>
          </view>
          <view class="empty-bottom">
            <view class="recommend-title-box">
              <view class="recommend-title-line"></view>
              <view class="recommend-title">推荐货源</view>
              <view class="recommend-title-line"></view>
            </view>
            <view class="recommend-desc">该线路无更多符合条件货源，为您推荐</view>
          </view>
        </view>
        <!-- 订阅新货卡片 -->
        <view class="card new-goods-card">
          <uni-swipe-action>
            <uni-swipe-action-item v-for="item in listGoods" :key="item.id" :isMoreLine="true" :right-options="options" :show="item.show" :disabled="item.disabled" @click.top="itemHandleClick(item)">
              <view class="goods-title">
                <text class="from">{{ item.from }}</text>
                <text class="arrow">→</text>
                <text class="to">{{ item.to }}</text>
                <text class="type" :class="item.carpool ? 'carpool' : 'fixed'">{{ item.type }}</text>
                <text v-if="!item.carpool" class="tag blue">满运宝</text>
                <text v-if="!item.carpool" class="tag blue">企业</text>
              </view>
              <view class="goods-desc">{{ item.desc }}</view>
              <view class="goods-info">{{ item.info }}</view>
              <view class="goods-footer">
                <view class="credit"><u-icon name="account" size="18"></u-icon>信用 {{ item.credit }}星 交易{{ item.deals }}</view>
                <view class="price-action">
                  <text class="price">*****元/趟</text>
                  <u-button type="error" size="mini" shape="circle" text="去抢单"></u-button>
                </view>
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js'

const statusBarHeight = ref(0)
const topBarTotalHeight = computed(() => {
  if (tags.value.length > 0) {
    return statusBarHeight.value * 2 + 152 * 2
  }
  return statusBarHeight.value * 2 + 126 * 2
})

const dropdownTop = computed(() => {
  return statusBarHeight.value * 2 + 110 * 2
})

// 文本截断工具函数
const truncateText = (text: string, maxLength: number = 4): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 格式化路线标题
const formattedRouteTitle = computed(() => {
  const fromText = truncateText(lineData.value.from, 3)
  const toText = truncateText(lineData.value.to, 3)
  return `${fromText} → ${toText}`
})

const lineData = ref<any>({ from: '', to: '' })
const subscribedList = ref([
  { from: '天津', to: '安徽' },
  { from: '北京', to: '北京' },
  { from: '北京', to: '廊坊' },
  { from: '张家口', to: '背景' },
  { from: '呼伦贝尔', to: '河北' },
  { from: '河北', to: '石家庄' },
])
const tags = ref<string[]>(['12.5/17.5米', '厢式', '高栏', '平板', '1.5方', '3.3方', '4.2方', '6.6方', '9.6方', '13.5方', '17.5方'])

const listGoods = ref<any[]>([])
const pagingRef = ref()

const sortOptions = reactive(['智能排序', '距离优先', '价格优先', '时间优先'])
const selectedSort = ref('智能排序')
const showSortDropdown = ref(false)
const showFilterDropdown = ref(false)

const pageStyle = ref('')

// 模拟异步分页请求
const queryList = async (page: number, pageSize: number) => {
  return new Promise<any[]>((resolve) => {
    setTimeout(() => {
      let newData = Array.from({ length: pageSize }, (_, i) => ({
        id: (page - 1) * pageSize + i + 1,
        from: '北京 昌平',
        to: '张家口 张北',
        type: i % 2 === 0 ? '拼车' : '一口价',
        carpool: i % 2 === 0,
        desc: i % 2 === 0 ? '电动车 裸装 今天装货' : '电子产品及配件 托盘 明天8-9点装货...',
        info: i % 2 === 0 ? '2.7米及以上 | 面包车 | 1.5方' : '2.7/3.8米 | 厢式/高栏/平板 | 0.7吨 | 3.3方',
        credit: '4.5',
        deals: i % 2 === 0 ? '882' : '86',
      }))
      // 模拟最后一页
      if (page > 3) newData = []
      resolve(newData)
    }, 2000)
  })
}

const handlePagingQuery = async (page: number, pageSize: number, from: any) => {
  const data = await queryList(page, pageSize)
  pagingRef.value.complete(data)
}

// 类似mixins，如果是页面滚动务必要写这一行，并传入当前ref绑定的paging，注意此处是paging，而非paging.value
useZPaging(pagingRef)

const options = ref([
  {
    text: `今天`,
    text2: `不再展示`,
    style: {
      backgroundColor: 'gray',
      color: '#fff',
      fontSize: '26rpx'
    }
  }
])

const itemHandleClick = (data: any) => {
  console.log(data)
}

onLoad(() => {
  // 兼容APP端状态栏高度
  // #ifdef APP-PLUS
  const sysInfo = uni.getSystemInfoSync()
  statusBarHeight.value = sysInfo.statusBarHeight || 0
  // #endif
  // 获取路由参数
  const eventChannel = getCurrentPages()[getCurrentPages().length - 1].getOpenerEventChannel()
  eventChannel.on('lineData', (data: any) => {
    lineData.value = data.data
  })
})

const goBack = () => {
  uni.navigateBack()
}

// 预留下拉弹窗事件
const handleTitleClick = () => {
  // TODO: 后续实现下拉弹窗
  uni.showToast({ title: '后续支持下拉弹窗', icon: 'none' })
}

// 移除单个标签
const removeTag = (idx: number) => {
  tags.value.splice(idx, 1)
}

// 清空所有标签
const clearTags = () => {
  tags.value = []
}

const selectSubscribed = (item: any) => {
  lineData.value.from = item.from
  lineData.value.to = item.to
}

const selectSort = (option: string) => {
  selectedSort.value = option
  showSortDropdown.value = false
}

// 在 script 部分添加控制页面滚动的方法
const toggleScroll = (disable: boolean) => {
  // #ifdef H5
  if (disable) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = 'auto';
  }
  // #endif

  // #ifdef APP-PLUS
  if (disable) {
    pageStyle.value = 'overflow: hidden;'
  } else {
    pageStyle.value = 'overflow: scroll;'
  }
  // #endif
};

// 在 showSortDropdown 的 watch 中调用 toggleScroll
watch(showSortDropdown, (newVal) => {
  toggleScroll(newVal);
});

// 在 showFilterDropdown 的 watch 中调用 toggleScroll
watch(showFilterDropdown, (newVal) => {
  toggleScroll(newVal);
});

// 在 script 部分修改点击外部关闭弹窗的方法
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.dropdown-popup') && !target.closest('.filter-btn')) {
    showSortDropdown.value = false;
    showFilterDropdown.value = false;
  }
};

// 在 onMounted 和 onUnmounted 中添加和移除事件监听
onMounted(() => {
  // #ifdef H5
  document.addEventListener('click', handleClickOutside);
  // #endif
});

onUnmounted(() => {
  // #ifdef H5
  document.removeEventListener('click', handleClickOutside);
  // #endif
});

const handlePageClick = (event: any) => {
  if (showSortDropdown.value || showFilterDropdown.value) {
    showSortDropdown.value = false;
    showFilterDropdown.value = false;
  }
};

// 筛选项数据
const filterOptions = [
  { label: '货源类型（可多选）', key: 'goodsType', options: ['不限类型', '满运宝', '满帮优车'] },
  { label: '用车类型（可多选）', key: 'carType', options: ['不限类型', '整车', '零担'] },
  { label: '装货时间（可多选）', key: 'loadTime', options: ['不限', '今天', '明天', '明天以后'] },
  { label: '重量范围（吨，可多选）', key: 'weight', options: ['不限', '0-5', '5-10', '10-20', '20-30', '30-40', '40以上'] },
  { label: '车长（可多选）', key: 'carLength', options: ['不限车长', '1.8米', '2.7米', '3.3米', '3.8米', '4.2米', '5米', '6.2米', '6.8米', '7.7米', '8.2米', '8.7米', '9.6米', '11.7米', '12.5米', '13米', '13.7米', '15米', '16米', '17.5米'] },
  { label: '车型（可多选）', key: 'carModel', options: ['不限车型', '平板', '高栏', '厢式', '集装箱', '自卸', '冷藏', '厢式保温', '高低板', '面包车', '棉被车', '爬梯车', '飞翼车', '依维柯'] },
  { label: '货物类型（可多选）', key: 'cargoType', options: ['不限', '蔬菜', '水果', '玉米', '木材苗圃', '粮食谷壳', '活禽活畜', '活水产品', '经济作物', '其他农业', '金属钢材', '煤炭矿产', '建材', '化工塑料', '纸类', '机械设备', '配件零件', '农用物资', '空包装', '废品废料', '家具家居', '食品饮料', '服饰纺织', '设施用具', '快递搬家', '车辆', '办公文体', '快消医药', '数码家电', '其他'] }
]

// 选中项
const filterSelected = reactive<Record<string, string[]>>({})

// 切换选项
function toggleFilterOption(key: string, option: string) {
  if (!filterSelected[key]) filterSelected[key] = []
  const idx = filterSelected[key].indexOf(option)
  if (idx > -1) {
    filterSelected[key].splice(idx, 1)
  } else {
    filterSelected[key].push(option)
  }
}

// 清空筛选
function clearFilter() {
  Object.keys(filterSelected).forEach(k => filterSelected[k] = [])
}

// 确定筛选
function confirmFilter() {
  // 汇总所有选中的项
  const selectedTags: string[] = []
  filterOptions.forEach(group => {
    if (filterSelected[group.key] && filterSelected[group.key].length) {
      filterSelected[group.key].forEach(opt => {
        if (!opt.startsWith('不限')) selectedTags.push(opt)
      })
    }
  })
  tags.value = selectedTags
  showFilterDropdown.value = false
}
</script>

<style lang="scss" scoped>
.router-detail {
  background: #f7f7f7;
  min-height: 100vh;
}

.top-bar {
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  padding: 0 24rpx 24rpx 24rpx;
  position: relative;
}

.top-bar-row {
  display: flex;
  align-items: center;
  height: 80rpx;
  position: relative;
}

.title-select {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  z-index: 2;
  cursor: pointer;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}

.dropdown-arrow {
  margin-left: 8rpx;
  font-size: 22rpx;
  color: #e84138;
}

.spacer {
  flex: 1;
}

.subscribed-row {
  display: flex;
  align-items: center;
  margin-top: 18rpx;
}

.subscribed-tip {
  color: #999;
  font-size: 24rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.subscribed-scroll {
  white-space: nowrap;
  width: 0;
  flex: 1;

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }
}

.subscribed-list {
  display: flex;
  gap: 18rpx;
}

.subscribed-item {
  background: #f5f5f5;
  color: #e84138;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 8rpx 22rpx;
  margin-right: 8rpx;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-top: 18rpx;
  gap: 32rpx;
  position: relative;
}

.listen-status {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 26rpx;
  gap: 8rpx;
}

.filter-dropdowns {
  display: flex;
  gap: 18rpx;
  margin-left: auto;
}

.dropdown-btn {
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 18rpx;
}

.dropdown-arrow-mini {
  margin-left: 4rpx;
  font-size: 16rpx;
  color: #e84138;
  transition: transform 0.3s;

  &.rotate {
    transform: rotate(180deg);
  }
}

.tags-row {
  display: flex;
  align-items: center;
  margin-top: 18rpx;
}

.tags-scroll {
  white-space: nowrap;
  width: 0;
  flex: 1;

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }
}

.tags-list {
  display: flex;
  gap: 18rpx;
}

.tag {
  background: transparent;
  color: #e84138;
  font-size: 24rpx;
  border: 1rpx solid #e84138;
  border-radius: 24rpx;
  padding: 8rpx 22rpx;
  display: flex;
  align-items: center;
  margin-right: 8rpx;
}

.tag-close {
  margin-left: 6rpx;
  font-size: 18rpx;
  color: #bbb;
  cursor: pointer;
}

.clear-btn {
  color: #999;
  font-size: 24rpx;
  margin-left: 12rpx;
  cursor: pointer;
  flex-shrink: 0;
}

// 主体内容样式
.card {
  background: #fff;
  border-radius: 18rpx;
  margin: 24rpx 24rpx 0 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  padding: 0 0 24rpx 0;
}

.new-goods-card {
  background-color: transparent;
  padding-bottom: 0px;

  :deep(.uni-swipe) {
    margin-top: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);

    .uni-swipe_text--center {
      padding: 24rpx;
    }

    .goods-title {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;

      .arrow {
        margin: 0 12rpx;
        color: #999;
      }

      .type {
        margin-left: 12rpx;
        font-size: 24rpx;
        border-radius: 6rpx;
        padding: 2rpx 12rpx;

        &.carpool {
          background: #e8f5e9;
          color: #7ac251;
        }

        &.fixed {
          background: #fff2f0;
          color: #e84138;
        }
      }

      .tag {
        margin-left: 8rpx;
        font-size: 20rpx;
        background: #e6f0ff;
        color: #3a7afe;
        border-radius: 4rpx;
        padding: 2rpx 8rpx;

        &.blue {
          background: #e6f0ff;
          color: #3a7afe;
        }
      }
    }

    .goods-desc {
      font-size: 26rpx;
      color: #666;
      margin: 12rpx 0 0 0;
    }

    .goods-info {
      font-size: 24rpx;
      color: #999;
      margin: 8rpx 0 0 0;
    }

    .goods-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18rpx;

      .credit {
        font-size: 22rpx;
        color: #999;
        display: flex;
        align-items: center;

        u-icon {
          margin-right: 4rpx;
        }
      }

      .price-action {
        display: flex;
        align-items: center;

        .price {
          color: #e84138;
          font-size: 28rpx;
          font-weight: bold;
          margin-right: 12rpx;
        }
      }
    }
  }
}

.empty-recommend-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 18rpx;
  overflow: hidden;
}

.empty-top {
  width: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0 0 0;
}

.empty-img {
  width: 160rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 30rpx;
  color: #888;
  margin-bottom: 24rpx;
}

.empty-bottom {
  width: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0 18rpx 0;
}

.recommend-title-box {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 12rpx;
}

.recommend-title-line {
  flex: 1;
  height: 2rpx;
  background: #eee;
}

.recommend-title {
  font-size: 28rpx;
  color: #333;
  margin: 0 18rpx;
  font-weight: bold;
}

.recommend-desc {
  font-size: 24rpx;
  color: #aaa;
}

.dropdown-mask {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.dropdown-popup {
  position: fixed;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 0 0 18rpx 18rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  z-index: 1001;
  width: 100%;
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-section {
  padding: 32rpx 24rpx 0 24rpx;
}

.filter-group {
  margin-bottom: 32rpx;
}

.filter-group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 18rpx;
}

.filter-group-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 18rpx 18rpx;
}

.filter-option {
  background: #f5f5f5;
  color: #222;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 0;
  margin-bottom: 0;
  cursor: pointer;
  border: 2rpx solid transparent;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  justify-self: center;
  &.selected {
    background: #fff0f0;
    color: #e84138;
    border-color: #e84138;
  }
}

.filter-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 24rpx 24rpx 24rpx;
  border-top: 1rpx solid #f2f2f2;
  background: #fff;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.filter-clear {
  color: #999;
  font-size: 28rpx;
  padding: 12rpx 32rpx;
  border-radius: 24rpx;
  background: #f5f5f5;
  cursor: pointer;
}

.filter-confirm {
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 64rpx;
  border-radius: 24rpx;
  background: #e84138;
  cursor: pointer;
}

.dropdown-popup-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 36rpx 32rpx 48rpx;
  font-size: 30rpx;
  color: #222;
  border-bottom: 1rpx solid #f2f2f2;
  background: #fff;

  &:last-child {
    border-bottom: none;
  }
}

.dropdown-popup-label.active {
  color: #e84138;
  font-weight: bold;
}

.dropdown-popup-check {
  margin-left: 12rpx;
}
</style>
