<template>
  <view class="distribution-page" @click="handlePageClick">
    <page-meta :page-style="pageStyle"></page-meta>
    <!-- 顶部功能区 -->
    <view class="custom-top-bar" :style="{ paddingTop: statusBarHeight * 2 + 'rpx' }">
      <view class="custom-top-bar-inner">
        <view class="top-bar-row">
          <view class="top-bar-item left">
            <view class="icon-btn" @click="navigateToMessage">
              <u-icon name="chat" size="28"></u-icon>
              <text>消息</text>
            </view>
          </view>
          <view class="top-bar-item center">
            <text class="location">
              <span>{{ fromLocation }}</span>
              <span class="arrow">→</span>
              <span>{{ toLocation }}</span>
            </text>
          </view>
          <view class="top-bar-item right">
            <view class="icon-btn" @click="navigateToFindRecord">
              <u-icon name="search" size="28"></u-icon>
              <text>找货记录</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 筛选栏写到顶部功能区内部，作为第二行 -->
      <view class="filter-bar">
        <view class="row1">
          <view class="row1-left">
            <view class="select-item">
              <span :key="fromLocation">{{ fromLocation }}</span>
            </view>
            <view class="swap-icon" @click="swapLocation">
              <view class="iconfont icon-owner-jiaohuan"></view>
            </view>
            <view class="select-item">
              <span :key="toLocation">{{ toLocation }}</span>
            </view>
          </view>
          <view class="row1-right">
            <view class="sort-item" @click.stop="showSortDropdown = !showSortDropdown">
              <text>{{ selectedSort.substring(0, 2) }}</text>
            </view>
            <view class="filter-btn filter-action" @click.stop="showFilterDropdown = !showFilterDropdown">
              <text>筛选</text>
            </view>
          </view>
        </view>
        <!-- 智能排序下拉弹窗和遮罩层 -->
        <view v-if="showSortDropdown">
          <view class="dropdown-mask" :style="{ top: dropdownTop + 'rpx' }" @click.stop="showSortDropdown = false"></view>
          <view class="dropdown-popup" :style="{ top: dropdownTop + 'rpx' }">
            <view v-for="option in sortOptions" :key="option" class="dropdown-popup-item" @click.stop="selectSort(option)">
              <text :class="['dropdown-popup-label', selectedSort === option ? 'active' : '']">{{ option }}</text>
              <u-icon v-if="selectedSort === option" name="checkmark" size="22" color="#e84138" class="dropdown-popup-check" />
            </view>
          </view>
        </view>
        <!-- 筛选下拉弹窗和遮罩层 -->
        <view v-if="showFilterDropdown">
          <view class="dropdown-mask" :style="{ top: dropdownTop + 'rpx' }" @click.stop="showFilterDropdown = false"></view>
          <view class="dropdown-popup" :style="{ top: dropdownTop + 'rpx' }">
            <view class="filter-section">
              <view class="filter-group">
                <view class="filter-group-title">车型</view>
                <view class="filter-group-list">
                  <view v-for="item in vehicleTypes" :key="item.value" class="filter-option" :class="{ selected: selectedVehicleTypes.includes(item.value) }" @click.stop="toggleVehicleType(item.value)">
                    {{ item.label }}
                  </view>
                </view>
              </view>
              <view class="filter-group">
                <view class="filter-group-title">车长</view>
                <view class="filter-group-list">
                  <view v-for="item in vehicleLengths" :key="item.value" class="filter-option" :class="{ selected: selectedVehicleLengths.includes(item.value) }" @click.stop="toggleVehicleLength(item.value)">
                    {{ item.label }}
                  </view>
                </view>
              </view>
              <view class="filter-group">
                <view class="filter-group-title">载重</view>
                <view class="filter-group-list">
                  <view v-for="item in loadCapacities" :key="item.value" class="filter-option" :class="{ selected: selectedLoadCapacities.includes(item.value) }" @click.stop="toggleLoadCapacity(item.value)">
                    {{ item.label }}
                  </view>
                </view>
              </view>
            </view>
            <view class="filter-footer">
              <view class="filter-clear" @click.stop="clearFilters">清空</view>
              <view class="filter-confirm" @click.stop="confirmFilters">确定</view>
            </view>
          </view>
        </view>
        <view class="tags-row" v-if="tags.length > 0">
          <scroll-view class="tags-scroll" scroll-x show-scrollbar="false">
            <view class="tags-list">
              <view class="filter-tag" v-for="(tag, idx) in tags" :key="tag">
                {{ tag }}
                <u-icon name="close" size="12" color="#bbb" class="tag-close" @click="removeTag(idx)" />
              </view>
            </view>
          </scroll-view>
          <view class="clear-btn" v-if="tags.length" @click="clearTags">清空</view>
        </view>
      </view>
    </view>
    <!-- 主体内容用z-paging包裹，下拉刷新整个区域 -->
    <z-paging ref="pagingRef" v-model="listGoods" @query="handlePagingQuery" :fixed="false" :show-scrollbar="false" :auto-show-back-to-top="true" :use-page-scroll="true" auto-show-system-loading cell-height-mode="dynamic" :loading-more-enabled="true" :show-loading-more-no-more-view="true" :show-empty-view="true" :empty-view-text="'暂无新货'" :style="{ paddingTop: topBarTotalHeight + 'rpx' }">
      <template #default>
        <!-- 提示区域 -->
<!--        <view class="tip-card">
          <view class="tip-content">
            <view class="tip-left">
              <view class="tip-title">
                <u-icon name="file-text" size="32" color="#e84138"></u-icon>
                <text>补充证件 畅接海量货源！</text>
              </view>
              <view class="tip-desc">完善证件信息，提高接单成功率</view>
            </view>
            <button class="upload-btn">
              <u-icon name="arrow-right" size="24" color="#fff"></u-icon>
              <text>去上传</text>
            </button>
          </view>
        </view>-->
        <view class="no-data-tip">
          暂无符合条件的货源，清除筛选或者看看其他线路吧~
          <view class="recommend-text">推荐货源</view>
          <view class="no-more-text">该线路无更多符合条件货源，为您推荐</view>
        </view>
        <!-- 订阅新货卡片 -->
        <view class="card new-goods-card">
          <uni-swipe-action>
            <uni-swipe-action-item v-for="item in listGoods" :key="item.id" :isMoreLine="true" :right-options="options" :show="item.show" :disabled="item.disabled" @click.top="itemHandleClick(item)">
              <view class="goods-title">
                <text class="from">{{ item.loadingAddress.province }} {{ item.loadingAddress.city }}</text>
                <text class="arrow">→</text>
                <text class="to">{{ item.dischargeAddress.province }} {{ item.dischargeAddress.city }}</text>
<!--                <text v-if="!item.carpool" class="tag blue">满运宝</text>
                <text v-if="!item.carpool" class="tag blue">企业</text>-->
              </view>
              <view class="goods-desc">{{ item.desc }}</view>
              <view class="goods-info">{{ item.info }}</view>
              <view class="goods-footer">
<!--
                <view class="credit"><u-icon name="account" size="18"></u-icon>信用 {{ item.credit }}星 交易{{ item.deals }}</view>
-->
                <view class="price-action">
                  <text class="price">*****元/趟</text>
                  <u-button type="error" size="mini" shape="circle" text="去抢单" @click="grabOrder(item)"></u-button>
                </view>
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
// @ts-ignore
import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js';
import {getCarLengthList, getModel, getNewDistributionDriver} from '../../api/transport'

const statusBarHeight = ref(0)
const topBarTotalHeight = computed(() => {
  if (tags.value.length > 0) {
    return statusBarHeight.value * 2 + 124 * 2
  }
  return statusBarHeight.value * 2 + 85 * 2
})

const dropdownTop = computed(() => {
  return statusBarHeight.value * 2 + 85 * 2
})

const listGoods = ref<any[]>([])
const pagingRef = ref()

const sortOptions = reactive(['智能排序', '距离优先', '价格优先', '时间优先'])
const selectedSort = ref('智能排序')
const showSortDropdown = ref(false)
const showFilterDropdown = ref(false)

const pageStyle = ref('')

const selectSort = (option: string) => {
  selectedSort.value = option
  showSortDropdown.value = false
}

// 在 script 部分添加控制页面滚动的方法
const toggleScroll = (disable: boolean) => {
  // #ifdef H5
  if (disable) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = 'auto';
  }
  // #endif

  // #ifdef APP-PLUS
  if (disable) {
    pageStyle.value = 'overflow: hidden;'
  } else {
    pageStyle.value = 'overflow: scroll;'
  }
  // #endif
};

// 在 showSortDropdown 的 watch 中调用 toggleScroll
watch(showSortDropdown, (newVal) => {
  toggleScroll(newVal);
});

// 在 showFilterDropdown 的 watch 中调用 toggleScroll
watch(showFilterDropdown, (newVal) => {
  toggleScroll(newVal);
});

const fromLocation = ref('张家口')
const toLocation = ref('阳原')

// 筛选相关
const selectedVehicleTypes = ref<string[]>([])
const selectedVehicleLengths = ref<string[]>([])
const selectedLoadCapacities = ref<string[]>([])

let vehicleTypes = [
  { label: '厢式', value: 'box' },
  { label: '高栏', value: 'high' },
  { label: '平板', value: 'flat' },
  { label: '冷藏', value: 'cold' },
  { label: '危险品', value: 'danger' },
  { label: '集装箱', value: 'container' }
]

let vehicleLengths = [
  { label: '4.2米', value: '4.2' },
  { label: '6.8米', value: '6.8' },
  { label: '7.6米', value: '7.6' },
  { label: '9.6米', value: '9.6' },
  { label: '13米', value: '13' },
  { label: '17.5米', value: '17.5' }
]

const loadCapacities = [
  { label: '1吨以下', value: '1' },
  { label: '1-5吨', value: '1-5' },
  { label: '5-10吨', value: '5-10' },
  { label: '10-20吨', value: '10-20' },
  { label: '20吨以上', value: '20+' }
]
const getModelAll = async () => {
  vehicleTypes = []
  const result: any = await getModel()
  result.result.forEach((item: any) => {
    vehicleTypes.push({
      label: item.typename,
      value: item.id
    })
  })
}
getModelAll()
const getCarLengthListAll = async () => {
  vehicleLengths = []
  const result: any = await getCarLengthList()
  result.result.forEach((item: any) => {
    vehicleLengths.push({
      label: item.carTypeNames,
      value: item.id
    })
  })
}
getCarLengthListAll()
const toggleVehicleType = (value: string) => {
  const index = selectedVehicleTypes.value.indexOf(value)
  if (index > -1) {
    selectedVehicleTypes.value.splice(index, 1)
  } else {
    selectedVehicleTypes.value.push(value)
  }
}

const toggleVehicleLength = (value: string) => {
  const index = selectedVehicleLengths.value.indexOf(value)
  if (index > -1) {
    selectedVehicleLengths.value.splice(index, 1)
  } else {
    selectedVehicleLengths.value.push(value)
  }
}

const toggleLoadCapacity = (value: string) => {
  const index = selectedLoadCapacities.value.indexOf(value)
  if (index > -1) {
    selectedLoadCapacities.value.splice(index, 1)
  } else {
    selectedLoadCapacities.value.push(value)
  }
}

const clearFilters = () => {
  selectedVehicleTypes.value = []
  selectedVehicleLengths.value = []
  selectedLoadCapacities.value = []
}

const confirmFilters = () => {
  // 清空现有标签
  tags.value = []

  // 添加车型标签
  selectedVehicleTypes.value.forEach(type => {
    const option = vehicleTypes.find(item => item.value === type)
    if (option) {
      tags.value.push(option.label)
    }
  })

  // 添加车长标签
  selectedVehicleLengths.value.forEach(length => {
    const option = vehicleLengths.find(item => item.value === length)
    if (option) {
      tags.value.push(option.label)
    }
  })

  // 添加载重标签
  selectedLoadCapacities.value.forEach(capacity => {
    const option = loadCapacities.find(item => item.value === capacity)
    if (option) {
      tags.value.push(option.label)
    }
  })

  showFilterDropdown.value = false
  // TODO: 根据筛选条件重新加载数据
}

// 模拟异步分页请求
const queryList = async (page: number, pageSize: number) => {
  return new Promise<any[]>((resolve) => {
    getNewDistributionDriver().then((response: any) => {
      if (response.code == 700) {
        resolve(response.result)
      }
    })
  })
}
// 去抢单
const grabOrder = (item: any) => {
  driverGrabOrders({
    id: item.id
  }).then((response: any) => {
    if (response.code == 700) {

    }
  })
}
const handlePagingQuery = async (page: number, pageSize: number, from: any) => {
  console.log(123, from)
  const data = await queryList(page, pageSize)
  pagingRef.value.complete(data)
}

// 类似mixins，如果是页面滚动务必要写这一行，并传入当前ref绑定的paging，注意此处是paging，而非paging.value
useZPaging(pagingRef);

const options = reactive([
  {
    text: `今天`,
    text2: `不再展示`,
    style: {
      backgroundColor: 'gray',
      color: '#fff',
      fontSize: '26rpx'
    }
  }
]);

const itemHandleClick = (data: any) => {
  console.log(data);
};

const tags = ref<string[]>(['12.5/17.5米', '厢式', '高栏', '平板', '1.5方', '3.3方', '4.2方', '6.6方', '9.6方', '13.5方', '17.5方'])
const removeTag = (idx: number) => {
  tags.value.splice(idx, 1)
}
const clearTags = () => {
  tags.value = []
}

const swapLocation = () => {
  const temp = fromLocation.value
  fromLocation.value = toLocation.value
  toLocation.value = temp
}

const handlePageClick = (event: any) => {
  if (showSortDropdown.value || showFilterDropdown.value) {
    showSortDropdown.value = false;
    showFilterDropdown.value = false;
  }
};

const navigateToMessage = () => {
  uni.navigateTo({
    url: '/pageComDriver/common/message'
  });
};

const navigateToFindRecord = () => {
  uni.navigateTo({
    url: '/pageComDriver/common/find_record'
  });
};

onMounted(() => {
  // #ifdef APP-PLUS
  const sysInfo = uni.getSystemInfoSync()
  statusBarHeight.value = sysInfo.statusBarHeight || 0
  // #endif
})
</script>

<style lang="scss" scoped>
.distribution-page {
  background: #f7f7f7;
  min-height: 100vh;
}

.custom-top-bar {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-sizing: border-box;
  background: linear-gradient(90deg, #ffe6e6 0%, #fff 100%);
  box-shadow: 0 2rpx 8rpx rgba(232, 65, 56, 0.04);

  .custom-top-bar-inner {
    padding: 0 32rpx;
  }
}

.top-bar-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.top-bar-item {
  display: flex;
  align-items: center;
}

.top-bar-item.left {
  flex: 1;
  justify-content: flex-start;
}

.top-bar-item.center {
  flex: 2;
  justify-content: center;
  display: flex;
}

.top-bar-item.right {
  flex: 1;
  justify-content: flex-end;
}

.icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 22rpx;
  color: #333;

  text {
    margin-top: 6rpx;
  }
}

.location {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 400rpx;

  .location-item {
    display: inline-block;
    position: relative;
  }

  .arrow {
    font-size: 32rpx;
    color: #333;
    margin: 0 12rpx;
    font-weight: normal;
  }
}

.card {
  background: #fff;
  border-radius: 18rpx;
  margin: 24rpx 24rpx 0 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  padding: 0 0 24rpx 0;
}

.line-list {
  padding: 0 24rpx;

  .line-item {
    display: flex;
    align-items: center;
    margin-top: 18rpx;
    font-size: 28rpx;
    color: #333;
    background: linear-gradient(90deg, #ffe6e6 0%, #fff 100%);
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 12rpx;
    position: relative;

    .edit-actions {
      position: absolute;
      right: 20rpx;
      display: flex;
      gap: 20rpx;
      align-items: center;

      .edit-text {
        color: #666;
        font-size: 24rpx;
      }

      .delete-text {
        color: #e84138;
        font-size: 24rpx;
      }
    }

    .line {
      margin-left: 12rpx;
      margin-right: 8rpx;
    }

    .tag {
      background: #f5f5f5;
      color: #e84138;
      font-size: 22rpx;
      border-radius: 6rpx;
      padding: 2rpx 12rpx;
      margin-left: 8rpx;
    }

    .red-dot {
      background: #e84138;
      color: #fff;
      border-radius: 50%;
      font-size: 20rpx;
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8rpx;
    }
  }
}

.new-goods-card {
  background-color: transparent;
  padding-bottom: 0px;

  :deep(.uni-swipe) {
    margin-top: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);

    .uni-swipe_text--center {
      padding: 24rpx;
    }

    .goods-title {
      display: flex;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;

      .arrow {
        margin: 0 12rpx;
        color: #999;
      }

      .type {
        margin-left: 12rpx;
        font-size: 24rpx;
        border-radius: 6rpx;
        padding: 2rpx 12rpx;

        &.carpool {
          background: #e8f5e9;
          color: #7ac251;
        }

        &.fixed {
          background: #fff2f0;
          color: #e84138;
        }
      }

      .tag {
        margin-left: 8rpx;
        font-size: 20rpx;
        background: #e6f0ff;
        color: #3a7afe;
        border-radius: 4rpx;
        padding: 2rpx 8rpx;

        &.blue {
          background: #e6f0ff;
          color: #3a7afe;
        }
      }
    }

    .goods-desc {
      font-size: 26rpx;
      color: #666;
      margin: 12rpx 0 0 0;
    }

    .goods-info {
      font-size: 24rpx;
      color: #999;
      margin: 8rpx 0 0 0;
    }

    .goods-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18rpx;

      .credit {
        font-size: 22rpx;
        color: #999;
        display: flex;
        align-items: center;

        u-icon {
          margin-right: 4rpx;
        }
      }

      .price-action {
        display: flex;
        align-items: center;

        .price {
          color: #e84138;
          font-size: 28rpx;
          font-weight: bold;
          margin-right: 12rpx;
        }
      }
    }
  }
}

.filter-bar {
  width: 100%;
  z-index: 10;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  padding: 0 32rpx;
}

.row1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 0rpx;
  width: 100%;
  box-sizing: border-box;
}

.row1-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  margin-right: 24rpx;
}

.row1-right {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.select-item {
  position: relative;
  min-width: 120rpx;
  max-width: 200rpx;
  text-align: center;
  overflow: hidden;
  flex-shrink: 0;

  span {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 26rpx;
    color: #333;
  }
}

.swap-icon {
  margin: 0 24rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8rpx;
}

.sort-item,
.filter-btn {
  font-size: 26rpx;
  padding: 8rpx 24rpx;
  white-space: nowrap;
  border-radius: 28rpx;
  background: #f5f5f5;
  color: #666;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
  }
}

.filter-btn.filter-action {
  background: #fff2f0;
  color: #e84138;
}

.tags-row {
  display: flex;
  align-items: center;
  padding: 12rpx 0rpx;
}

.tags-scroll {
  white-space: nowrap;
  width: 0;
  flex: 1;

  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }
}

.tags-list {
  display: flex;
  gap: 18rpx;
}

.filter-tag {
  background: transparent;
  color: #e84138;
  font-size: 24rpx;
  border: 1rpx solid #e84138;
  border-radius: 24rpx;
  padding: 8rpx 22rpx;
  display: flex;
  align-items: center;
  margin-right: 8rpx;
}

.tag-close {
  margin-left: 6rpx;
  font-size: 12rpx;
  color: #bbb;
  cursor: pointer;
}

.clear-btn {
  color: #999;
  font-size: 24rpx;
  margin-left: 12rpx;
  cursor: pointer;
  flex-shrink: 0;
}

.tip-card {
  background: #fff;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.tip-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.tip-left {
  flex: 1;
  margin-right: 24rpx;
}

.tip-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;

  text {
    font-weight: bold;
  }
}

.tip-desc {
  font-size: 24rpx;
  color: #999;
}

.upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  background: #e84138;
  color: #fff;
  font-size: 24rpx;
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(232, 65, 56, 0.2);
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(232, 65, 56, 0.15);
  }
}

.no-data-tip {
  padding-top: 24rpx;
  font-size: 26rpx;
  color: #999;
  text-align: center;
  background: transparent;
}

.recommend-text {
  margin-top: 12rpx;
  color: #333;
  font-weight: bold;
  position: relative;
  display: inline-block;
  padding: 0 20rpx;
}

.recommend-text::before,
.recommend-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 40rpx;
  height: 1rpx;
  background-color: #999;
}

.recommend-text::before {
  left: -60rpx;
}

.recommend-text::after {
  right: -60rpx;
}

.no-more-text {
  margin-top: 8rpx;
  color: #666;
}

.dropdown-mask {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.dropdown-popup {
  position: fixed;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 0 0 18rpx 18rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  z-index: 1001;
  width: 100%;
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-section {
  padding: 32rpx 24rpx 0 24rpx;
}

.filter-group {
  margin-bottom: 32rpx;
}

.filter-group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 18rpx;
}

.filter-group-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 18rpx 18rpx;
}

.filter-option {
  background: #f5f5f5;
  color: #222;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 0;
  margin-bottom: 0;
  cursor: pointer;
  border: 2rpx solid transparent;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  justify-self: center;

  &.selected {
    background: #fff0f0;
    color: #e84138;
    border-color: #e84138;
  }
}

.filter-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 24rpx 24rpx 24rpx;
  border-top: 1rpx solid #f2f2f2;
  background: #fff;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.filter-clear {
  color: #999;
  font-size: 28rpx;
  padding: 12rpx 32rpx;
  border-radius: 24rpx;
  background: #f5f5f5;
  cursor: pointer;
}

.filter-confirm {
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 64rpx;
  border-radius: 24rpx;
  background: #e84138;
  cursor: pointer;
}

.dropdown-popup-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 36rpx 32rpx 48rpx;
  font-size: 30rpx;
  color: #222;
  border-bottom: 1rpx solid #f2f2f2;
  background: #fff;

  &:last-child {
    border-bottom: none;
  }
}

.dropdown-popup-label.active {
  color: #e84138;
  font-weight: bold;
}

.dropdown-popup-check {
  margin-left: 12rpx;
}
</style>