<template>
	<view>
		这是支付页面
		<view class="">
			订单号：{{ orderId }}
			金额：{{ money }}
		</view>
	</view>
	<view class="pay-button" @click="pay">
		支付
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { orderPay } from "../../api/transport";
import { onLoad } from '@dcloudio/uni-app';
const money: any = ref(0.00)
const orderId: any = ref(0)
onLoad((options)=>{
	money.value = options.money
	orderId.value = options.orderId
})
const pay = () => {
	orderPay({
		openid: "xxxxxxxxxx",
		moeny: money.value,
		orderNum: orderId.value
	}).then((response: any) => {
		if (response.code == 700) {
			uni.showToast({
			  title: '支付成功',
			  icon: 'success',
			  duration: 2000
			});
			uni.navigateTo({
				url: "/pages/index/index"
			});
		}
	})
}
</script>

<style>
.pay-button {
	margin: 100px auto;
	width: 200px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #22ac38;
	color: #fff;
	border-radius: 10px;
}
</style>
