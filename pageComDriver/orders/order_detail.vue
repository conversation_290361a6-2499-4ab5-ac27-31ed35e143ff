<template>
  <view class="order-detail">
    <!-- 头部状态栏 -->
    <view class="header-bg"></view>
    <view
      class="header-status"
      :style="{ paddingTop: systemInfo.safeAreaInsets.top * 2 + 'rpx' }"
    >
      <view class="back-btn" @click="goBack">
        <view class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></view>
      </view>
      <view class="status-info">
        <text class="status-text">{{
          getOrderStatusText(orderDetail.orderStatus)
        }}</text>
      </view>
      <!--      <view class="header-actions">
        <view class="action-item">
          <view class="iconfont icon-owner-lianxikefu"></view>
          <text>清洁宝</text>
        </view>
        <view class="action-item">
          <view class="iconfont icon-owner-tishi"></view>
          <text>投诉</text>
        </view>
      </view>-->
    </view>

    <!-- 主要内容区域 -->
    <view
      class="main-content"
      :style="{
        paddingTop: systemInfo.safeAreaInsets.top * 2 + 80 + 20 + 'rpx',
      }"
    >
      <!-- 订单信息卡片 -->
      <view class="card order-info-card">
        <view class="order-header">
          <view class="order-title">
            <text class="title-text">订单信息</text>
            <!--            <view class="enterprise-tag">
              <text>蓝运宝</text>
              <text>企业</text>
            </view>-->
          </view>
          <view class="order-detail-btn" @click="goToOrderInfo">
            <text>订单明细</text>
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>
      </view>

      <!-- 路线信息 -->
      <view class="card route-card">
        <!-- 装货地 -->
        <view class="route-item pickup-point">
          <view class="route-tag pickup-tag">
            <text>装</text>
          </view>
          <view class="route-info">
            <text class="route-title">{{
              orderDetail.loadingAddressName
            }}</text>
            <text class="route-address"
              >{{ orderDetail.loadingAddress?.province }}
              {{ orderDetail.loadingAddress?.county }}</text
            >
          </view>
          <view class="route-action" @click="goToPickup">
            <view class="iconfont icon-owner-youjiantou"></view>
            <text>去装货地</text>
          </view>
        </view>

        <!-- 卸货地 -->
        <view class="route-item delivery-point">
          <view class="route-tag delivery-tag">
            <text>卸</text>
          </view>
          <view class="route-info">
            <text class="route-title">{{
              orderDetail?.unloadingAddressName
            }}</text>
            <text class="route-address"
              >{{ orderDetail.dischargeAddress?.province }}
              {{ orderDetail.dischargeAddress?.county }}</text
            >
          </view>
          <view class="route-action" @click="goToDelivery">
            <view class="iconfont icon-owner-youjiantou"></view>
            <text>去卸货地</text>
          </view>
        </view>

        <view class="route-time">
          <view class="iconfont icon-owner-time"></view>
          <text
            >{{ formatDate(orderDetail?.loadingDate) }}
            {{ orderDetail.earliestLoadingTime }}装货</text
          >
        </view>
      </view>

      <!-- 费用明细 -->
      <view class="card fee-card">
        <view class="fee-item" @click="openPaymentDialog('deposit')">
          <view class="fee-label">
            <text class="fee-tag">订金</text>
            <text class="fee-amount">¥{{ orderDetail.depositAmount }}</text>
            <text class="fee-desc">{{
              checkStatus(orderDetail.orderStatus)
            }}</text>
          </view>
          <view class="fee-action">
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>

        <!--        <view class="fee-item" @click="openPaymentDialog('freight')">
          <view class="fee-label">
            <text class="fee-tag">运费</text>
            <text class="fee-amount">¥2300</text>
            <text class="fee-desc">待货主提交支付申请</text>
          </view>
          <view class="fee-action">
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>-->

        <view class="fee-detail-btn" @click="goToCostInfo">
          <text>费用明细</text>
          <view class="detail-icon">
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>
      </view>

      <!-- 保险信息 -->
      <!--      <view class="card insurance-card">
        <view class="insurance-header">
          <text class="insurance-title">保险中</text>
          <text class="insurance-desc">购买省心套餐，即可享放空保障。</text>
          <view class="insurance-action">
            <text>去购买</text>
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>
      </view>-->

      <!-- 订单图片 -->
      <view class="card photos-card" v-if="checkOrderPic">
        <view class="photos-header">
          <text class="photos-title">订单图片</text>
          <text class="photos-desc">装货、卸货、回单</text>
          <view class="photos-action" @click="goToOrderPic">
            <text>编辑/查看</text>
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部固定区域 -->
    <view class="bottom-fixed-container">
      <!-- 货主信息 -->
      <view class="driver-card">
        <view class="driver-info">
          <view class="driver-avatar">
            <image :src="orderDetail.shippersUrl" class="head"></image>
          </view>
          <view class="driver-details">
            <view class="driver-name">
              <text>{{ orderDetail?.shippersName || '--' }}</text>
              <view class="phone-icon" @click="contactOwner">
                <view class="iconfont icon-owner-dianhua"></view>
              </view>
              <!--
              <text class="driver-rating">信用4.5星</text>
-->
            </view>
            <text class="driver-desc"
              >{{ orderDetail.cargoName }} {{ orderDetail.vehicleLength }}
              {{ orderDetail.vehicleType }}</text
            >
          </view>
        </view>
      </view>

      <!-- 消息提示 -->
      <!-- <view class="message-card">
        <view class="message-content">
          <view class="message-icon">
            <view class="iconfont icon-owner-lianxikefu"></view>
          </view>
          <text class="message-text">您有1条未读消息</text>
          <view class="message-action">
            <view class="action-btn" @click="replyMessage">
              <text>回复</text>
            </view>
            <view class="action-btn contact-btn" @click="contactOwner">
              <view class="iconfont icon-owner-dianhua"></view>
              <text>联系货主</text>
            </view>
          </view>
        </view>
      </view> -->

      <!-- 底部操作栏 -->
      <view class="bottom-actions">
        <!-- 更多操作展开区域 -->
        <view class="more-actions-panel" v-show="isMoreActionsExpanded">
          <view class="more-actions-buttons">
            <view class="more-action-btn" @click="applyDeductDeposit">
              <text class="more-btn-text">申请扣订金</text>
            </view>
            <view class="more-action-btn" @click="agreeRefund">
              <text class="more-btn-text">同意退还</text>
            </view>
            <view class="more-action-btn" @click="rejectRefund">
              <text class="more-btn-text">拒绝退还</text>
            </view>
            <view class="more-action-btn" @click="applyArbitration">
              <text class="more-btn-text">申请仲裁</text>
            </view>
            <view class="more-action-btn" @click="withdrawDeductDeposit">
              <text class="more-btn-text">撤回扣订金</text>
            </view>
          </view>
        </view>

        <view class="action-buttons">
          <!-- 更多按钮 -->
          <view
            class="action-btn secondary more-btn"
            @click="toggleMoreActions"
          >
            <text>{{ isMoreActionsExpanded ? '收起' : '更多' }}</text>
          </view>

          <view
            class="action-btn secondary"
            v-if="checkUpdateOrderStatus(6)"
            @click="agreePriceEdit"
          >
            <text>同意改价</text>
          </view>
          <view
            class="action-btn secondary"
            v-if="checkUpdateOrderStatus(6)"
            @click="rejectPriceEdit"
          >
            <text>拒绝改价</text>
          </view>
          <view
            class="action-btn primary"
            v-if="checkUpdateOrderStatus(1)"
            @tap="arriveLoadingPoint(5)"
          >
            <text>出发装货地</text>
          </view>
          <view
            class="action-btn primary"
            v-if="checkUpdateOrderStatus(2)"
            @tap="arriveLoadingPoint(6)"
          >
            <text>已到达装货地</text>
          </view>
          <view
            class="action-btn primary"
            v-if="checkUpdateOrderStatus(3)"
            @tap="arriveLoadingPoint(7)"
          >
            <text>出发卸货地</text>
          </view>
          <view
            class="action-btn primary"
            v-if="checkUpdateOrderStatus(4)"
            @tap="arriveLoadingPoint(8)"
          >
            <text>到达卸货地</text>
          </view>
          <view
            class="action-btn primary"
            v-if="checkUpdateOrderStatus(5)"
            @tap="arriveLoadingPoint(9)"
          >
            <text>确认完成</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 钱款去向弹窗 -->
    <cu-payment-dialog
      ref="paymentDialog"
      :deposit-info="depositInfo"
      :freight-info="freightInfo"
      @close="handlePaymentDialogClose"
      @offline-payment="handleOfflinePayment"
      @collect-freight="handleCollectFreight"
    />

    <!-- 同意改价确认弹窗 -->
    <cu-confirm-dialog
      ref="agreePriceEditDialog"
      :title="agreePriceEditDialogData.title"
      :content="agreePriceEditDialogData.content"
      :cancel-text="agreePriceEditDialogData.cancelText"
      :confirm-text="agreePriceEditDialogData.confirmText"
      @cancel="handleAgreePriceEditCancel"
      @confirm="handleAgreePriceEditConfirm"
    />

    <!-- 拒绝改价确认弹窗 -->
    <cu-confirm-dialog
      ref="rejectPriceEditDialog"
      :title="rejectPriceEditDialogData.title"
      :content="rejectPriceEditDialogData.content"
      :cancel-text="rejectPriceEditDialogData.cancelText"
      :confirm-text="rejectPriceEditDialogData.confirmText"
      @cancel="handleRejectPriceEditCancel"
      @confirm="handleRejectPriceEditConfirm"
    />

    <!-- 申请扣订金确认弹窗 -->
    <cu-confirm-dialog
      ref="applyDeductDepositDialog"
      :title="applyDeductDepositDialogData.title"
      :content="applyDeductDepositDialogData.content"
      :cancel-text="applyDeductDepositDialogData.cancelText"
      :confirm-text="applyDeductDepositDialogData.confirmText"
      @cancel="handleApplyDeductDepositCancel"
      @confirm="handleApplyDeductDepositConfirm"
    />

    <!-- 同意退还确认弹窗 -->
    <cu-confirm-dialog
      ref="agreeRefundDialog"
      :title="agreeRefundDialogData.title"
      :content="agreeRefundDialogData.content"
      :cancel-text="agreeRefundDialogData.cancelText"
      :confirm-text="agreeRefundDialogData.confirmText"
      @cancel="handleAgreeRefundCancel"
      @confirm="handleAgreeRefundConfirm"
    />

    <!-- 拒绝退还确认弹窗 -->
    <cu-confirm-dialog
      ref="rejectRefundDialog"
      :title="rejectRefundDialogData.title"
      :content="rejectRefundDialogData.content"
      :cancel-text="rejectRefundDialogData.cancelText"
      :confirm-text="rejectRefundDialogData.confirmText"
      @cancel="handleRejectRefundCancel"
      @confirm="handleRejectRefundConfirm"
    />

    <!-- 申请仲裁确认弹窗 -->
    <cu-confirm-dialog
      ref="applyArbitrationDialog"
      :title="applyArbitrationDialogData.title"
      :content="applyArbitrationDialogData.content"
      :cancel-text="applyArbitrationDialogData.cancelText"
      :confirm-text="applyArbitrationDialogData.confirmText"
      @cancel="handleApplyArbitrationCancel"
      @confirm="handleApplyArbitrationConfirm"
    />

    <!-- 撤回扣订金确认弹窗 -->
    <cu-confirm-dialog
      ref="withdrawDeductDepositDialog"
      :title="withdrawDeductDepositDialogData.title"
      :content="withdrawDeductDepositDialogData.content"
      :cancel-text="withdrawDeductDepositDialogData.cancelText"
      :confirm-text="withdrawDeductDepositDialogData.confirmText"
      @cancel="handleWithdrawDeductDepositCancel"
      @confirm="handleWithdrawDeductDepositConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUpdated, nextTick, watch } from 'vue'
import { getOrderInfoById, editOrderInfo } from '../../api/transport'
import dayjs from 'dayjs'
import { makeCall, validatePhoneNumber } from '@/utils/phone'
import { onShow } from '@dcloudio/uni-app'
import CuConfirmDialog from '../../components/cu-confirm-dialog/cu-confirm-dialog.vue'

const systemInfo: any = uni.getSystemInfoSync()
const heightHeaderBg = systemInfo.safeAreaInsets.top * 2 + 80 + 50 + 'rpx'
const heightHeaderStatus = systemInfo.safeAreaInsets.top * 2 + 80 + 20 + 'rpx'

// 底部固定区域高度
const bottomHeight = ref('360rpx') // 默认值

// 钱款去向弹窗引用
const paymentDialog = ref(null)

// 订金信息
const depositInfo = ref({
  amount: 0,
  timeline: [
    { title: '成功支付给货主', time: '2025.03.14 12:22' },
    { title: '支付处理中', time: '2025.03.14 12:22' },
    {
      title: '已到达订金自动流转时间，发起给货主¥100',
      time: '2025.03.14 12:22',
    },
    { title: '您已支付订金，平台监管中', time: '2025.03.12 13:14' },
  ],
})

// 同意改价确认弹窗引用
const agreePriceEditDialog = ref(null)

// 同意改价弹窗数据
const agreePriceEditDialogData = ref({
  title: '确认同意改价',
  content: '',
  cancelText: '取消',
  confirmText: '确认同意',
})

// 拒绝改价确认弹窗引用
const rejectPriceEditDialog = ref(null)

// 拒绝改价弹窗数据
const rejectPriceEditDialogData = ref({
  title: '确认拒绝改价',
  content: '',
  cancelText: '取消',
  confirmText: '确认拒绝',
})

// 更多按钮展开状态
const isMoreActionsExpanded = ref(false)

// 申请扣订金确认弹窗引用和数据
const applyDeductDepositDialog = ref(null)
const applyDeductDepositDialogData = ref({
  title: '申请扣订金',
  content: '确定要申请扣订金吗？',
  cancelText: '取消',
  confirmText: '确认申请',
})

// 同意退还确认弹窗引用和数据
const agreeRefundDialog = ref(null)
const agreeRefundDialogData = ref({
  title: '同意退还',
  content: '确定要同意退还订金吗？',
  cancelText: '取消',
  confirmText: '确认同意',
})

// 拒绝退还确认弹窗引用和数据
const rejectRefundDialog = ref(null)
const rejectRefundDialogData = ref({
  title: '拒绝退还',
  content: '确定要拒绝退还订金吗？',
  cancelText: '取消',
  confirmText: '确认拒绝',
})

// 申请仲裁确认弹窗引用和数据
const applyArbitrationDialog = ref(null)
const applyArbitrationDialogData = ref({
  title: '申请仲裁',
  content: '确定要申请仲裁吗？',
  cancelText: '取消',
  confirmText: '确认申请',
})

// 撤回扣订金确认弹窗引用和数据
const withdrawDeductDepositDialog = ref(null)
const withdrawDeductDepositDialogData = ref({
  title: '撤回扣订金',
  content: '确定要撤回扣订金申请吗？',
  cancelText: '取消',
  confirmText: '确认撤回',
})

// 运费信息
const freightInfo = ref({
  amount: '2400',
  timeline: [
    { title: '待货主支付运费', time: '2025.03.14 12:22' },
    { title: '货主提交支付申请', time: '2025.03.14 10:15' },
    { title: '司机确认到达卸货地', time: '2025.03.14 09:30' },
    { title: '开始运输', time: '2025.03.13 14:00' },
  ],
})

// 计算底部固定区域高度
const calculateBottomHeight = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery()
    query
      .select('.bottom-fixed-container')
      .boundingClientRect((rect: any) => {
        if (rect && rect.height) {
          // 获取系统信息用于准确的px到rpx转换
          const systemInfo = uni.getSystemInfoSync()
          const pixelRatio = systemInfo.pixelRatio || 2

          // 更准确的px到rpx转换：rpx = px * 750 / 屏幕宽度
          const screenWidth = systemInfo.screenWidth || 375
          const heightInRpx = Math.ceil((rect.height * 750) / screenWidth)

          // 添加一些额外的安全边距，确保内容不被遮挡
          const safeMargin = 20 // 20rpx的安全边距
          bottomHeight.value = heightInRpx + safeMargin + 'rpx'

          console.log('底部区域高度计算:', {
            原始高度: rect.height + 'px',
            转换后高度: heightInRpx + 'rpx',
            最终高度: bottomHeight.value,
            屏幕宽度: screenWidth,
            像素比: pixelRatio,
          })
        } else {
          console.warn('无法获取底部固定区域的尺寸信息')
          // 如果无法获取尺寸，使用一个合理的默认值
          bottomHeight.value = '400rpx'
        }
      })
      .exec()
  })
}

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: any
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖的计算函数
const debouncedCalculateHeight = debounce(calculateBottomHeight, 300)

// 强制重新计算底部高度（用于调试或手动触发）
const forceRecalculateHeight = () => {
  console.log('强制重新计算底部高度')
  calculateBottomHeight()
}

// 暴露给全局，方便调试
if (typeof window !== 'undefined') {
  window.forceRecalculateHeight = forceRecalculateHeight
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 回复消息
const replyMessage = () => {
  // 跳转到消息回复页面或打开消息对话框
  console.log('回复消息')
  uni.showToast({
    title: '打开消息回复',
    icon: 'none',
  })
}

// 发送短信功能
const sendSMS = (phoneNumber: string, message: string = '') => {
  console.log('准备发送短信:', phoneNumber, message)

  // 验证电话号码
  if (!validatePhoneNumber(phoneNumber)) {
    console.error('电话号码格式不正确:', phoneNumber)
    uni.showToast({
      title: '电话号码格式不正确',
      icon: 'none',
    })
    return
  }

  // 检查平台支持
  // #ifdef APP-PLUS
  console.log('当前平台: APP-PLUS - 发送短信')

  // 方法1: 使用plus.messaging (推荐)
  if (typeof plus !== 'undefined' && plus.messaging) {
    const msg = plus.messaging.createMessage(plus.messaging.TYPE_SMS)
    msg.to = [phoneNumber]
    msg.body = message || '您好，这是来自货运司机的消息。'

    plus.messaging.sendMessage(
      msg,
      () => {
        console.log('短信发送成功')
        uni.showToast({
          title: '短信发送成功',
          icon: 'success',
        })
      },
      (err) => {
        console.error('短信发送失败:', err)

        // 如果plus.messaging失败，尝试URL scheme方式
        const smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(
          message || '您好，这是来自货运司机的消息。'
        )}`
        plus.runtime.openURL(smsUrl, (error) => {
          console.error('打开短信应用失败:', error)
          uni.showModal({
            title: '发送短信失败',
            content: '无法打开短信应用，请手动发送短信到: ' + phoneNumber,
            showCancel: false,
          })
        })
      }
    )
  } else {
    // 方法2: 使用URL scheme
    const smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(
      message || '您好，这是来自货运司机的消息。'
    )}`
    plus.runtime.openURL(smsUrl, (error) => {
      console.error('打开短信应用失败:', error)
      uni.showModal({
        title: '发送短信失败',
        content: '无法打开短信应用，请手动发送短信到: ' + phoneNumber,
        showCancel: false,
      })
    })
  }
  // #endif

  // #ifdef H5
  console.log('当前平台: H5 - 发送短信')
  const smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(
    message || '您好，这是来自货运司机的消息。'
  )}`
  window.location.href = smsUrl
  // #endif

  // #ifdef MP
  console.log('当前平台: 小程序 - 发送短信')
  uni.showModal({
    title: '提示',
    content:
      '小程序环境不支持直接发送短信，请手动发送短信到: ' +
      phoneNumber +
      '\n\n内容: ' +
      (message || '您好，这是来自货运司机的消息。'),
    showCancel: false,
  })
  // #endif
}

// 联系货主
const contactOwner = () => {
  console.log('联系货主')

  // 这里应该从订单数据中获取实际的货主电话号码
  const ownerPhone = '13800138000' // 临时测试号码，实际应该从props或API获取

  uni.showActionSheet({
    itemList: ['拨打电话', '发送短信'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 拨打电话
        makeCall(ownerPhone)
      } else if (res.tapIndex === 1) {
        // 发送短信
        const defaultMessage =
          '您好，我是负责您订单的司机，有事情需要和您沟通。'
        sendSMS(ownerPhone, defaultMessage)
      }
    },
    fail: (err) => {
      console.error('显示操作菜单失败:', err)
    },
  })
}

// TODO: 这里先写个弹窗，其实只有高德地图，后边把这块注释了，使用之前写的先跳地图页面规划路线，然后跳转高德app
const goToPickup = () => {
  console.log('去装货地')
  // 打开地图导航到装货地
  uni.showActionSheet({
    itemList: ['高德地图', '百度地图', '腾讯地图'],
    success: (res) => {
      const pickupAddress = '天津市 滨海新区 塘黄公路'
      let mapUrl = ''

      if (res.tapIndex === 0) {
        // 高德地图
        mapUrl = `amapuri://route/plan/?dlat=&dlon=&dname=${encodeURIComponent(
          pickupAddress
        )}&dev=0&t=0`
      } else if (res.tapIndex === 1) {
        // 百度地图
        mapUrl = `baidumap://map/direction?destination=${encodeURIComponent(
          pickupAddress
        )}&mode=driving`
      } else if (res.tapIndex === 2) {
        // 腾讯地图
        mapUrl = `qqmap://map/routeplan?type=drive&to=${encodeURIComponent(
          pickupAddress
        )}`
      }

      // 尝试打开地图应用
      plus.runtime.openURL(mapUrl, (err) => {
        uni.showToast({
          title: '请安装对应的地图应用',
          icon: 'none',
        })
      })
    },
  })
}

// 去卸货地
const goToDelivery = () => {
  console.log('去卸货地')
  // 打开地图导航到卸货地
  uni.showActionSheet({
    itemList: ['高德地图', '百度地图', '腾讯地图'],
    success: (res) => {
      const deliveryAddress = '潍坊市 寒亭区 凯祥物流'
      let mapUrl = ''

      if (res.tapIndex === 0) {
        // 高德地图
        mapUrl = `amapuri://route/plan/?dlat=&dlon=&dname=${encodeURIComponent(
          deliveryAddress
        )}&dev=0&t=0`
      } else if (res.tapIndex === 1) {
        // 百度地图
        mapUrl = `baidumap://map/direction?destination=${encodeURIComponent(
          deliveryAddress
        )}&mode=driving`
      } else if (res.tapIndex === 2) {
        // 腾讯地图
        mapUrl = `qqmap://map/routeplan?type=drive&to=${encodeURIComponent(
          deliveryAddress
        )}`
      }

      // 尝试打开地图应用
      plus.runtime.openURL(mapUrl, (err) => {
        uni.showToast({
          title: '请安装对应的地图应用',
          icon: 'none',
        })
      })
    },
  })
}

// 去订单信息
const goToOrderInfo = () => {
  uni.navigateTo({
    url: '/pageComDriver/orders/order_info',
    success: function (res) {
      res.eventChannel.emit('orderInfo', {
        orderId: orderDetail.value.id,
        orderTime: orderDetail.value.orderTime,
        cargoName: orderDetail.value.cargoName,
        vehicleLength: orderDetail.value.vehicleLength,
        vehicleType: orderDetail.value.vehicleType,
        estimatedDistance: estimatedDistance.value,
      })
    },
  })
}

// 去费用明细
const goToCostInfo = () => {
  uni.navigateTo({
    url: '/pageComDriver/orders/cost_info',
    success: function (res) {
      res.eventChannel.emit('costInfo', {
        depositAmount: orderDetail.value.depositAmount,
        bidAmount: orderDetail.value.bidAmount,
      })
    },
  })
}

// 订金状态
const checkStatus = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '待发起退订金',
    2: '司机已发起退订金',
    3: '货主同意退订金',
    4: '货主拒绝退订金',
    5: '订金冻结',
    6: '货主发起扣订金',
    7: '司机同意扣订金',
    8: '司机拒绝扣订金',
    9: '仲裁受理完毕',
    10: '订金已退还司机',
    11: '订金已支付货主',
  }
  return statusMap[status] || '--'
}

// 去订单图片
const goToOrderPic = () => {
  uni.navigateTo({
    url: '/pageComDriver/orders/order_pic',
    success: function (res) {
      res.eventChannel.emit('orderInfo', {
        orderId: orderDetail.value.id,
        orderStatus: orderDetail.value.orderStatus,
        loadingImages: orderDetail.value.loadingImages || '',
        unloadingImages: orderDetail.value.unloadingImages || '',
        deliveryProofImages: orderDetail.value.deliveryProofImages || '',
      })
    },
  })
}

// 打开钱款去向弹窗
const openPaymentDialog = (tab: string) => {
  depositInfo.value.amount = orderDetail.value.depositAmount
  depositInfo.value.timeline = orderDetail.value.depositProcesses
  paymentDialog.value?.open(tab)
}

// 处理钱款去向弹窗关闭
const handlePaymentDialogClose = () => {
  console.log('钱款去向弹窗关闭')
}

// 处理线下收款
const handleOfflinePayment = () => {
  console.log('处理线下收款')
  uni.showToast({
    title: '已确认线下收款',
    icon: 'success',
  })
}

// 处理收运费
const handleCollectFreight = () => {
  console.log('处理收运费')
  uni.showToast({
    title: '发起收运费申请',
    icon: 'success',
  })
}
const formatDate = (date: string | number | Date) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}

const getOrderStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '已接单',
    2: '改价待确认',
    3: '改价已确认',
    4: '改价已拒绝',
    5: '到达装货地',
    6: '装货中',
    7: '运输中',
    8: '到达卸货地',
    9: '已完成待确认',
    10: '已完成',
    99: '已完成',
    98: '已取消',
  }
  return statusMap[status] || '未知状态'
}
const orderDetail = ref({})
// 存储订单ID，用于在onShow中重新获取数据
const currentOrderId = ref('')

// 获取订单详情的函数
const fetchOrderDetail = async (id: string) => {
  try {
    // 调用接口获取订单详情
    const response: any = await getOrderInfoById(id)
    uni.hideLoading()
    if (response.code === 700) {
      orderDetail.value = response.result
      calculateDistance()
      console.log(12332, orderDetail.value)

      // 数据加载完成后重新计算底部高度
      nextTick(() => {
        setTimeout(() => {
          calculateBottomHeight()
        }, 300)
      })
    } else {
      console.error('获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
  }
}
// 计算距离的函数
const estimatedDistance = ref(null)
const calculateDistance = () => {
  const startLng = orderDetail.value.loadingLongitude
  const startLat = orderDetail.value.loadingLatitude
  const endLng = orderDetail.value.unloadingLongitude
  const endLat = orderDetail.value.unloadingLatitude
  const R = 6371 // 地球半径，单位：公里
  const dLat = (endLat - startLat) * (Math.PI / 180)
  const dLng = (endLng - startLng) * (Math.PI / 180)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(startLat * (Math.PI / 180)) *
      Math.cos(endLat * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c
  estimatedDistance.value = distance.toFixed(1)
}

// 已到达装货地
const arriveLoadingPoint = async (num: number) => {
  try {
    const result: any = await editOrderInfo({
      id: orderDetail.value.id,
      orderStatus: num,
    })
    uni.hideLoading()
    if (result.code == 700) {
      uni.showToast({
        title: '已到达装货地',
        icon: 'success',
        duration: 1000,
      })
      setTimeout(() => {
        goBack()
      }, 1000)
    } else {
      uni.showToast({
        title: result.msg || '操作失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 同意改价
const agreePriceEdit = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  // 设置弹窗内容，显示价格对比
  const originalPrice = orderDetail.value.bidAmount || '0'
  const newPrice = orderDetail.value.notBidAmount || '0'

  agreePriceEditDialogData.value.content = `确定同意此次改价吗？\n\n原始价格：¥${originalPrice}元\n改价后：¥${newPrice}元`

  console.log('打开同意改价确认弹窗')

  // 打开同意改价确认弹窗
  agreePriceEditDialog.value?.open()
}

// 处理同意改价取消
const handleAgreePriceEditCancel = () => {
  console.log('用户取消同意改价')
}

// 处理同意改价确认
const handleAgreePriceEditConfirm = async () => {
  try {
    console.log('确认同意改价，订单ID:', orderDetail.value.id)

    // 显示加载提示
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用同意改价接口
    const result: any = await editOrderInfo({
      id: orderDetail.value.id,
      orderStatus: 3, // 改价已确认
      bidAmount: orderDetail.value.notBidAmount,
    })

    uni.hideLoading()

    if (result.code == 700) {
      uni.showToast({
        title: '已同意改价',
        icon: 'success',
        duration: 1000,
      })

      // 刷新订单详情数据
      setTimeout(() => {
        fetchOrderDetail(currentOrderId.value)
      }, 1000)
    } else {
      uni.showToast({
        title: result.msg || '操作失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('同意改价失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 拒绝改价
const rejectPriceEdit = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  // 设置弹窗内容，显示价格对比
  const originalPrice = orderDetail.value.bidAmount || '0'
  const newPrice = orderDetail.value.notBidAmount || '0'

  rejectPriceEditDialogData.value.content = `确定拒绝此次改价吗？\n\n原始价格：¥${originalPrice}元\n改价后：¥${newPrice}元`

  console.log('打开拒绝改价确认弹窗')

  // 打开拒绝改价确认弹窗
  rejectPriceEditDialog.value?.open()
}

// 处理拒绝改价取消
const handleRejectPriceEditCancel = () => {
  console.log('用户取消拒绝改价')
}

// 处理拒绝改价确认
const handleRejectPriceEditConfirm = async () => {
  try {
    console.log('确认拒绝改价，订单ID:', orderDetail.value.id)

    // 显示加载提示
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用拒绝改价接口
    const result: any = await editOrderInfo({
      id: orderDetail.value.id,
      orderStatus: 4, // 改价已拒绝
    })

    uni.hideLoading()

    if (result.code == 700) {
      uni.showToast({
        title: '已拒绝改价',
        icon: 'success',
        duration: 1000,
      })

      // 刷新订单详情数据
      setTimeout(() => {
        fetchOrderDetail(currentOrderId.value)
      }, 1000)
    } else {
      uni.showToast({
        title: result.msg || '操作失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('拒绝改价失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 切换更多操作展开状态
const toggleMoreActions = () => {
  isMoreActionsExpanded.value = !isMoreActionsExpanded.value
}

// 申请扣订金
const applyDeductDeposit = () => {
  console.log('打开申请扣订金确认弹窗')
  applyDeductDepositDialog.value?.open()
}

// 同意退还
const agreeRefund = () => {
  console.log('打开同意退还确认弹窗')
  agreeRefundDialog.value?.open()
}

// 拒绝退还
const rejectRefund = () => {
  console.log('打开拒绝退还确认弹窗')
  rejectRefundDialog.value?.open()
}

// 申请仲裁
const applyArbitration = () => {
  console.log('打开申请仲裁确认弹窗')
  applyArbitrationDialog.value?.open()
}

// 撤回扣订金
const withdrawDeductDeposit = () => {
  console.log('打开撤回扣订金确认弹窗')
  withdrawDeductDepositDialog.value?.open()
}

// 申请扣订金 - 取消
const handleApplyDeductDepositCancel = () => {
  console.log('用户取消申请扣订金')
}

// 申请扣订金 - 确认
const handleApplyDeductDepositConfirm = () => {
  console.log('用户确认申请扣订金')
  // TODO: 调用申请扣订金接口
  uni.showToast({
    title: '申请扣订金成功',
    icon: 'success',
    duration: 2000,
  })
}

// 同意退还 - 取消
const handleAgreeRefundCancel = () => {
  console.log('用户取消同意退还')
}

// 同意退还 - 确认
const handleAgreeRefundConfirm = () => {
  console.log('用户确认同意退还')
  // TODO: 调用同意退还接口
  uni.showToast({
    title: '同意退还成功',
    icon: 'success',
    duration: 2000,
  })
}

// 拒绝退还 - 取消
const handleRejectRefundCancel = () => {
  console.log('用户取消拒绝退还')
}

// 拒绝退还 - 确认
const handleRejectRefundConfirm = () => {
  console.log('用户确认拒绝退还')
  // TODO: 调用拒绝退还接口
  uni.showToast({
    title: '拒绝退还成功',
    icon: 'success',
    duration: 2000,
  })
}

// 申请仲裁 - 取消
const handleApplyArbitrationCancel = () => {
  console.log('用户取消申请仲裁')
}

// 申请仲裁 - 确认
const handleApplyArbitrationConfirm = () => {
  console.log('用户确认申请仲裁')
  // TODO: 调用申请仲裁接口
  uni.showToast({
    title: '申请仲裁成功',
    icon: 'success',
    duration: 2000,
  })
}

// 撤回扣订金 - 取消
const handleWithdrawDeductDepositCancel = () => {
  console.log('用户取消撤回扣订金')
}

// 撤回扣订金 - 确认
const handleWithdrawDeductDepositConfirm = () => {
  console.log('用户确认撤回扣订金')
  // TODO: 调用撤回扣订金接口
  uni.showToast({
    title: '撤回扣订金成功',
    icon: 'success',
    duration: 2000,
  })
}

// 校验是否已到达可上传图片步骤
const checkOrderPic = computed(() => {
  // 6装货中  8到达卸货地  9已完成待确认（回执）
  return (
    orderDetail.value.orderStatus == 6 ||
    orderDetail.value.orderStatus == 8 ||
    orderDetail.value.orderStatus == 9 ||
    10
  )
})

// 底部按钮校验  1:出发装货地  2:已到达装货地   3:出发卸货地  4:到达卸货地  5:已完成待确认  6:同意改价/拒绝改价
const checkUpdateOrderStatus = (num: number) => {
  if (num == 1) {
    return (
      orderDetail.value.orderStatus == 1 ||
      orderDetail.value.orderStatus == 2 ||
      orderDetail.value.orderStatus == 3 ||
      orderDetail.value.orderStatus == 4
    )
  } else if (num == 2) {
    return orderDetail.value.orderStatus == 5
  } else if (num == 3) {
    return orderDetail.value.orderStatus == 6
  } else if (num == 4) {
    return orderDetail.value.orderStatus == 7
  } else if (num == 5) {
    return orderDetail.value.orderStatus == 8
  } else if (num == 6) {
    return orderDetail.value.orderStatus == 2
  }
  return false
}

// 监听订单状态变化，重新计算底部高度
watch(
  () => orderDetail.value.orderStatus,
  (newStatus, oldStatus) => {
    if (newStatus !== oldStatus && newStatus !== undefined) {
      console.log('订单状态变化，重新计算底部高度:', oldStatus, '->', newStatus)
      // 延迟计算，确保DOM更新完成
      setTimeout(() => {
        calculateBottomHeight()
      }, 200)
    }
  },
  { deep: true }
)

// 组件挂载后计算高度
onMounted(() => {
  // 延迟计算，确保DOM完全渲染
  setTimeout(() => {
    calculateBottomHeight()
  }, 100)

  // 监听窗口大小变化
  uni.onWindowResize(() => {
    debouncedCalculateHeight()
  })

  const pages = getCurrentPages()
  if (pages.length > 1) {
    const currentPage = pages[pages.length - 1]
    // 从页面实例获取事件通道
    const eventChannel = currentPage.$vm.getOpenerEventChannel()
    eventChannel.on('orderId', (data) => {
      const id = data.id
      if (id) {
        // 存储订单ID
        currentOrderId.value = id
        console.log('接收到订单ID:', id)
        // 首次进入页面时立即调用接口
        fetchOrderDetail(id)
      } else {
        console.error('未获取到订单 ID')
      }
    })
  }
})

// 组件更新后重新计算高度
onUpdated(() => {
  debouncedCalculateHeight()
})

// 页面显示时重新计算高度（处理从其他页面返回的情况）
onShow(() => {
  console.log('页面显示，重新计算底部高度')
  setTimeout(() => {
    calculateBottomHeight()
  }, 200)

  // 如果有订单ID，则获取订单详情
  if (currentOrderId.value) {
    console.log('页面显示，获取订单详情:', currentOrderId.value)
    fetchOrderDetail(currentOrderId.value)
  }
})
</script>

<style lang="scss" scoped>
.order-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeaderBg);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

.header-status {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: v-bind(heightHeaderStatus);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;

  .back-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconfont {
      font-size: 34rpx;
      color: #333;
    }
  }

  .status-info {
    flex: 1;
    text-align: center;

    .status-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .header-actions {
    display: flex;
    gap: 30rpx;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .iconfont {
        font-size: 32rpx;
        color: #333;
        margin-bottom: 8rpx;
      }

      text {
        font-size: 22rpx;
        color: #333;
      }
    }
  }
}

.main-content {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  min-height: calc(100vh - 140rpx);
  position: relative;
  z-index: 2;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.order-info-card {
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .order-title {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .title-text {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .enterprise-tag {
        display: flex;
        gap: 8rpx;

        text {
          background-color: #e8f4fd;
          color: #1890ff;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 22rpx;
        }
      }
    }

    .order-detail-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;

      text {
        font-size: 28rpx;
        color: #666;
      }

      .iconfont {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.route-card {
  .route-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-of-type {
      border-bottom: none;
    }

    .route-tag {
      width: 48rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      flex-shrink: 0;

      text {
        font-size: 24rpx;
        font-weight: bold;
        color: #fff;
      }

      &.pickup-tag {
        background-color: #1890ff;
      }

      &.delivery-tag {
        background-color: #ff4d4f;
      }
    }

    .route-info {
      flex: 1;
      margin-left: 8rpx;

      .route-title {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .route-address {
        font-size: 28rpx;
        color: #666;
      }
    }

    .route-action {
      display: flex;
      align-items: center;
      gap: 6rpx;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      flex-shrink: 0;

      .iconfont {
        font-size: 20rpx;
      }

      text {
        font-size: 26rpx;
      }

      &:active {
        opacity: 0.7;
      }
    }

    &.pickup-point .route-action {
      background-color: rgba(24, 144, 255, 0.1);

      .iconfont {
        color: #1890ff;
      }

      text {
        color: #1890ff;
      }
    }

    &.delivery-point .route-action {
      background-color: rgba(255, 77, 79, 0.1);

      .iconfont {
        color: #ff4d4f;
      }

      text {
        color: #ff4d4f;
      }
    }
  }

  .route-time {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-top: 20rpx;
    padding: 16rpx 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;

    .iconfont {
      font-size: 28rpx;
      color: #666;
    }

    text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.fee-card {
  .fee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s;

    &:last-of-type {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f9fa;
    }

    .fee-label {
      flex: 1;

      .fee-tag {
        display: inline-block;
        background-color: #f0f9ff;
        color: #1890ff;
        padding: 6rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        margin-right: 16rpx;
      }

      .fee-amount {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-right: 16rpx;
      }

      .fee-desc {
        font-size: 24rpx;
        color: #666;
      }
    }

    .fee-action {
      .iconfont {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .fee-detail-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8rpx;
    margin-top: 20rpx;
    padding: 16rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;

    text {
      font-size: 28rpx;
      color: #666;
    }

    .detail-icon {
      .iconfont {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.insurance-card {
  .insurance-header {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .insurance-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .insurance-desc {
      flex: 1;
      font-size: 28rpx;
      color: #666;
    }

    .insurance-action {
      display: flex;
      align-items: center;
      gap: 8rpx;

      text {
        font-size: 28rpx;
        color: #1890ff;
      }

      .iconfont {
        font-size: 24rpx;
        color: #1890ff;
      }
    }
  }
}

.photos-card {
  .photos-header {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .photos-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .photos-desc {
      flex: 1;
      font-size: 28rpx;
      color: #666;
    }

    .photos-action {
      display: flex;
      align-items: center;
      gap: 8rpx;

      text {
        font-size: 28rpx;
        color: #1890ff;
      }

      .iconfont {
        font-size: 24rpx;
        color: #1890ff;
      }
    }
  }
}

.driver-card {
  .driver-info {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .driver-avatar {
      width: 80rpx;
      height: 80rpx;
      background-color: #f0f0f0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .head {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .driver-details {
      flex: 1;

      .driver-name {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: 8rpx;

        text:first-child {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }

        .phone-icon {
          width: 60rpx;
          height: 60rpx;
          background-color: #f0f0f0;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: auto;

          .iconfont {
            font-size: 50rpx;
            color: #52c41a;
          }

          &:active {
            background-color: #e0e0e0;
          }
        }

        .driver-rating {
          font-size: 24rpx;
          color: #666;
        }
      }

      .driver-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}

.bottom-fixed-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 9;
  border-top: 1rpx solid #f0f0f0;

  .driver-card {
    background-color: #fff;
    border-radius: 0;
    padding: 20rpx 30rpx;
    margin-bottom: 0;
    box-shadow: none;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .message-card {
    background-color: #e8f5e8;
    margin: 0 30rpx 20rpx 30rpx;
    border-radius: 16rpx;
    padding: 16rpx 20rpx;

    .message-content {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .message-icon {
        width: 40rpx;
        height: 40rpx;
        background-color: #52c41a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .iconfont {
          font-size: 24rpx;
          color: #fff;
        }
      }

      .message-text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }

      .message-action {
        display: flex;
        gap: 16rpx;

        .action-btn {
          display: flex;
          align-items: center;
          gap: 6rpx;
          padding: 8rpx 16rpx;
          border-radius: 8rpx;
          background-color: rgba(82, 196, 26, 0.1);

          text {
            font-size: 28rpx;
            color: #52c41a;
          }

          .iconfont {
            font-size: 24rpx;
            color: #52c41a;
          }

          &.contact-btn {
            background-color: rgba(24, 144, 255, 0.1);

            text {
              color: #1890ff;
            }

            .iconfont {
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  .bottom-actions {
    padding: 20rpx 30rpx;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    box-sizing: border-box;

    // 更多操作展开区域
    .more-actions-panel {
      margin-bottom: 20rpx;
      padding: 20rpx;
      background-color: #fafafa;
      border-radius: 12rpx;
      border: 1rpx solid #f0f0f0;

      .more-actions-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx 16rpx; // 垂直间距12rpx，水平间距16rpx
        justify-content: center; // 居中对齐

        .more-action-btn {
          width: 200rpx; // 固定宽度，确保按钮大小一致
          height: 72rpx;
          background-color: #fff;
          border: 1rpx solid #e0e0e0;
          border-radius: 36rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0; // 防止按钮被压缩

          .more-btn-text {
            font-size: 26rpx;
            color: #333;
            white-space: nowrap; // 防止文字换行
          }

          &:active {
            background-color: #f5f5f5;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 16rpx;

      .action-btn {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12rpx 8rpx;
        border-radius: 12rpx;
        min-height: 65rpx;

        &.secondary {
          background-color: #f8f9fa;

          .iconfont {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 6rpx;
          }

          text {
            font-size: 22rpx;
            color: #666;
          }
        }

        &.primary {
          background-color: #ff4d4f;
          flex: 1.5;

          text {
            font-size: 32rpx;
            color: #fff;
            font-weight: bold;
          }
        }
      }
    }
  }
}

// 为底部固定区域留出空间
.main-content {
  padding-bottom: v-bind(bottomHeight); // 使用Vue的v-bind动态绑定高度
}
</style>
