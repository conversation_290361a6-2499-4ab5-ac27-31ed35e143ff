<template>
  <view class="order-info">
    <!-- 顶部导航栏 -->
    <view class="header-bg"></view>
    <view
      class="header-status"
      :style="{ paddingTop: systemInfo.safeAreaInsets.top * 2 + 'rpx' }"
    >
      <view class="back-btn" @click="goBack">
        <view class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></view>
      </view>
      <view class="header-title">
        <text class="title-text">订单明细</text>
      </view>
      <view class="header-placeholder"></view>
    </view>

    <!-- 主要内容区域 -->
    <view
      class="main-content"
      :style="{
        paddingTop: systemInfo.safeAreaInsets.top * 2 + 80 + 20 + 'rpx',
      }"
    >
      <!-- 订单信息列表 -->
      <view class="info-list">
        <!-- 订单编号 -->
        <view class="info-item">
          <text class="info-label">订单编号</text>
          <view class="info-value-row">
            <text class="info-value">{{ orderInfo.orderId }}</text>
            <view class="copy-btn" @click="copyOrderNumber">
              <text class="copy-text">复制</text>
            </view>
          </view>
        </view>

        <!-- 下单时间 -->
        <view class="info-item">
          <text class="info-label">下单时间</text>
          <text class="info-value">{{ formatDate(orderInfo.orderTime) }}</text>
        </view>

        <!-- 货物信息 -->
        <view class="info-item">
          <text class="info-label">货物信息</text>
          <text class="info-value">{{ orderInfo.cargoInfo }}</text>
        </view>

        <!-- 车辆信息 -->
        <view class="info-item">
          <text class="info-label">车辆信息</text>
          <text class="info-value">{{ orderInfo.vehicleInfo }}</text>
        </view>

        <!-- 里程信息 -->
        <view class="info-item">
          <text class="info-label">里程信息</text>
          <text class="info-value">{{ orderInfo.estimatedDistance }}</text>
        </view>
      </view>

      <!-- 查看货物运输协议 -->
      <!-- <view class="agreement-link" @click="viewAgreement">
        <text class="agreement-text">查看货物运输协议</text>
        <view class="iconfont icon-owner-youjiantou agreement-arrow"></view>
      </view> -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'

const systemInfo: any = uni.getSystemInfoSync()

// 订单信息数据
const orderInfo = ref({
  orderId: '--',
  orderTime: '--',
  cargoInfo: '--',
  vehicleInfo: '--',
  // estimatedDistance: '326公里 (发货时预估，仅供参考)',
})

// 定义时间转换函数
const formatDate = (date: string | number | Date) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 复制订单编号
const copyOrderNumber = () => {
  uni.setClipboardData({
    data: orderInfo.value.orderId,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
      })
    },
  })
}

// 查看货物运输协议
const viewAgreement = () => {
  // 这里可以跳转到协议页面或打开协议文档
  console.log('查看货物运输协议')
  uni.showToast({
    title: '打开运输协议',
    icon: 'none',
  })
}
onLoad(() => {
  const eventChannel =
    getCurrentPages()[getCurrentPages().length - 1].getOpenerEventChannel()
  eventChannel.on('orderInfo', function (data) {
    orderInfo.value.orderId = data.orderId
    orderInfo.value.orderTime = data.orderTime
    orderInfo.value.cargoInfo = data.cargoName
    orderInfo.value.vehicleInfo = data.vehicleLength + ' ' + data.vehicleType
    orderInfo.value.estimatedDistance =
      data.estimatedDistance + '公里 (发货时预估，仅供参考)'
  })
})
</script>

<style scoped lang="scss">
.order-info {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--status-bar-height) + 80rpx + 50rpx);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

.header-status {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: calc(var(--status-bar-height) + 80rpx + 20rpx);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;

  .back-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconfont {
      font-size: 34rpx;
      color: #333;
    }
  }

  .header-title {
    flex: 1;
    text-align: center;

    .title-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .header-placeholder {
    width: 60rpx;
    height: 60rpx;
  }
}

.main-content {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  min-height: calc(100vh - 140rpx);
  position: relative;
  z-index: 2;
}

.info-list {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .info-label {
    font-size: 32rpx;
    color: #666;
    flex-shrink: 0;
    width: 160rpx;
  }

  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: #999;
    text-align: right;
    line-height: 1.4;
  }

  .info-value-row {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20rpx;

    .info-value {
      flex: 1;
      text-align: right;
      font-size: 28rpx;
      color: #999;
    }

    .copy-btn {
      background-color: #f0f0f0;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      flex-shrink: 0;

      .copy-text {
        font-size: 28rpx;
        color: #666;
      }

      &:active {
        background-color: #e0e0e0;
      }
    }
  }
}

.agreement-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 30rpx;

  .agreement-text {
    font-size: 32rpx;
    color: #999;
  }

  .agreement-arrow {
    font-size: 24rpx;
    color: #999;
  }

  &:active {
    opacity: 0.7;
  }
}
</style>
