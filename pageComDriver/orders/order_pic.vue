<template>
  <view class="order-pic-page">
    <!-- 头部导航 -->
    <cu-header class="cu-header-box">
      <template #leftBtn>
        <text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON>ji<PERSON><PERSON>"></text>
      </template>
      <template #title>{{ isOwnerView ? '订单图片' : '图片编辑' }}</template>
      <template #rightBtn>
        {{ '' }}
      </template>
    </cu-header>

    <!-- 主要内容区域 -->
    <view class="main-content" :style="{
      marginTop: headerTotalHeight + 'rpx',
      paddingBottom: isOwnerView ? '32rpx' : '200rpx',
    }">
      <!-- 装货照片 -->
      <view class="upload-section" v-if="showLoadingUpload">
        <view class="section-title">
          <text class="required-mark">*</text>
          <text>装货照片</text>
        </view>
        <view class="upload-container">
          <cu-upload v-model="loadingImage" height="300rpx" width="100%" placeholder="点击上传装货照片" background-color="transparent" :editable="!isOwnerView && orderInfo.orderStatus == 6" @success="handleLoadingUploadSuccess" @fail="handleUploadFail">
            <template #placeholder>
              <view class="upload-placeholder">
                <view class="camera-icon">
                  <text class="iconfont icon-owner-xiangji"></text>
                </view>
              </view>
            </template>
          </cu-upload>
        </view>
      </view>

      <!-- 卸货照片 -->
      <view class="upload-section" v-if="showUnloadingUpload">
        <view class="section-title">
          <text class="required-mark">*</text>
          <text>卸货照片</text>
        </view>
        <view class="upload-container">
          <cu-upload v-model="unloadingImage" height="300rpx" width="100%" placeholder="点击上传卸货照片" background-color="transparent" :editable="!isOwnerView && orderInfo.orderStatus == 8" @success="handleUnloadingUploadSuccess" @fail="handleUploadFail">
            <template #placeholder>
              <view class="upload-placeholder">
                <view class="camera-icon">
                  <text class="iconfont icon-owner-xiangji"></text>
                </view>
              </view>
            </template>
          </cu-upload>
        </view>
      </view>

      <!-- 回单照片 -->
      <view class="upload-section" v-if="showReceiptUpload">
        <view class="section-title">
          <text class="required-mark">*</text>
          <text>回单照片</text>
        </view>
        <view class="upload-container">
          <cu-upload v-model="receiptImage" height="300rpx" width="100%" placeholder="点击上传回单照片" background-color="transparent" :editable="!isOwnerView && orderInfo.orderStatus == 9" @success="handleReceiptUploadSuccess" @fail="handleUploadFail">
            <template #placeholder>
              <view class="upload-placeholder">
                <view class="camera-icon">
                  <text class="iconfont icon-owner-xiangji"></text>
                </view>
              </view>
            </template>
          </cu-upload>
        </view>
      </view>

      <!-- 底部提示文字 -->
      <view class="tip-section">
        <text class="tip-text" v-if="isOwnerView && showReceiptUpload">查看司机上传的回单照片</text>
        <text class="tip-text" v-else-if="isOwnerView && showUnloadingUpload">查看司机上传的卸货照片</text>
        <text class="tip-text" v-else-if="isOwnerView && showLoadingUpload">查看司机上传的装货照片</text>
        <text class="tip-text" v-else-if="isEditMode && showReceiptUpload">若无回单，可上传其他与当前订单信息关联的运输凭证，如出入库单、磅单等</text>
        <text class="tip-text" v-else-if="isEditMode && showLoadingUpload">请上传装货现场照片，确保货物信息清晰可见</text>
        <view class="info-tip" v-if="isEditMode">
          <text class="iconfont icon-owner-tishi"></text>
          <text class="info-text">新增或删除图片，请点击屏幕下方提交图片按钮，提交后才生效</text>
        </view>
        <view class="info-tip" v-else-if="isOwnerView">
          <text class="iconfont icon-owner-tishi"></text>
          <text class="info-text">此页面仅供查看，如需修改请联系司机</text>
        </view>
        <view class="info-tip" v-else>
          <text class="iconfont icon-owner-tishi"></text>
          <text class="info-text">订单已完成，此页面仅供查看</text>
        </view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="bottom-section" v-if="shouldShowSubmitButton">
      <button class="submit-btn" :class="{ active: hasAnyImage }" @click="handleSubmit">
        {{ getSubmitButtonText }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { editOrderInfo } from '../../api/transport'
import { onShow } from '@dcloudio/uni-app'

// 获取系统信息
const { safeAreaInsets } = uni.getSystemInfoSync()

// 订单信息
const orderInfo = ref({
  orderId: '',
  orderStatus: 0,
  loadingImages: '',
  unloadingImages: '',
  deliveryProofImages: '',
})

// 查看模式标识
const viewMode = ref('')
const isOwnerView = computed(() => viewMode.value === 'owner')

// 图片数据 - 支持多张图片
const loadingImages = ref<string[]>([])
const unloadingImages = ref<string[]>([])
const deliveryProofImages = ref<string[]>([])

// 兼容原有的单张图片逻辑
const loadingImage = ref('')
const unloadingImage = ref('')
const receiptImage = ref('')

// 根据订单状态判断显示哪些上传区域
const showLoadingUpload = computed(() => {
  // 订单状态等于6可以上传装货图片
  return orderInfo.value.orderStatus >= 6
})

const showUnloadingUpload = computed(() => {
  // 订单状态等于8可以上传卸货图片
  return orderInfo.value.orderStatus >= 8
})

const showReceiptUpload = computed(() => {
  // 订单状态等于9可以上传回单图片
  return orderInfo.value.orderStatus >= 9
})

// 计算是否有任何图片上传（根据当前状态判断）
const hasAnyImage = computed(() => {
  let hasImage = false

  // 检查当前状态允许的图片类型
  if (showLoadingUpload.value && loadingImage.value) {
    hasImage = true
  }

  if (showUnloadingUpload.value && unloadingImage.value) {
    hasImage = true
  }

  if (showReceiptUpload.value && receiptImage.value) {
    hasImage = true
  }

  return hasImage
})

// 计算头部总高度
const headerTotalHeight = computed(() => {
  return safeAreaInsets.top * 2 + 80 // 状态栏高度 + 导航栏高度
})

// 判断是否应该显示提交按钮
const shouldShowSubmitButton = computed(() => {
  // 不是货主视图且订单状态不是已完成（10）
  return !isOwnerView.value && orderInfo.value.orderStatus != 10 && orderInfo.value.orderStatus != 99 && orderInfo.value.orderStatus != 98
})

// 判断是否为编辑模式
const isEditMode = computed(() => {
  // 不是货主视图且订单状态不是已完成（10）
  return !isOwnerView.value && orderInfo.value.orderStatus != 10
})

// 计算提交按钮文字
const getSubmitButtonText = computed(() => {
  if (orderInfo.value.orderStatus == 6) {
    return '提交装货图片'
  } else if (orderInfo.value.orderStatus == 8) {
    return '提交卸货图片'
  } else if (orderInfo.value.orderStatus == 9) {
    return '提交回单图片'
  } else if (orderInfo.value.orderStatus == 10) {
    return '查看图片'
  }
  return '提交图片'
})

// 处理装货照片上传成功
const handleLoadingUploadSuccess = (res: any) => {
  console.log('装货照片上传成功', res)
  if (res.url) {
    // 更新当前显示的图片
    loadingImage.value = res.url

    // 如果是新图片，添加到数组中；如果是替换，则替换第一张
    if (loadingImages.value.length == 0) {
      loadingImages.value = [res.url]
    } else {
      // 替换第一张图片（当前cu-upload组件只支持单张）
      loadingImages.value[0] = res.url
    }

    console.log('装货图片数组更新:', loadingImages.value)
  }

  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500,
  })
}

// 处理卸货照片上传成功
const handleUnloadingUploadSuccess = (res: any) => {
  console.log('卸货照片上传成功', res)
  if (res.url) {
    // 更新当前显示的图片
    unloadingImage.value = res.url

    // 如果是新图片，添加到数组中；如果是替换，则替换第一张
    if (unloadingImages.value.length == 0) {
      unloadingImages.value = [res.url]
    } else {
      // 替换第一张图片（当前cu-upload组件只支持单张）
      unloadingImages.value[0] = res.url
    }

    console.log('卸货图片数组更新:', unloadingImages.value)
  }

  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500,
  })
}

// 处理回单照片上传成功
const handleReceiptUploadSuccess = (res: any) => {
  console.log('回单照片上传成功', res)
  if (res.url) {
    // 更新当前显示的图片
    receiptImage.value = res.url

    // 如果是新图片，添加到数组中；如果是替换，则替换第一张
    if (deliveryProofImages.value.length == 0) {
      deliveryProofImages.value = [res.url]
    } else {
      // 替换第一张图片（当前cu-upload组件只支持单张）
      deliveryProofImages.value[0] = res.url
    }

    console.log('回单图片数组更新:', deliveryProofImages.value)
  }

  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500,
  })
}

// 处理上传失败
const handleUploadFail = (error: any) => {
  console.error('图片上传失败', error)
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
    duration: 2000,
  })
}

// 处理提交
const handleSubmit = async () => {
  // 根据订单状态验证必需的图片
  if (showLoadingUpload.value && !loadingImage.value) {
    uni.showToast({
      title: '请上传装货照片',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (showUnloadingUpload.value && !unloadingImage.value) {
    uni.showToast({
      title: '请上传卸货照片',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (showReceiptUpload.value && !receiptImage.value) {
    uni.showToast({
      title: '请上传回单照片',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    // 准备提交数据
    const submitData: any = {
      id: orderInfo.value.orderId,
    }

    // 根据当前状态添加相应的图片字段
    if (showLoadingUpload.value && loadingImages.value.length > 0) {
      submitData.loadingImages = loadingImages.value
        .filter((url) => url.trim())
        .join(',')
      console.log('提交装货图片:', submitData.loadingImages)
    }

    if (showUnloadingUpload.value && unloadingImages.value.length > 0) {
      submitData.unloadingImages = unloadingImages.value
        .filter((url) => url.trim())
        .join(',')
      console.log('提交卸货图片:', submitData.unloadingImages)
    }

    if (showReceiptUpload.value && deliveryProofImages.value.length > 0) {
      submitData.deliveryProofImages = deliveryProofImages.value
        .filter((url) => url.trim())
        .join(',')
      console.log('提交回单图片:', submitData.deliveryProofImages)
    }

    // 如果没有任何图片需要提交，提示用户
    if (
      !submitData.loadingImages &&
      !submitData.unloadingImages &&
      !submitData.deliveryProofImages
    ) {
      uni.showToast({
        title: '请先上传图片',
        icon: 'none',
        duration: 2000,
      })
      return
    }

    console.log('提交图片数据', submitData)

    // 调用接口提交
    const response = await editOrderInfo(submitData)

    if (response.code == 700) {
      uni.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 1500,
      })

      // 提交成功后返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: response.msg || '提交失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('提交图片失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 组件挂载时接收订单信息
onShow(() => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    const currentPage = pages[pages.length - 1]
    const eventChannel = currentPage.$vm.getOpenerEventChannel()
    eventChannel.on('orderInfo', (data) => {
      console.log('接收到订单信息:', data)

      // 设置查看模式
      viewMode.value = data.viewMode || ''

      orderInfo.value = {
        orderId: data.orderId,
        orderStatus: data.orderStatus,
        loadingImages: data.loadingImages || '',
        unloadingImages: data.unloadingImages || '',
        deliveryProofImages: data.deliveryProofImages || '',
      }

      // 初始化已有的图片数据
      console.log('初始化图片数据:', {
        loadingImages: data.loadingImages,
        unloadingImages: data.unloadingImages,
        deliveryProofImages: data.deliveryProofImages,
      })

      // 装货图片初始化
      if (data.loadingImages && data.loadingImages.trim()) {
        loadingImages.value = data.loadingImages
          .split(',')
          .filter((url) => url.trim())
        if (loadingImages.value.length > 0) {
          loadingImage.value = loadingImages.value[0]
          console.log('装货图片返显:', loadingImage.value)
        }
      } else {
        loadingImages.value = []
        loadingImage.value = ''
      }

      // 卸货图片初始化
      if (data.unloadingImages && data.unloadingImages.trim()) {
        unloadingImages.value = data.unloadingImages
          .split(',')
          .filter((url) => url.trim())
        if (unloadingImages.value.length > 0) {
          unloadingImage.value = unloadingImages.value[0]
          console.log('卸货图片返显:', unloadingImage.value)
        }
      } else {
        unloadingImages.value = []
        unloadingImage.value = ''
      }

      // 回单图片初始化
      if (data.deliveryProofImages && data.deliveryProofImages.trim()) {
        deliveryProofImages.value = data.deliveryProofImages
          .split(',')
          .filter((url) => url.trim())
        if (deliveryProofImages.value.length > 0) {
          receiptImage.value = deliveryProofImages.value[0]
          console.log('回单图片返显:', receiptImage.value)
        }
      } else {
        deliveryProofImages.value = []
        receiptImage.value = ''
      }

      // 调试信息：显示当前状态和图片显示情况
      console.log('页面状态总结:', {
        订单状态: orderInfo.value.orderStatus,
        显示装货上传: showLoadingUpload.value,
        显示卸货上传: showUnloadingUpload.value,
        显示回单上传: showReceiptUpload.value,
        装货图片: loadingImage.value,
        卸货图片: unloadingImage.value,
        回单图片: receiptImage.value,
      })
    })
  }
})
</script>

<style lang="scss" scoped>
.order-pic-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.cu-header-box {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.main-content {
  flex: 1;
  padding: 32rpx;
}

.upload-section {
  margin-bottom: 32rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;

    .required-mark {
      color: #e84138;
      margin-right: 8rpx;
      font-size: 32rpx;
    }
  }

  .upload-container {
    border-radius: 16rpx;
    overflow: hidden;
    background-color: #f8f9fa;
    border: 2rpx dashed #e0e0e0;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 40rpx;

  .camera-icon {
    width: 140rpx;
    height: 140rpx;
    background-color: #e84138;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(232, 65, 56, 0.3);

    .iconfont {
      font-size: 70rpx;
      color: #fff;
    }
  }
}

.tip-section {
  margin-top: 32rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .tip-text {
    font-size: 28rpx;
    color: #e84138;
    line-height: 1.5;
    display: block;
    margin-bottom: 16rpx;
  }

  .info-tip {
    display: flex;
    align-items: flex-start;

    .iconfont {
      font-size: 28rpx;
      color: #999;
      margin-right: 8rpx;
      margin-top: 2rpx;
      flex-shrink: 0;
    }

    .info-text {
      font-size: 26rpx;
      color: #999;
      line-height: 1.5;
      flex: 1;
    }
  }
}

.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1rpx solid #eee;
  z-index: 10;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background-color: #ccc;
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.active {
      background-color: #e84138;
    }

    &:active {
      transform: scale(0.98);
    }
  }
}
</style>
