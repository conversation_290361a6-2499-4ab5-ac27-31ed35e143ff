<template>
  <view class="cost-info">
    <!-- 顶部导航栏 -->
    <view class="header-bg"></view>
    <view class="header-status" :style="{ paddingTop: systemInfo.safeAreaInsets.top * 2 + 'rpx' }">
      <view class="back-btn" @click="goBack">
        <view class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></view>
      </view>
      <view class="header-title">
        <text class="title-text">费用明细</text>
      </view>
      <view class="header-placeholder"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content" :style="{ paddingTop: systemInfo.safeAreaInsets.top * 2 + 80 + 20 + 'rpx' }">
      <!-- 订金卡片 -->
      <view class="cost-card">
        <view class="cost-item main-item">
          <view class="cost-label">
            <text class="label-text">订金</text>
            <text class="label-tag">（可退）</text>
          </view>
          <text class="cost-amount">¥{{ costInfo.deposit.amount }}</text>
        </view>
        <view class="cost-item sub-item">
          <text class="cost-label">实际支付</text>
          <text class="cost-amount">¥{{ costInfo.deposit.actualPaid }}</text>
        </view>
      </view>

      <!-- 运费卡片 -->
      <view class="cost-card">
        <view class="cost-item main-item">
          <view class="cost-label">
            <text class="label-text">运费</text>
            <view class="info-icon" @click="showFreightDialog">
              <view class="iconfont icon-owner-tishi1"></view>
            </view>
          </view>
          <text class="cost-amount">¥{{ costInfo.freight.amount }}</text>
        </view>
        <view class="cost-item sub-item clickable" @click="viewFreightDetails">
          <text class="cost-label">到付款</text>
          <view class="cost-value-row">
            <text class="cost-amount">¥{{ costInfo.freight.codAmount }}</text>
<!--
            <view class="iconfont icon-owner-youjiantou arrow-icon"></view>
-->
          </view>
        </view>
      </view>

      <!-- 技术服务费卡片 -->
<!--      <view class="cost-card">
        <view class="cost-item main-item">
          <view class="cost-label">
            <text class="label-text">技术服务费</text>
            <view class="info-icon" @click="showServiceFeeDialog">
              <view class="iconfont icon-owner-tishi1"></view>
            </view>
          </view>
          <text class="cost-amount">¥{{ costInfo.serviceFee.amount }}</text>
        </view>
        <view class="cost-item sub-item">
          <text class="cost-label">实际支付</text>
          <text class="cost-amount">¥{{ costInfo.serviceFee.actualPaid }}</text>
        </view>
      </view>-->
    </view>

    <!-- 运费说明弹窗 -->
    <cu-confirm-dialog
      ref="freightDialog"
      title=""
      :content="freightDialogContent"
      confirm-text="好的"
      :show-cancel="false"
      @confirm="handleFreightDialogConfirm"
    />

    <!-- 技术服务费说明弹窗 -->
    <cu-confirm-dialog
      ref="serviceFeeDialog"
      title="技术服务费"
      :content="serviceFeeDialogContent"
      :important-tips="serviceFeeDialogTips"
      confirm-text="我知道了"
      :show-cancel="false"
      @confirm="handleServiceFeeDialogConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const systemInfo: any = uni.getSystemInfoSync()

// 费用信息数据
const costInfo = ref({
  deposit: {
    amount: '--',
    actualPaid: '--'
  },
  freight: {
    amount: '--',
    codAmount: '--'
  },
  serviceFee: {
    amount: '49.00',
    actualPaid: '49.00'
  }
})

// 运费弹窗引用
const freightDialog = ref(null)

// 运费弹窗内容
const freightDialogContent = ref('此运费金额为依据法律法规扣除相关税费（如有）后向承运人支付的税后净额')

// 技术服务费弹窗引用
const serviceFeeDialog = ref(null)

// 技术服务费弹窗内容
const serviceFeeDialogContent = ref('本单技术服务费占比=2.1%\n计算公式=技术服务费49.00元/净得运费2300.00元（总运费2300.00元）')

// 技术服务费弹窗提示
const serviceFeeDialogTips = ref('2.1%仅指本单，因路线情况等差异，不代表其他订单费率水平。')

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 查看运费详情
const viewFreightDetails = () => {
  console.log('查看运费详情')
  uni.showToast({
    title: '查看运费详情',
    icon: 'none'
  })
  // 这里可以跳转到运费详情页面或显示运费详情弹窗
}

// 显示运费说明弹窗
const showFreightDialog = () => {
  freightDialog.value?.open()
}

// 处理运费弹窗确认
const handleFreightDialogConfirm = () => {
  console.log('用户确认了运费说明')
}

// 显示技术服务费说明弹窗
const showServiceFeeDialog = () => {
  serviceFeeDialog.value?.open()
}

// 处理技术服务费弹窗确认
const handleServiceFeeDialogConfirm = () => {
  console.log('用户确认了技术服务费说明')
}

onLoad(() => {
  const eventChannel =
    getCurrentPages()[getCurrentPages().length - 1].getOpenerEventChannel()
  eventChannel.on('costInfo', function (data) {
    costInfo.value.deposit.amount = data.depositAmount
    costInfo.value.deposit.actualPaid = data.depositAmount
    costInfo.value.deposit.amount = data.bidAmount
    costInfo.value.deposit.codAmount = data.bidAmount
  })
})
</script>

<style scoped lang="scss">
.cost-info {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--status-bar-height) + 80rpx + 50rpx);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

.header-status {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: calc(var(--status-bar-height) + 80rpx + 20rpx);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;

  .back-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconfont {
      font-size: 34rpx;
      color: #333;
    }
  }

  .header-title {
    flex: 1;
    text-align: center;

    .title-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .header-placeholder {
    width: 60rpx;
    height: 60rpx;
  }
}

.main-content {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  min-height: calc(100vh - 140rpx);
  position: relative;
  z-index: 2;
}

.cost-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;

  &.main-item {
    .cost-label {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .label-text {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      .label-tag {
        font-size: 24rpx;
        color: #999;
      }

      .info-icon {
        padding: 8rpx;
        margin: -8rpx;

        .iconfont {
          font-size: 24rpx;
          color: #999;
        }

        &:active {
          opacity: 0.6;
        }
      }
    }

    .cost-amount {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
  }

  &.sub-item {
    padding-left: 60rpx;
    padding-top: 20rpx;
    padding-bottom: 20rpx;

    .cost-label {
      font-size: 28rpx;
      color: #999;
    }

    .cost-amount {
      font-size: 28rpx;
      color: #999;
    }

    .cost-value-row {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .cost-amount {
        font-size: 28rpx;
        color: #999;
      }

      .arrow-icon {
        font-size: 20rpx;
        color: #999;
      }
    }

    &.clickable {
      &:active {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>
