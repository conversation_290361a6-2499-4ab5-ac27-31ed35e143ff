<template>
  <view class="entrance-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>

    <!-- 页面标题 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <text class="title">选择资质类别</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="entrance-content" :style="{ paddingBottom: safeAreaBottom, marginTop: headerTotalHeight }">
      <!-- 顶部提示栏 -->
      <view class="tip-bar">
        <view class="tip-icon">
          <text class="tip-icon-inner">!</text>
        </view>
        <text class="tip-text">提示：认证审核成功后，身份不可变更，请根据实际情况选择您的身份</text>
      </view>

      <!-- 选项列表 -->
      <view class="option-list">
        <!-- 司机 -->
        <view class="option-item" :class="{ active: selectedOption === 'driver' }" @click="selectOption('driver')">
          <!-- 状态标签 -->
          <view class="status-tag status-failed" v-if="userInfoLocal.userType == 2 && userInfoLocal.auditStatus == 3">
            <text>审核未通过</text>
          </view>
          <view class="option-icon driver-icon">
            <view class="doc-icon"></view>
          </view>
          <view class="option-content">
            <text class="option-title">我是司机</text>
            <view class="option-desc">
              <text class="upload-required">需上传：</text>
              <text class="doc-list">身份证、驾驶证、行驶证、道路运输证（选填）、环保信息随车清单（选填）</text>
            </view>
          </view>
        </view>

        <!-- 货主(个人) -->
        <view class="option-item" :class="{ active: selectedOption === 'shipper_personal' }" @click="selectOption('shipper_personal')">
          <view class="option-icon personal-icon">
            <view class="doc-icon"></view>
          </view>
          <view class="option-content">
            <text class="option-title">我是货主（个人）</text>
            <view class="option-desc">
              <text class="doc-list">个人货主，无需上传资质</text>
            </view>
          </view>
        </view>

        <!-- 货主(企业) -->
        <view class="option-item" :class="{ active: selectedOption === 'shipper_company' }" @click="selectOption('shipper_company')">
          <!-- 状态标签 -->
          <view class="status-tag status-failed" v-if="userInfoLocal.userType == 4 && userInfoLocal.auditStatus == 3">
            <text>审核未通过</text>
          </view>
          <view class="option-icon company-icon">
            <view class="doc-icon"></view>
          </view>
          <view class="option-content">
            <text class="option-title">我是货主（企业）</text>
            <view class="option-desc">
              <text class="upload-required">需上传：</text>
              <text class="doc-list">身份证、营业执照、授权书（选填）</text>
            </view>
          </view>
        </view>

        <!-- 配货站 -->
        <view class="option-item" :class="{ active: selectedOption === 'station' }" @click="selectOption('station')">
          <!-- 状态标签 -->
          <view class="status-tag status-failed" v-if="userInfoLocal.userType == 3 && userInfoLocal.auditStatus == 3">
            <text>审核未通过</text>
          </view>
          <view class="option-icon station-icon">
            <view class="doc-icon"></view>
          </view>
          <view class="option-content">
            <text class="option-title">我是配货站</text>
            <view class="option-desc">
              <text class="upload-required">需上传：</text>
              <text class="doc-list">身份证、营业执照、授权书（选填）</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部确认按钮 -->
      <view class="confirm-btn-wrapper">
        <view class="confirm-btn" :class="{ disabled: !selectedOption }" @click="handleConfirm">
          <text>确认选择</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { setStoragetItem, getStoragetItem } from '@/utils/storage';
import { saveLoginUserInfo } from '@/api/transport';

// 获取系统信息，用于处理安全区域
const systemInfo = uni.getSystemInfoSync();
// 状态栏高度 (单位: rpx) - 系统API返回的是px，需要转换为rpx
const statusBarHeight = systemInfo.statusBarHeight * 2; // 1px = 2rpx 的转换比例

// 计算顶部安全区域高度
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif

  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

// 计算底部安全区域高度
const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif

  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '104rpx';

  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif

  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

const userInfoLocal: any = getStoragetItem('userInfo')

// 当前选择的选项
const selectedOption = ref('');

// 选择选项
const selectOption = (type: 'driver' | 'shipper_personal' | 'shipper_company' | 'station') => {
  selectedOption.value = type;
};

// 确认选择，执行跳转
const handleConfirm = async () => {
  if (!selectedOption.value) {
    uni.showToast({
      title: '请先选择身份类型',
      icon: 'none'
    });
    return;
  }

  const type = selectedOption.value;
  const result: any = await saveLoginUserInfo({
    id: getStoragetItem('userInfo').id,
    userType: type == 'shipper_personal' ? 1 : type == 'driver' ? 2 : type == 'station' ? 3 : type == 'shipper_company' ? 4 : 0
  })
  if (result.code == 700) {
    console.log("库里更新用户类型成功")
  }
  // 个人货主直接跳转到首页，无需认证
  if (type === 'shipper_personal') {
    setStoragetItem('userType', type);
    uni.reLaunch({
      url: '/pages/index/index'
    });
    return;
  }

  // 其他类型跳转到认证页面
  uni.navigateTo({
    url: `/pages/auth/auth?userType=${type}`
  });
};
</script>

<style scoped lang="scss">
.entrance-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-x: hidden; // 防止水平溢出
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  margin-bottom: 0;
  background-color: #fff;
  z-index: 99;

  .title {
    font-size: 40rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }

  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.entrance-content {
  flex: 1;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.tip-bar {
  background-color: #fff2f2;
  padding: 16rpx 30rpx;
  margin: 20rpx 0 30rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 136, 0.1);
  border-left: 6rpx solid #ff9cac;

  .tip-icon {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    background-color: #ff9cac;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 16rpx;
    flex-shrink: 0;

    .tip-icon-inner {
      color: #fff;
      font-size: 24rpx;
      font-weight: bold;
    }
  }

  .tip-text {
    font-size: 24rpx;
    color: #ff6b88;
    line-height: 1.4;
    flex: 1;
  }
}

.option-list {
  padding: 0;
  flex: 1;

  .option-item {
    display: flex;
    background-color: #fff;
    padding: 30rpx;
    margin-bottom: 30rpx;
    border-radius: 16rpx;
    border: none;
    position: relative;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;

    // 添加轻微的底部装饰条
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 6rpx;
      background: linear-gradient(90deg, rgba(120, 120, 120, 0.3), rgba(120, 120, 120, 0.1));
    }

    // 司机选项 - 蓝色主题
    &:nth-child(1) {
      &::after {
        background: linear-gradient(90deg, rgba(64, 158, 255, 0.8), rgba(64, 158, 255, 0.2));
      }

      &.active {
        background: linear-gradient(to bottom right, rgba(236, 245, 255, 0.9), rgba(236, 245, 255, 0.4));
        box-shadow: 0 6rpx 24rpx rgba(64, 158, 255, 0.15);
        border-left: 6rpx solid #409EFF;

        .doc-icon {
          border-color: #409EFF;
          background-color: rgba(64, 158, 255, 0.05);

          &::before,
          &::after {
            background-color: #409EFF;
          }
        }

        .option-title {
          color: #409EFF;
        }
      }
    }

    // 货主(个人)选项 - 绿色主题
    &:nth-child(2) {
      &::after {
        background: linear-gradient(90deg, rgba(103, 194, 58, 0.8), rgba(103, 194, 58, 0.2));
      }

      &.active {
        background: linear-gradient(to bottom right, rgba(240, 249, 235, 0.9), rgba(240, 249, 235, 0.4));
        box-shadow: 0 6rpx 24rpx rgba(103, 194, 58, 0.15);
        border-left: 6rpx solid #67C23A;

        .doc-icon {
          border-color: #67C23A;
          background-color: rgba(103, 194, 58, 0.05);

          &::before,
          &::after {
            background-color: #67C23A;
          }
        }

        .option-title {
          color: #67C23A;
        }
      }
    }

    // 货主(企业)选项 - 橙色主题
    &:nth-child(3) {
      &::after {
        background: linear-gradient(90deg, rgba(230, 162, 60, 0.8), rgba(230, 162, 60, 0.2));
      }

      &.active {
        background: linear-gradient(to bottom right, rgba(253, 246, 236, 0.9), rgba(253, 246, 236, 0.4));
        box-shadow: 0 6rpx 24rpx rgba(230, 162, 60, 0.15);
        border-left: 6rpx solid #E6A23C;

        .doc-icon {
          border-color: #E6A23C;
          background-color: rgba(230, 162, 60, 0.05);

          &::before,
          &::after {
            background-color: #E6A23C;
          }
        }

        .option-title {
          color: #E6A23C;
        }
      }
    }

    // 配货站选项 - 粉色主题
    &:nth-child(4) {
      &::after {
        background: linear-gradient(90deg, rgba(245, 108, 108, 0.8), rgba(245, 108, 108, 0.2));
      }

      &.active {
        background: linear-gradient(to bottom right, rgba(254, 240, 240, 0.9), rgba(254, 240, 240, 0.4));
        box-shadow: 0 6rpx 24rpx rgba(245, 108, 108, 0.15);
        border-left: 6rpx solid #F56C6C;

        .doc-icon {
          border-color: #F56C6C;
          background-color: rgba(245, 108, 108, 0.05);

          &::before,
          &::after {
            background-color: #F56C6C;
          }
        }

        .option-title {
          color: #F56C6C;
        }
      }
    }

    &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    }

    .option-icon {
      margin-right: 20rpx;

      // 不同选项的图标颜色
      &.driver-icon .doc-icon {
        border-color: rgba(64, 158, 255, 0.6);
      }

      &.personal-icon .doc-icon {
        border-color: rgba(103, 194, 58, 0.6);
      }

      &.company-icon .doc-icon {
        border-color: rgba(230, 162, 60, 0.6);
      }

      &.station-icon .doc-icon {
        border-color: rgba(245, 108, 108, 0.6);
      }

      .doc-icon {
        width: 60rpx;
        height: 70rpx;
        position: relative;
        border: 2rpx solid #999;
        border-radius: 8rpx;
        background-color: rgba(153, 153, 153, 0.03);
        transition: all 0.3s ease;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

        &::before {
          content: '';
          position: absolute;
          top: 16rpx;
          left: 10rpx;
          width: 40rpx;
          height: 2rpx;
          background-color: #999;
          transition: all 0.3s ease;
        }

        &::after {
          content: '';
          position: absolute;
          top: 26rpx;
          left: 10rpx;
          width: 30rpx;
          height: 2rpx;
          background-color: #999;
          transition: all 0.3s ease;
        }
      }
    }

    // 状态标签样式
    .status-tag {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      font-weight: 500;
      z-index: 10;

      // 审核未通过状态
      &.status-failed {
        background-color: #E6A23C;
        color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(230, 162, 60, 0.3);
      }

      // 其他状态可以在这里扩展
      &.status-pending {
        background-color: #E6A23C;
        color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(230, 162, 60, 0.3);
      }

      &.status-passed {
        background-color: #67C23A;
        color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.3);
      }
    }

    .option-content {
      flex: 1;

      .option-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 16rpx;
        transition: all 0.3s ease;
      }

      .option-desc {
        .upload-required {
          font-size: 24rpx;
          color: #999;
        }

        .doc-list {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}

/* #ifdef APP-PLUS */
// 在较小屏幕上的额外优化
@media screen and (max-height: 700px) {
  .option-item {
    padding: 20rpx !important;
    margin-bottom: 20rpx !important;

    .option-icon .doc-icon {
      width: 50rpx !important;
      height: 60rpx !important;
    }

    .option-content .option-title {
      font-size: 28rpx !important;
      margin-bottom: 10rpx !important;
    }

    .option-content .option-desc {

      .upload-required,
      .doc-list {
        font-size: 22rpx !important;
      }
    }

    // 状态标签在小屏幕上的适配
    .status-tag {
      top: 15rpx !important;
      right: 15rpx !important;
      padding: 6rpx 12rpx !important;
      font-size: 20rpx !important;
    }
  }
}

/* #endif */

// 底部确认按钮
.confirm-btn-wrapper {
  padding: 30rpx;
  margin-top: auto; // 使按钮区域位于底部
}

.confirm-btn {
  height: 90rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);

  &.disabled {
    background: linear-gradient(90deg, #ccc, #ddd);
    box-shadow: none;
    opacity: 0.8;
  }

  text {
    color: #fff;
    font-size: 34rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }

  &:active:not(.disabled) {
    transform: scale(0.98);
  }
}

/* #ifdef APP-PLUS */
// 在较小屏幕上的额外优化
@media screen and (max-height: 700px) {
  .confirm-btn-wrapper {
    padding: 20rpx 30rpx;
  }

  .confirm-btn {
    height: 80rpx;

    text {
      font-size: 32rpx;
    }
  }
}

/* #endif */
</style>