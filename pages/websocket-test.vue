<template>
  <view class="container">
    <view class="header">
      <text class="title">WebSocket连接测试</text>
    </view>
    
    <!-- 连接状态 -->
    <view class="status-card">
      <view class="status-row">
        <text class="label">连接状态：</text>
        <text class="status" :class="statusClass">{{ statusText }}</text>
      </view>
      <view class="status-row">
        <text class="label">连接时间：</text>
        <text class="value">{{ connectTime || '未连接' }}</text>
      </view>
      <view class="status-row">
        <text class="label">消息数量：</text>
        <text class="value">{{ messageCount }}</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="button-group">
      <button 
        class="btn primary" 
        @click="handleConnect"
        :disabled="isConnected"
      >
        {{ isConnected ? '已连接' : '连接' }}
      </button>
      
      <button 
        class="btn secondary" 
        @click="handleDisconnect"
        :disabled="!isConnected"
      >
        断开连接
      </button>
      
      <button 
        class="btn info" 
        @click="sendTestMessage"
        :disabled="!isConnected"
      >
        发送测试消息
      </button>
      
      <button 
        class="btn warning" 
        @click="clearMessages"
      >
        清空消息
      </button>
    </view>
    
    <!-- 消息列表 -->
    <view class="message-section">
      <view class="section-title">消息记录</view>
      <scroll-view class="message-list" scroll-y>
        <view 
          v-for="(message, index) in messages" 
          :key="index"
          class="message-item"
          :class="getMessageClass(message.type)"
        >
          <view class="message-header">
            <text class="message-type">{{ message.type }}</text>
            <text class="message-time">{{ formatTime(message.timestamp) }}</text>
          </view>
          <view class="message-content">
            {{ JSON.stringify(message.data, null, 2) }}
          </view>
        </view>
        
        <view v-if="messages.length === 0" class="empty-message">
          暂无消息
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ws, WSStatus, WSMessage } from '../utils/websocket'

// 响应式数据
const connectionStatus = ref<WSStatus>(WSStatus.DISCONNECTED)
const messages = ref<WSMessage[]>([])
const connectTime = ref<string>('')

// 计算属性
const isConnected = computed(() => connectionStatus.value === WSStatus.CONNECTED)
const messageCount = computed(() => messages.value.length)

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case WSStatus.DISCONNECTED:
      return '未连接'
    case WSStatus.CONNECTING:
      return '连接中'
    case WSStatus.CONNECTED:
      return '已连接'
    case WSStatus.ERROR:
      return '连接错误'
    default:
      return '未知状态'
  }
})

const statusClass = computed(() => {
  switch (connectionStatus.value) {
    case WSStatus.CONNECTED:
      return 'status-connected'
    case WSStatus.CONNECTING:
      return 'status-connecting'
    case WSStatus.ERROR:
      return 'status-error'
    default:
      return 'status-disconnected'
  }
})

// 方法
const handleConnect = () => {
  console.log('手动连接WebSocket')
  ws.connect()
}

const handleDisconnect = () => {
  console.log('手动断开WebSocket')
  ws.disconnect()
}

const sendTestMessage = () => {
  const testMessage = {
    type: 'test',
    data: {
      content: '这是一条测试消息',
      timestamp: Date.now(),
      random: Math.random()
    }
  }
  
  ws.send(testMessage)
  console.log('发送测试消息:', testMessage)
}

const clearMessages = () => {
  messages.value = []
  console.log('清空消息列表')
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getMessageClass = (type: string) => {
  switch (type) {
    case 'status_change':
      return 'message-status'
    case 'heartbeat':
    case 'ping':
    case 'pong':
      return 'message-heartbeat'
    case 'error':
      return 'message-error'
    default:
      return 'message-normal'
  }
}

// 消息处理器
const handleMessage = (message: WSMessage) => {
  console.log('收到WebSocket消息:', message)
  
  // 添加到消息列表
  messages.value.unshift(message)
  
  // 限制消息数量
  if (messages.value.length > 50) {
    messages.value = messages.value.slice(0, 50)
  }
}

const handleStatusChange = (message: WSMessage) => {
  const status = message.data.status
  connectionStatus.value = status
  
  if (status === WSStatus.CONNECTED) {
    connectTime.value = new Date().toLocaleString()
  } else if (status === WSStatus.DISCONNECTED) {
    connectTime.value = ''
  }
  
  // 也添加到消息列表
  handleMessage(message)
}

// 生命周期
onMounted(() => {
  console.log('WebSocket测试页面加载')
  
  // 获取当前状态
  connectionStatus.value = ws.getStatus()
  
  // 监听所有消息
  ws.on('*', handleMessage)
  
  // 监听状态变化
  ws.on('status_change', handleStatusChange)
  
  // 如果已连接，设置连接时间
  if (isConnected.value) {
    connectTime.value = new Date().toLocaleString()
  }
})

onUnmounted(() => {
  console.log('WebSocket测试页面卸载')
  
  // 移除监听器
  ws.off('*', handleMessage)
  ws.off('status_change', handleStatusChange)
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.status-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-size: 28rpx;
      color: #666;
    }
    
    .status {
      font-size: 28rpx;
      font-weight: bold;
      
      &.status-connected {
        color: #28a745;
      }
      
      &.status-connecting {
        color: #ffc107;
      }
      
      &.status-error {
        color: #dc3545;
      }
      
      &.status-disconnected {
        color: #6c757d;
      }
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
  
  .btn {
    flex: 1;
    min-width: 200rpx;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
    
    &.primary {
      background-color: #007bff;
      color: white;
    }
    
    &.secondary {
      background-color: #6c757d;
      color: white;
    }
    
    &.info {
      background-color: #17a2b8;
      color: white;
    }
    
    &.warning {
      background-color: #ffc107;
      color: #212529;
    }
    
    &:disabled {
      opacity: 0.6;
    }
  }
}

.message-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  height: 600rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .message-list {
    height: 500rpx;
    
    .message-item {
      border: 1rpx solid #e9ecef;
      border-radius: 10rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      
      &.message-status {
        border-color: #007bff;
        background-color: #f8f9ff;
      }
      
      &.message-heartbeat {
        border-color: #28a745;
        background-color: #f8fff8;
      }
      
      &.message-error {
        border-color: #dc3545;
        background-color: #fff8f8;
      }
      
      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;
        
        .message-type {
          font-size: 24rpx;
          font-weight: bold;
          color: #007bff;
        }
        
        .message-time {
          font-size: 22rpx;
          color: #999;
        }
      }
      
      .message-content {
        font-size: 24rpx;
        color: #333;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
    
    .empty-message {
      text-align: center;
      color: #999;
      font-size: 28rpx;
      padding: 100rpx 0;
    }
  }
}
</style>
