<template>
  <view class="business-card-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>
    
    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <view class="back-btn" @click="handleBack">
        <view class="back-icon"></view>
      </view>
      <text class="title">营业执照认证</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingBottom: safeAreaBottom, marginTop: headerTotalHeight }">
      <!-- 上传必要说明 -->
      <view class="upload-requirement">
        <text class="required-symbol">*</text>
        <text class="requirement-text">上传营业执照</text>
      </view>
      
      <!-- 图片上传区域 -->
      <view class="upload-section">
        <view class="upload-item">
          <view class="upload-preview-container">
            <cu-upload
              v-model="businessLicensePath"
              height="460rpx"
              placeholder="上传营业执照"
              @success="handleUploadSuccess"
              @fail="handleUploadFail"
            >
              <template #placeholder>
                <view class="upload-placeholder">
                  <view class="upload-icon"></view>
                  <text class="upload-text">上传营业执照</text>
                </view>
              </template>
            </cu-upload>
          </view>
        </view>
        
        <!-- 小提示文本 -->
        <view class="tip-text">
          <text class="iconfont icon-owner-tishi"></text>
          <text>请上传清晰完整的营业执照照片</text>
        </view>
      </view>
      
      <!-- 营业执照信息区域 -->
      <view class="license-info-section">
        <view class="section-header">
          <text class="section-title">营业执照信息</text>
          <view class="edit-btn" @click="openEditDialog">
            <text class="edit-text">编辑</text>
            <text class="iconfont icon-owner-bianji"></text>
          </view>
        </view>
        
        <view class="info-container">
          <view class="info-row">
            <view class="info-label">公司名称</view>
            <view class="info-value">{{ companyInfo.companyName || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">法人名称</view>
            <view class="info-value">{{ companyInfo.legalPerson || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">营业执照号</view>
            <view class="info-value">{{ companyInfo.businessLicenseCode || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">税号</view>
            <view class="info-value">{{ companyInfo.taxNumber || '未填写' }}</view>
          </view>
        </view>
      </view>
      
      <!-- 协议同意区域 -->
      <view class="agreement-section">
        <view class="checkbox" @click="isAgreed = !isAgreed">
          <view class="checkbox-inner" :class="{ 'checked': isAgreed }"></view>
        </view>
        <text class="agreement-text">您同意我们收集营业执照用于企业资质审核</text>
      </view>

      <!-- 提交按钮 -->
      <view 
        class="submit-btn" 
        :class="{ 'disabled': !isFormValid }"
        @click="handleSubmit"
      >
        <text>提交申核</text>
      </view>
    </view>
    
    <!-- 编辑弹窗 -->
    <view class="edit-dialog" v-if="showEditDialog">
      <view class="dialog-mask" @click="closeEditDialog"></view>
      <view class="dialog-container" :style="{ paddingBottom: safeAreaBottom }">
        <view class="dialog-header">
          <text class="dialog-title">编辑营业执照信息</text>
          <view class="close-btn" @click="closeEditDialog">×</view>
        </view>
        
        <view class="dialog-content">
          <view class="form-item">
            <view class="form-label">公司名称</view>
            <input class="form-input" type="text" v-model="tempCompanyInfo.companyName" placeholder="请输入公司名称" />
          </view>

          <view class="form-item">
            <view class="form-label">法人名称</view>
            <input class="form-input" type="text" v-model="tempCompanyInfo.legalPerson" placeholder="请输入法人名称" />
          </view>

          <view class="form-item">
            <view class="form-label">营业执照号</view>
            <input class="form-input" type="text" v-model="tempCompanyInfo.businessLicenseCode" placeholder="请输入营业执照号" />
          </view>

          <view class="form-item">
            <view class="form-label">税号</view>
            <input class="form-input" type="text" v-model="tempCompanyInfo.taxNumber" placeholder="请输入税号" />
          </view>
        </view>
        
        <view class="dialog-footer">
          <view class="cancel-btn" @click="closeEditDialog">取消</view>
          <view class="confirm-btn" @click="saveCompanyInfo">确认</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { saveLoginUser } from "../../api/transport";

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif
  
  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif
  
  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '104rpx';
  
  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif
  
  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 页面参数
const userType = ref('');
const isOptional = ref(false);

// 营业执照图片
const businessLicensePath = ref('');

// 是否同意协议
const isAgreed = ref(false);

// 编辑弹窗显示控制
const showEditDialog = ref(false);

// 营业执照信息
const companyInfo = reactive({
  companyName: '',           // 公司名称
  legalPerson: '',    // 法人名称
  businessLicenseCode: '',   // 营业执照号
  taxNumber: ''       // 税号
});

// 临时存储编辑中的营业执照信息
const tempCompanyInfo = reactive({
  companyName: '',
  legalPerson: '',
  businessLicenseCode: '',
  taxNumber: ''
});

// 表单是否有效
const isFormValid = computed(() => {
  // 图片已上传，信息已填写，并且同意协议
  return businessLicensePath.value &&
         companyInfo.companyName &&
         companyInfo.legalPerson &&
         companyInfo.businessLicenseCode &&
         companyInfo.taxNumber &&
         isAgreed.value;
});

// 处理返回按钮
const handleBack = () => {
  uni.navigateBack();
};

// 处理上传成功
const handleUploadSuccess = (res: any) => {
  console.log('营业执照上传成功', res);
  
  // 存储图片路径
  businessLicensePath.value = res.url;
  
  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理上传失败
const handleUploadFail = (err: any) => {
  console.error('上传失败', err);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
    duration: 2000
  });
};

// 打开编辑弹窗
const openEditDialog = () => {
  // 复制当前信息到临时变量
  tempCompanyInfo.companyName = companyInfo.companyName;
  tempCompanyInfo.legalPerson = companyInfo.legalPerson;
  tempCompanyInfo.businessLicenseCode = companyInfo.businessLicenseCode;
  tempCompanyInfo.taxNumber = companyInfo.taxNumber;

  console.log('打开编辑弹窗，临时数据：', JSON.stringify(tempCompanyInfo));
  showEditDialog.value = true;
};

// 关闭编辑弹窗
const closeEditDialog = () => {
  showEditDialog.value = false;
};

// 保存营业执照信息
const saveCompanyInfo = () => {
  console.log('保存前临时数据：', JSON.stringify(tempCompanyInfo));

  // 验证必填字段
  if (!tempCompanyInfo.companyName ||
      !tempCompanyInfo.legalPerson ||
      !tempCompanyInfo.businessLicenseCode ||
      !tempCompanyInfo.taxNumber) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 保存信息
  companyInfo.companyName = tempCompanyInfo.companyName;
  companyInfo.legalPerson = tempCompanyInfo.legalPerson;
  companyInfo.businessLicenseCode = tempCompanyInfo.businessLicenseCode;
  companyInfo.taxNumber = tempCompanyInfo.taxNumber;

  console.log('保存后数据：', JSON.stringify(companyInfo));

  // 关闭弹窗
  closeEditDialog();

  // 提示成功
  uni.showToast({
    title: '信息已保存',
    icon: 'success',
    duration: 1500
  });
};

// 提交
const handleSubmit = () => {
  // 表单验证
  if (!isFormValid.value) {
    let errorMsg = '';

    if (!businessLicensePath.value) {
      errorMsg = '请上传营业执照';
    } else if (!companyInfo.companyName ||
               !companyInfo.legalPerson ||
               !companyInfo.businessLicenseCode ||
               !companyInfo.taxNumber) {
      errorMsg = '请填写完整的营业执照信息';
    } else if (!isAgreed.value) {
      errorMsg = '请同意用户协议';
    }

    if (errorMsg) {
      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
      return;
    }
  }

  // 显示加载中
  uni.showLoading({
    title: '提交中...',
    mask: true
  });

  // 调用真实接口提交营业执照信息
  saveLoginUser({
    companyName: companyInfo.companyName,
    legalPerson: companyInfo.legalPerson,
    businessLicenseCode: companyInfo.businessLicenseCode,
    taxNumber: companyInfo.taxNumber,
    businessLicensePath: businessLicensePath.value
  }).then((response: any) => {
    uni.hideLoading();

    if (response.code == 700) {
      uni.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 1500
      });

      // 提交成功后返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      // 处理接口返回的错误信息
      uni.showToast({
        title: response.message || '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }).catch((error: any) => {
    uni.hideLoading();
    console.error('营业执照提交失败：', error);

    uni.showToast({
      title: '网络错误，请检查网络连接后重试',
      icon: 'none',
      duration: 2000
    });
  });
};

// 参数有效性检查辅助函数
const isValidParam = (param: any): boolean => {
  return param && param !== 'undefined' && param !== 'null' && param.trim() !== '';
};

// 安全解码URL参数
const safeDecodeURIComponent = (param: any): string => {
  if (!isValidParam(param)) {
    return '';
  }

  try {
    return decodeURIComponent(param);
  } catch (error) {
    console.warn('URL参数解码失败：', param, error);
    return '';
  }
};

// 页面加载时获取用户类型参数
onLoad((options: any) => {
  if (options?.type) {
    userType.value = options.type;
  }

  if (options?.isOptional) {
    isOptional.value = options.isOptional === 'true';
  }

  // 返显营业执照图片
  if (isValidParam(options?.imgUrl)) {
    businessLicensePath.value = options.imgUrl;
  }

  // 返显营业执照信息
  const decodedCompanyName = safeDecodeURIComponent(options?.companyName);
  if (decodedCompanyName) {
    companyInfo.companyName = decodedCompanyName;
  }

  const decodedLegalPerson = safeDecodeURIComponent(options?.legalPerson);
  if (decodedLegalPerson) {
    companyInfo.legalPerson = decodedLegalPerson;
  }

  const decodedBusinessLicenseCode = safeDecodeURIComponent(options?.businessLicenseCode);
  if (decodedBusinessLicenseCode) {
    companyInfo.businessLicenseCode = decodedBusinessLicenseCode;
  }

  const decodedTaxNumber = safeDecodeURIComponent(options?.taxNumber);
  if (decodedTaxNumber) {
    companyInfo.taxNumber = decodedTaxNumber;
  }

  // 如果有营业执照图片，说明用户之前已经同意过协议，自动勾选协议同意状态
  if (businessLicensePath.value) {
    isAgreed.value = true;
  }

  console.log('营业执照页面参数接收完成：', {
    userType: userType.value,
    isOptional: isOptional.value,
    businessLicensePath: businessLicensePath.value,
    companyInfo: JSON.stringify(companyInfo),
    isAgreed: isAgreed.value,
    originalParams: {
      imgUrl: options?.imgUrl,
      companyName: options?.companyName,
      legalPerson: options?.legalPerson,
      businessLicenseCode: options?.businessLicenseCode,
      taxNumber: options?.taxNumber
    }
  });
});
</script>

<style scoped lang="scss">
.business-card-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  box-sizing: border-box;
  position: relative;
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;
  
  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      width: 40rpx;
      height: 40rpx;
      position: relative;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 3rpx;
        background-color: #333;
        left: 4rpx;
        border-radius: 2rpx;
      }
      
      &::before {
        top: 12rpx;
        transform: rotate(-45deg);
      }
      
      &::after {
        bottom: 12rpx;
        transform: rotate(45deg);
      }
    }
    
    &:active {
      opacity: 0.7;
    }
  }
  
  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }
  
  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.content-area {
  padding: 20rpx 30rpx 40rpx;
}

// 上传必要说明
.upload-requirement {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  .required-symbol {
    color: #ff6b88;
    font-size: 32rpx;
    margin-right: 6rpx;
  }
  
  .requirement-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    flex: 1;
  }
}

// 上传区域
.upload-section {
  margin-bottom: 40rpx;
  
  .upload-item {
    width: 100%;
    margin-bottom: 20rpx;
    
    .upload-preview-container {
      width: 100%;
      height: 460rpx;
      border-radius: 16rpx;
      overflow: hidden;
    }
  }
  
  .tip-text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      margin-right: 8rpx;
      color: #ff9cac;
      font-size: 28rpx;
    }
  }
}

// 自定义上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .upload-icon {
    width: 80rpx;
    height: 80rpx;
    border: 4rpx dashed #ddd;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    position: relative;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      background-color: #ddd;
    }
    
    &::before {
      width: 4rpx;
      height: 40rpx;
      top: 20rpx;
      left: 38rpx;
    }
    
    &::after {
      width: 40rpx;
      height: 4rpx;
      top: 38rpx;
      left: 20rpx;
    }
  }
  
  .upload-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 营业执照信息区域
.license-info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
    
    .edit-btn {
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 30rpx;
      
      .edit-text {
        font-size: 26rpx;
        color: #666;
        margin-right: 6rpx;
      }
      
      .iconfont {
        font-size: 24rpx;
        color: #666;
      }
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .info-container {
    .info-row {
      display: flex;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f5f5f5;
      justify-content: space-between;
      align-items: center;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info-label {
        width: 180rpx;
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
      }
      
      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        text-align: right;
        padding-left: 20rpx;
      }
    }
  }
}

// 协议同意区域
.agreement-section {
  display: flex;
  align-items: center;
  margin: 40rpx 0 30rpx;
  padding: 10rpx 0;
  
  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;
    flex-shrink: 0;
    
    .checkbox-inner {
      width: 24rpx;
      height: 24rpx;
      border-radius: 50%;
      background-color: transparent;
      transition: all 0.2s;
      
      &.checked {
        background-color: #ff6b88;
      }
    }
  }
  
  .agreement-text {
    font-size: 28rpx;
    color: #666;
  }
}

// 提交按钮
.submit-btn {
  margin: 20rpx 0 40rpx;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);
  
  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &.disabled {
    opacity: 0.6;
  }
}

// 编辑弹窗
.edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 900;
  
  .dialog-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .dialog-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    transform: translateY(0);
    transition: transform 0.3s;
    z-index: 901;
    
    .dialog-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30rpx;
      position: relative;
      border-bottom: 1rpx solid #f5f5f5;
      
      .dialog-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }
      
      .close-btn {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40rpx;
        color: #999;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .dialog-content {
      padding: 30rpx;
      max-height: 70vh;
      overflow-y: auto;
      
      .form-item {
        margin-bottom: 30rpx;
        
        .form-label {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 15rpx;
        }
        
        .form-input {
          width: 100%;
          height: 80rpx;
          background-color: #f8f8f8;
          border-radius: 8rpx;
          padding: 0 20rpx;
          box-sizing: border-box;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
    
    .dialog-footer {
      display: flex;
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #f5f5f5;
      
      .cancel-btn, .confirm-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
      }
      
      .cancel-btn {
        background-color: #f8f8f8;
        color: #666;
        margin-right: 20rpx;
      }
      
      .confirm-btn {
        background: linear-gradient(90deg, #ff6b88, #ff9cac);
        color: #fff;
      }
    }
  }
}
</style>