<template>
  <view class="driving-card-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>
    
    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <view class="back-btn" @click="handleBack">
        <view class="back-icon"></view>
      </view>
      <text class="title">行驶证认证</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingBottom: safeAreaBottom, marginTop: headerTotalHeight }">
      <!-- 上传必要说明 -->
      <view class="upload-requirement">
        <text class="required-symbol">*</text>
        <text class="requirement-text">上传行驶证</text>
        <!-- <view class="permit-link" @click="goToPermitTypes">
          <text>允许准入车型</text>
          <text class="iconfont icon-owner-youji<PERSON>ou"></text>
        </view> -->
      </view>
      
      <!-- 行驶证上传区域 -->
      <view class="upload-section">
        <view class="upload-area">
          <!-- 正面和副页上传(横向排列) -->
          <view class="upload-row">
            <!-- 主页上传 -->
            <view class="upload-item">
              <view class="upload-preview-container">
                <cu-upload
                  v-model="frontImage"
                  height="260rpx"
                  placeholder="上传行驶证主页"
                  @success="handleFrontUploadSuccess"
                  @fail="handleUploadFail"
                >
                  <template #placeholder>
                    <view class="upload-placeholder">
                      <view class="upload-icon"></view>
                      <text class="upload-text">上传行驶证主页</text>
                    </view>
                  </template>
                </cu-upload>
              </view>
            </view>
            
            <!-- 副页上传 -->
            <view class="upload-item">
              <view class="upload-preview-container">
                <cu-upload
                  v-model="backImage"
                  height="260rpx"
                  placeholder="上传行驶证副页"
                  @success="handleBackUploadSuccess"
                  @fail="handleUploadFail"
                >
                  <template #placeholder>
                    <view class="upload-placeholder">
                      <view class="upload-icon"></view>
                      <text class="upload-text">上传行驶证副页</text>
                    </view>
                  </template>
                </cu-upload>
              </view>
            </view>
          </view>
          
          <!-- 副页背面上传区域 -->
          <!-- <view class="back-info-area">
            <view class="back-info-header">
              <text>上传行驶证副页背面</text>
              <view class="help-link" @click="showBackHelp">
                <text>什么是副页背面？</text>
                <text class="iconfont icon-owner-youjiantou"></text>
              </view>
            </view>
            
            <view class="upload-item full-width">
              <view class="upload-preview-container back-page">
                <cu-upload
                  v-model="backPageImage"
                  height="360rpx"
                  placeholder="上传行驶证副页背面"
                  @success="handleBackPageUploadSuccess"
                  @fail="handleUploadFail"
                >
                  <template #placeholder>
                    <view class="upload-placeholder">
                      <view class="upload-icon"></view>
                      <text class="upload-text">上传行驶证副页背面</text>
                    </view>
                  </template>
                </cu-upload>
              </view>
            </view>
          </view> -->
        </view>
      </view>
      
      <!-- 行驶证信息区域 -->
      <view class="license-info-section">
        <view class="section-header">
          <text class="section-title">行驶证信息</text>
          <view class="edit-btn" @click="openEditDialog">
            <text class="edit-text">编辑</text>
            <text class="iconfont icon-owner-bianji"></text>
          </view>
        </view>
        
        <view class="info-container">
          <!-- <view class="info-row">
            <view class="info-label">车型车长</view>
            <view class="info-value">{{ vehicleInfo.modelLength || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">车牌号</view>
            <view class="info-value">{{ vehicleInfo.plateNumber || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">车辆识别号</view>
            <view class="info-value">{{ vehicleInfo.vin || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">车辆类型</view>
            <view class="info-value">{{ vehicleInfo.vehicleType || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">使用性质</view>
            <view class="info-value">{{ vehicleInfo.useNature || '未填写' }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">年审有效期</view>
            <view class="info-value">{{ vehicleInfo.inspectionValidDate || '未填写' }}</view>
          </view> -->
					
					<view class="info-row">
					  <view class="info-label">所有人</view>
					  <view class="info-value">{{ vehicleInfo.name || '未填写' }}</view>
					</view>
        </view>
      </view>
      
      <!-- 重要提示信息 -->
      <view class="important-notice">
        <view class="notice-icon">①</view>
        <text class="notice-text">如果您是挂车，请优先上传牵引车行驶证，审核通过后，再上传挂车行驶证，以免影响接单。</text>
      </view>
      
      <!-- 协议同意区域 -->
      <view class="agreement-section">
        <view class="checkbox" @click="isAgreed = !isAgreed">
          <view class="checkbox-inner" :class="{ 'checked': isAgreed }"></view>
        </view>
        <text class="agreement-text">您同意我们收集行驶证用于承运资质审核</text>
      </view>

      <!-- 提交按钮 -->
      <view 
        class="submit-btn" 
        :class="{ 'disabled': !isFormValid }"
        @click="handleSubmit"
      >
        <text>提交审核</text>
      </view>
    </view>
    
    <!-- 编辑弹窗 -->
    <view class="edit-dialog" v-if="showEditDialog">
      <view class="dialog-mask" @click="closeEditDialog"></view>
      <view class="dialog-container" :style="{ paddingBottom: safeAreaBottom }">
        <view class="dialog-header">
          <text class="dialog-title">编辑行驶证信息</text>
          <view class="close-btn" @click="closeEditDialog">×</view>
        </view>
        
        <view class="dialog-content">
          <!-- <view class="form-item">
            <view class="form-label">车型车长</view>
            <input class="form-input" type="text" v-model="tempVehicleInfo.modelLength" placeholder="请输入车型车长，如：高栏/13.0米" />
          </view>
          
          <view class="form-item">
            <view class="form-label">车牌号</view>
            <input class="form-input" type="text" v-model="tempVehicleInfo.plateNumber" placeholder="请输入车牌号" />
          </view>
          
          <view class="form-item">
            <view class="form-label">车辆识别号</view>
            <input class="form-input" type="text" v-model="tempVehicleInfo.vin" placeholder="请输入车辆识别号" />
          </view>
          
          <view class="form-item">
            <view class="form-label">车辆类型</view>
            <input class="form-input" type="text" v-model="tempVehicleInfo.vehicleType" placeholder="请输入车辆类型，如：重型半挂牵引车" />
          </view>
          
          <view class="form-item">
            <view class="form-label">使用性质</view>
            <input class="form-input" type="text" v-model="tempVehicleInfo.useNature" placeholder="请输入使用性质，如：货运" />
          </view>
          
          <view class="form-item">
            <view class="form-label">年审有效期</view>
            <picker 
              class="form-picker" 
              mode="date" 
              :value="tempVehicleInfo.inspectionValidDate" 
              @change="onInspectionDateChange"
              fields="day"
              style="position: relative; z-index: 10000;"
            >
              <view class="picker-value">
                <text>{{ tempVehicleInfo.inspectionValidDate || '请选择日期' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view> -->
					
					<view class="form-item">
					  <view class="form-label">所有人</view>
					  <input class="form-input" type="text" v-model="tempVehicleInfo.name" placeholder="车辆所有人" />
					</view>
        </view>
        
        <view class="dialog-footer">
          <view class="cancel-btn" @click="closeEditDialog">取消</view>
          <view class="confirm-btn" @click="saveVehicleInfo">确认</view>
        </view>
      </view>
    </view>

    <!-- 添加一个单独的日期选择弹层 -->
    <view class="date-picker-modal" v-if="showDatePicker">
      <view class="date-picker-mask" @click="closeDatePicker"></view>
      <view class="date-picker-container" :style="{ paddingBottom: safeAreaBottom }">
        <view class="date-picker-header">
          <text class="cancel-btn" @click="closeDatePicker">取消</text>
          <text class="title">选择日期</text>
          <text class="confirm-btn" @click="confirmDatePicker">确定</text>
        </view>
        <picker-view 
          class="date-picker-view" 
          :value="datePickerValue" 
          @change="onDatePickerChange"
          indicator-style="height: 80rpx;"
          :mask-style="'background: linear-gradient(180deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.9));'"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(year, index) in years" :key="'year-'+index">{{year}}年</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(month, index) in months" :key="'month-'+index">{{month}}月</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(day, index) in days" :key="'day-'+index">{{day}}日</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { saveLoginUser } from "../../api/transport";

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif
  
  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif
  
  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '104rpx';
  
  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif
  
  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 页面参数
const userType = ref('');
const isOptional = ref(false);

// 行驶证图片
const frontImage = ref('');  // 行驶证主页
const backImage = ref('');   // 行驶证副页
const backPageImage = ref(''); // 行驶证副页背面

// 是否同意协议
const isAgreed = ref(false);

// 行驶证信息
const vehicleInfo = reactive({
  // modelLength: '',        // 车型车长
  // plateNumber: '',        // 车牌号
  // vin: '',                // 车辆识别号
  // vehicleType: '',        // 车辆类型
  // useNature: '',          // 使用性质
  // inspectionValidDate: '', // 年审有效期
	name: ''
});

// 临时存储编辑中的行驶证信息
const tempVehicleInfo = reactive({
  // modelLength: '',
  // plateNumber: '',
  // vin: '',
  // vehicleType: '',
  // useNature: '',
  // inspectionValidDate: '',
	name: ''
});

// 编辑弹窗显示控制
const showEditDialog = ref(false);

// 表单是否有效
const isFormValid = computed(() => {
  // 所有图片都上传，并且同意协议，并且行驶证信息已填写
  return frontImage.value && 
         backImage.value && 
         isAgreed.value && 
         // vehicleInfo.modelLength && 
         // vehicleInfo.plateNumber && 
         // vehicleInfo.vin &&
         // vehicleInfo.vehicleType &&
         // vehicleInfo.useNature &&
         // vehicleInfo.inspectionValidDate
				 vehicleInfo.name
				 ;
});

// 监视vehicleInfo变化，用于调试
watch(vehicleInfo, (newVal) => {
  console.log('vehicleInfo 已更新:', JSON.stringify(newVal));
}, { deep: true });

// 处理返回按钮
const handleBack = () => {
  uni.navigateBack();
};

// 显示副页背面帮助
const showBackHelp = () => {
  uni.showModal({
    title: '行驶证副页背面',
    content: '行驶证副页背面是指行驶证副页反面的照片，包含了车辆检验记录等信息。',
    showCancel: false
  });
};

// 跳转到准入车型页面
const goToPermitTypes = () => {
  // TODO 这里写个弹窗，将允许准入车型格式列出来，暂时没有
};

// 处理主页照片上传成功
const handleFrontUploadSuccess = (res: any) => {
  console.log('行驶证主页上传成功', res);
  
  // 存储图片路径
  frontImage.value = res.url;
  
  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理副页照片上传成功
const handleBackUploadSuccess = (res: any) => {
  console.log('行驶证副页上传成功', res);
  
  // 存储图片路径
  backImage.value = res.url;
  
  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理副页背面照片上传成功
const handleBackPageUploadSuccess = (res: any) => {
  console.log('行驶证副页背面上传成功', res);
  
  // 存储图片路径
  backPageImage.value = res.url;
  
  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理上传失败
const handleUploadFail = (err: any) => {
  console.error('上传失败', err);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
    duration: 2000
  });
};

// 打开编辑弹窗
const openEditDialog = () => {
  // 复制当前信息到临时变量
  // tempVehicleInfo.modelLength = vehicleInfo.modelLength;
  // tempVehicleInfo.plateNumber = vehicleInfo.plateNumber;
  // tempVehicleInfo.vin = vehicleInfo.vin;
  // tempVehicleInfo.vehicleType = vehicleInfo.vehicleType;
  // tempVehicleInfo.useNature = vehicleInfo.useNature;
  // tempVehicleInfo.inspectionValidDate = vehicleInfo.inspectionValidDate;
	tempVehicleInfo.name = vehicleInfo.name;
  
  console.log('打开编辑弹窗，临时数据：', JSON.stringify(tempVehicleInfo));
  showEditDialog.value = true;
};

// 保存行驶证信息
const saveVehicleInfo = () => {
  console.log('保存前临时数据：', JSON.stringify(tempVehicleInfo));
  
  // 验证必填字段
  if (
		// !tempVehicleInfo.modelLength || 
  //     !tempVehicleInfo.plateNumber || 
  //     !tempVehicleInfo.vin ||
  //     !tempVehicleInfo.vehicleType ||
  //     !tempVehicleInfo.useNature ||
  //     !tempVehicleInfo.inspectionValidDate
			!tempVehicleInfo.name
			) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }
  
  // 保存信息
  // vehicleInfo.modelLength = tempVehicleInfo.modelLength;
  // vehicleInfo.plateNumber = tempVehicleInfo.plateNumber;
  // vehicleInfo.vin = tempVehicleInfo.vin;
  // vehicleInfo.vehicleType = tempVehicleInfo.vehicleType;
  // vehicleInfo.useNature = tempVehicleInfo.useNature;
  // vehicleInfo.inspectionValidDate = tempVehicleInfo.inspectionValidDate;
	
	vehicleInfo.name = tempVehicleInfo.name;
  
  console.log('保存后数据：', JSON.stringify(vehicleInfo));
  
  // 关闭弹窗
  closeEditDialog();
  
  // 提示成功
  uni.showToast({
    title: '信息已保存',
    icon: 'success',
    duration: 1500
  });
};

// 关闭编辑弹窗
const closeEditDialog = () => {
  showEditDialog.value = false;
};

// 日期选择器相关
const showDatePicker = ref(false);
const datePickerType = ref(''); // 当前正在编辑的日期类型
const datePickerValue = ref([0, 0, 0]); // 年、月、日的索引值
const years = ref<number[]>([]);
const months = ref<number[]>([]);
const days = ref<number[]>([]);
const tempPickerDate = ref(''); // 临时存储选择的日期

// 初始化日期选择器数据
const initDatePicker = () => {
  const currentYear = new Date().getFullYear();
  // 生成年份数据，从当前年份向前20年，向后20年
  years.value = Array.from({length: 41}, (_, i) => currentYear - 20 + i);
  // 生成月份数据
  months.value = Array.from({length: 12}, (_, i) => i + 1);
  // 初始化天数据，先默认31天
  updateDays(currentYear, 1);
};

// 根据年月更新天数
const updateDays = (year: number, month: number) => {
  const daysInMonth = new Date(year, month, 0).getDate();
  days.value = Array.from({length: daysInMonth}, (_, i) => i + 1);
};

// 打开日期选择器
const openDatePicker = (type: string) => {
  datePickerType.value = type;
  const dateStr = tempVehicleInfo[type as keyof typeof tempVehicleInfo] as string;
  
  if (dateStr) {
    // 如果已有日期，设置选择器初始值
    const [year, month, day] = dateStr.split('-').map(Number);
    const yearIndex = years.value.findIndex(y => y === year);
    const monthIndex = months.value.findIndex(m => m === month);
    
    updateDays(year, month);
    
    const dayIndex = days.value.findIndex(d => d === day);
    datePickerValue.value = [
      yearIndex > -1 ? yearIndex : 0,
      monthIndex > -1 ? monthIndex : 0,
      dayIndex > -1 ? dayIndex : 0
    ];
  } else {
    // 默认选择当前日期
    const now = new Date();
    const yearIndex = years.value.findIndex(y => y === now.getFullYear());
    const monthIndex = now.getMonth(); // 月份从0开始
    
    updateDays(now.getFullYear(), now.getMonth() + 1);
    
    datePickerValue.value = [
      yearIndex > -1 ? yearIndex : 0,
      monthIndex,
      now.getDate() - 1 // 天数从1开始，索引从0开始
    ];
  }
  
  tempPickerDate.value = '';
  showDatePicker.value = true;
};

// 关闭日期选择器
const closeDatePicker = () => {
  showDatePicker.value = false;
};

// 确认日期选择
const confirmDatePicker = () => {
  // if (tempPickerDate.value) {
  //   if (datePickerType.value === 'inspectionValidDate') {
  //     tempVehicleInfo.inspectionValidDate = tempPickerDate.value;
  //   }
  //   // 如果有其他日期字段，可以在这里添加处理
  // }
  // closeDatePicker();
};

// 日期选择变化
const onDatePickerChange = (e: any) => {
  const values = e.detail.value;
  const year = years.value[values[0]];
  const month = months.value[values[1]];
  
  // 更新天数
  updateDays(year, month);
  
  // 如果选择的天超过了当月的天数，则调整
  if (values[2] >= days.value.length) {
    values[2] = days.value.length - 1;
  }
  
  const day = days.value[values[2]];
  
  // 格式化日期为YYYY-MM-DD
  const formatMonth = month < 10 ? `0${month}` : `${month}`;
  const formatDay = day < 10 ? `0${day}` : `${day}`;
  tempPickerDate.value = `${year}-${formatMonth}-${formatDay}`;
  
  // 更新选择器的值
  datePickerValue.value = values;
};

// 日期选择变化
const onInspectionDateChange = (e: any) => {
  // console.log('日期选择变化：', e.detail.value);
  // tempVehicleInfo.inspectionValidDate = e.detail.value;
};

// 提交
const handleSubmit = () => {
  // 表单验证
  if (!isFormValid.value) {
    let errorMsg = '';
    
    if (!frontImage.value || !backImage.value) {
      errorMsg = '请上传行驶证主页和副页';
    } else if (
			// !vehicleInfo.modelLength || 
   //             !vehicleInfo.plateNumber || 
   //             !vehicleInfo.vin ||
   //             !vehicleInfo.vehicleType ||
   //             !vehicleInfo.useNature ||
   //             !vehicleInfo.inspectionValidDate
			!vehicleInfo.name
							 ) {
      errorMsg = '请填写完整的行驶证信息';
    } else if (!isAgreed.value) {
      errorMsg = '请同意用户协议';
    }
    
    if (errorMsg) {
      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
      return;
    }
  }
  
  // 显示加载中
  uni.showLoading({
    title: '提交中...',
    mask: true
  });
	
	saveLoginUser({
		vehicleLicensePath: frontImage.value,
    vehicleLicenseBackPath: backImage.value,
		vehicleOwner: vehicleInfo.name
	}).then((response: any) => {
		if (response.code == 700) {
			uni.hideLoading();
			uni.showToast({
			  title: '提交成功',
			  icon: 'success',
			  duration: 1500
			});
			
			// 提交成功后返回上一页
			setTimeout(() => {
			  uni.navigateBack();
			}, 1500);
		}
	})
};

// 参数有效性检查辅助函数
const isValidParam = (param: any): boolean => {
  return param && param !== 'undefined' && param !== 'null' && param.trim() !== '';
};

// 安全解码URL参数
const safeDecodeURIComponent = (param: any): string => {
  if (!isValidParam(param)) {
    return '';
  }

  try {
    return decodeURIComponent(param);
  } catch (error) {
    console.warn('URL参数解码失败：', param, error);
    return '';
  }
};

// 页面加载时获取用户类型参数
onLoad((options: any) => {
  if (options?.type) {
    userType.value = options.type;
  }

  if (options?.isOptional) {
    isOptional.value = options.isOptional === 'true';
  }

  // 返显行驶证图片
  if (isValidParam(options?.imgUrl)) {
    frontImage.value = options.imgUrl;
  }

  if (isValidParam(options?.imgUrl2)) {
    backImage.value = options.imgUrl2;
  }

  // 返显行驶证信息
  const decodedFullName = safeDecodeURIComponent(options?.fullName);
  if (decodedFullName) {
    vehicleInfo.name = decodedFullName;
  }

  // 如果有行驶证图片，说明用户之前已经同意过协议，自动勾选协议同意状态
  if (frontImage.value || backImage.value) {
    isAgreed.value = true;
  }

  console.log('行驶证页面参数接收完成：', {
    userType: userType.value,
    isOptional: isOptional.value,
    frontImage: frontImage.value,
    backImage: backImage.value,
    vehicleInfo: JSON.stringify(vehicleInfo),
    isAgreed: isAgreed.value,
    originalParams: {
      imgUrl: options?.imgUrl,
      imgUrl2: options?.imgUrl2,
      fullName: options?.fullName
    }
  });

  // 初始化日期选择器
  // initDatePicker();
});
</script>

<style lang="scss" scoped>
.driving-card-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;
  
  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      width: 40rpx;
      height: 40rpx;
      position: relative;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 3rpx;
        background-color: #333;
        left: 4rpx;
        border-radius: 2rpx;
      }
      
      &::before {
        top: 12rpx;
        transform: rotate(-45deg);
      }
      
      &::after {
        bottom: 12rpx;
        transform: rotate(45deg);
      }
    }
    
    &:active {
      opacity: 0.7;
    }
  }
  
  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }
  
  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.content-area {
  padding: 20rpx 30rpx 40rpx;
}

// 上传必要说明
.upload-requirement {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  .required-symbol {
    color: #ff6b88;
    font-size: 32rpx;
    margin-right: 6rpx;
  }
  
  .requirement-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    flex: 1;
  }
  
  .permit-link {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #ff6b88;
    
    .iconfont {
      margin-left: 4rpx;
      font-size: 24rpx;
      display: flex;
      align-items: center;
    }
  }
}

// 上传区域
.upload-section {
  margin-bottom: 40rpx;
  
  .upload-area {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    
    .upload-row {
      display: flex;
      gap: 20rpx;
      
      .upload-item {
        flex: 1;
        
        .upload-preview-container {
          width: 100%;
          height: 260rpx;
          border-radius: 16rpx;
          overflow: hidden;
        }
      }
    }
    
    .back-info-area {
      margin-top: 20rpx;
      
      .back-info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15rpx;
        
        text {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
        }
        
        .help-link {
          display: flex;
          align-items: center;
          font-size: 26rpx;
          color: #999;
          
          .iconfont {
            margin-left: 4rpx;
            font-size: 24rpx;
            display: flex;
            align-items: center;
          }
        }
      }
      
      .full-width {
        width: 100%;
      }
      
      .back-page {
        height: 360rpx;
      }
    }
  }
}

// 自定义上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .upload-icon {
    width: 80rpx;
    height: 80rpx;
    border: 4rpx dashed #ddd;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    position: relative;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      background-color: #ddd;
    }
    
    &::before {
      width: 4rpx;
      height: 40rpx;
      top: 20rpx;
      left: 38rpx;
    }
    
    &::after {
      width: 40rpx;
      height: 4rpx;
      top: 38rpx;
      left: 20rpx;
    }
  }
  
  .upload-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 行驶证信息区域
.license-info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
    
    .edit-btn {
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 30rpx;
      
      .edit-text {
        font-size: 26rpx;
        color: #666;
        margin-right: 6rpx;
      }
      
      .iconfont {
        font-size: 24rpx;
        color: #666;
      }
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .info-container {
    .info-row {
      display: flex;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f5f5f5;
      justify-content: space-between;
      align-items: center;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info-label {
        width: 180rpx;
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
      }
      
      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        text-align: right;
        padding-left: 20rpx;
      }
    }
  }
}

// 重要提示区域
.important-notice {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  margin: 0 0 20rpx;
  
  .notice-icon {
    width: 38rpx;
    height: 38rpx;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #666;
    margin-right: 12rpx;
    flex-shrink: 0;
  }
  
  .notice-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

// 协议同意区域
.agreement-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 10rpx 0;
  
  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;
    flex-shrink: 0;
    
    .checkbox-inner {
      width: 24rpx;
      height: 24rpx;
      border-radius: 50%;
      background-color: transparent;
      transition: all 0.2s;
      
      &.checked {
        background-color: #ff6b88;
      }
    }
  }
  
  .agreement-text {
    font-size: 28rpx;
    color: #666;
  }
}

// 提交按钮
.submit-btn {
  margin: 20rpx 0 40rpx;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);
  
  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &.disabled {
    opacity: 0.6;
  }
}

// 编辑弹窗
.edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 900;
  
  .dialog-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .dialog-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    transform: translateY(0);
    transition: transform 0.3s;
    z-index: 901;
    
    .dialog-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30rpx;
      position: relative;
      border-bottom: 1rpx solid #f5f5f5;
      
      .dialog-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }
      
      .close-btn {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40rpx;
        color: #999;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .dialog-content {
      padding: 30rpx;
      max-height: 70vh;
      overflow-y: auto;
      
      .form-item {
        margin-bottom: 30rpx;
        position: relative;
        z-index: 1010;
        
        .form-label {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 15rpx;
        }
        
        .form-input {
          width: 100%;
          height: 80rpx;
          background-color: #f8f8f8;
          border-radius: 8rpx;
          padding: 0 20rpx;
          box-sizing: border-box;
          font-size: 28rpx;
          color: #333;
        }
        
        .date-picker-wrapper {
          width: 100%;
          height: 80rpx;
          background-color: #f8f8f8;
          border-radius: 8rpx;
          padding: 0 20rpx;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          position: relative;
          
          .form-picker {
            font-size: 28rpx;
            color: #333;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
          }
          
          .iconfont {
            margin-left: 4rpx;
            font-size: 24rpx;
            display: flex;
            align-items: center;
          }
        }
      }
    }
    
    .dialog-footer {
      display: flex;
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #f5f5f5;
      
      .cancel-btn, .confirm-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
      }
      
      .cancel-btn {
        background-color: #f8f8f8;
        color: #666;
        margin-right: 20rpx;
      }
      
      .confirm-btn {
        background: linear-gradient(90deg, #ff6b88, #ff9cac);
        color: #fff;
      }
    }
  }
}

// 日期选择包装器
.date-picker-wrapper {
  width: 100%;
  
  .form-picker {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .iconfont {
      font-size: 36rpx;
      color: #999;
    }
  }
}

// 日期选择弹层
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  
  .date-picker-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .date-picker-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    
    .date-picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f5f5f5;
      
      .title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }
      
      .cancel-btn, .confirm-btn {
        font-size: 30rpx;
        padding: 10rpx 20rpx;
      }
      
      .cancel-btn {
        color: #666;
      }
      
      .confirm-btn {
        color: #ff6b88;
      }
    }
    
    .date-picker-view {
      width: 100%;
      height: 400rpx;
      
      .picker-item {
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        color: #333;
      }
    }
  }
}

.form-picker {
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  
  .picker-value {
    font-size: 28rpx;
    color: #333;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .picker-arrow {
      font-size: 24rpx;
      color: #999;
    }
  }
}

// 确保picker显示在最上层
uni-picker {
  z-index: 9999 !important;
}
</style>