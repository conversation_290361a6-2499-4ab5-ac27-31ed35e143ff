<template>
  <view class="auth-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>

    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <view class="back-btn" @click="handleBack">
        <view class="back-icon"></view>
      </view>
      <text class="title">{{ pageTitle }}</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 认证结果提示条 -->
    <view class="auth-result-tip" :style="{ top: headerTotalHeight }" v-if="loginUserInfo.auditStatus == 3">
      <view class="tip-icon">
        <text class="tip-icon-inner">!</text>
      </view>
      <view class="tip-content">
        <text class="tip-title">资质认证未通过</text>
        <text class="tip-desc">原因：人工审核，后台驳回</text>
      </view>
    </view>

    <!-- 资质列表区域 -->
    <scroll-view scroll-y class="auth-list" :style="{ paddingBottom: safeAreaBottom, marginTop: loginUserInfo.auditStatus == 3 ? 'calc(' + headerTotalHeight + ' + 120rpx)' : headerTotalHeight }">
      <view class="content-area">
        <!-- 司机资质 -->
        <template v-if="userType === 'driver'">
          <!-- 必填项 -->
          <view class="auth-item" v-for="(item, index) in driverDocs" :key="index">
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-desc">{{ item.desc || '证件缺失将影响您找货，请尽快上传' }}</text>
            </view>
            <view class="upload-btn" @click="goToUpload(item.title, item.optional)">
              <text>{{ getButtonText(item.title) }}</text>
            </view>
          </view>

          <!-- 选填分隔 -->
          <view class="divider">
            <text>以下信息为选填</text>
          </view>

          <!-- 选填项 -->
          <view class="auth-item" v-for="(item, index) in driverOptionalDocs" :key="index">
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-desc">{{ item.desc || '证件缺失将影响您找货，请尽快上传' }}</text>
            </view>
            <view class="upload-btn" @click="goToUpload(item.title, item.optional)">
              <text>{{ getButtonText(item.title) }}</text>
            </view>
          </view>
        </template>

        <!-- 企业货主资质 -->
        <template v-if="userType === 'shipper_company'">
          <view class="auth-item" v-for="(item, index) in shipperCompanyDocs" :key="index">
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-desc">{{ item.desc || '证件缺失将影响您发货，请尽快上传' }}</text>
            </view>
            <view class="upload-btn" @click="goToUpload(item.title, item.optional)">
              <text>{{ getButtonText(item.title) }}</text>
            </view>
          </view>
        </template>

        <!-- 配货站资质 -->
        <template v-if="userType === 'station'">
          <view class="auth-item" v-for="(item, index) in stationDocs" :key="index">
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-desc">{{ item.desc || '证件缺失将影响您使用平台，请尽快上传' }}</text>
            </view>
            <view class="upload-btn" @click="goToUpload(item.title, item.optional)">
              <text>{{ getButtonText(item.title) }}</text>
            </view>
          </view>
        </template>

        <!-- 个人货主提示 -->
        <template v-if="userType === 'shipper_personal'">
          <view class="no-auth-required">
            <view class="tip-icon">
              <text class="tip-icon-inner">✓</text>
            </view>
            <text class="tip-text">个人货主无需上传资质</text>
          </view>
        </template>

        <!-- 提交按钮 -->
        <view class="submit-btn" @click="goToIndex" v-if="userType !== 'shipper_personal'">
          <text>完成认证</text>
        </view>

        <!-- 底部安全区域占位 -->
        <view class="bottom-space"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { setStoragetItem } from '@/utils/storage';
import { getLoginUser, qualificationsAudit } from "../../api/transport";

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif

  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif

  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '124rpx';

  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif

  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 用户类型
const userType = ref('');

// 页面标题
const pageTitle = computed(() => {
  switch (userType.value) {
    case 'driver':
      return '司机资质认证';
    case 'shipper_personal':
      return '个人货主认证';
    case 'shipper_company':
      return '企业货主认证';
    case 'station':
      return '配货站认证';
    default:
      return '资质认证';
  }
});

// 检查证件是否已上传的辅助函数
const isDocUploaded = (docType: string): boolean => {
  if (!loginUserInfo.value) return false;

  switch (docType) {
    case '身份证':
      return !!(loginUserInfo.value.idCardPath && loginUserInfo.value.idCardBackPath);
    case '驾驶证':
      return !!(loginUserInfo.value.driverLicensePath && loginUserInfo.value.driverLicenseBackPath);
    case '行驶证':
      return !!(loginUserInfo.value.vehicleLicensePath && loginUserInfo.value.vehicleLicenseBackPath);
    case '营业执照':
      return !!loginUserInfo.value.businessLicensePath;
    case '授权书':
      return !!loginUserInfo.value.authorizationPath;
    case '道路运输证':
      return !!loginUserInfo.value.transportLicensePath;
    case '环保信息随车清单':
      return !!loginUserInfo.value.environmentalListPath;
    default:
      return false;
  }
};

// 获取动态描述文案
const getDocDesc = (docType: string, defaultDesc: string): string => {
  if (isDocUploaded(docType)) {
    return '证件已上传，点击可重新上传或查看';
  }
  return defaultDesc;
};

// 获取动态按钮文案
const getButtonText = (docType: string): string => {
  if (isDocUploaded(docType)) {
    return '查看/编辑';
  }
  return '去上传';
};

// 司机必填资质列表
const driverDocs = computed(() => [
  { title: '身份证', desc: getDocDesc('身份证', '当前证件缺少照片，请尽快更新'), optional: false },
  { title: '驾驶证', desc: getDocDesc('驾驶证', '证件缺失将影响您找货，请尽快上传'), optional: false },
  { title: '行驶证', desc: getDocDesc('行驶证', '证件缺失将影响您找货，请尽快上传'), optional: false },
]);

// 司机选填资质列表
const driverOptionalDocs = computed(() => [
  { title: '环保信息随车清单', desc: getDocDesc('环保信息随车清单', '证件缺失将影响您找货，请尽快上传'), optional: true },
  { title: '道路运输证', desc: getDocDesc('道路运输证', '证件缺失将影响您找货，请尽快上传'), optional: true },
]);

// 企业货主资质列表
const shipperCompanyDocs = computed(() => [
  { title: '身份证', desc: getDocDesc('身份证', '证件缺失将影响您发货，请尽快上传'), optional: false },
  { title: '营业执照', desc: getDocDesc('营业执照', '证件缺失将影响您发货，请尽快上传'), optional: false },
  { title: '授权书', desc: getDocDesc('授权书', '证件缺失将影响您发货，请尽快上传'), optional: true },
]);

// 配货站资质列表
const stationDocs = computed(() => [
  { title: '身份证', desc: getDocDesc('身份证', '证件缺失将影响您使用平台，请尽快上传'), optional: false },
  { title: '营业执照', desc: getDocDesc('营业执照', '证件缺失将影响您使用平台，请尽快上传'), optional: false },
  { title: '授权书', desc: getDocDesc('授权书', '证件缺失将影响您使用平台，请尽快上传'), optional: true },
]);

// 跳转到上传页面
const goToUpload = (docType: string, isOptional: boolean) => {
  // 如果是身份证，跳转到专门的身份证上传页面
  if (docType === '身份证') {
    // 安全获取参数，避免传递 undefined
    const idCardPath = loginUserInfo.value?.idCardPath || '';
    const idCardBackPath = loginUserInfo.value?.idCardBackPath || '';
    const fullName = loginUserInfo.value?.fullName || '';
    const idCardNumber = loginUserInfo.value?.idCardNumber || '';

    uni.navigateTo({
      url: `/pages/auth/id-card?type=${userType.value}&docType=${encodeURIComponent(docType)}&isOptional=${isOptional}&imgUrl=${idCardPath}&imgUrl2=${idCardBackPath}&fullName=${encodeURIComponent(fullName)}&idCardNumber=${encodeURIComponent(idCardNumber)}`
    });
  }
  // 如果是驾驶证，跳转到专门的驾驶证上传页面
  else if (docType === '驾驶证') {
    // 安全获取参数，避免传递 undefined
    const driverLicensePath = loginUserInfo.value?.driverLicensePath || '';
    const driverLicenseBackPath = loginUserInfo.value?.driverLicenseBackPath || '';
    const fullName = loginUserInfo.value?.fullName || '';

    uni.navigateTo({
      url: `/pages/auth/drive-card?type=${userType.value}&isOptional=${isOptional}&imgUrl=${driverLicensePath}&imgUrl2=${driverLicenseBackPath}&fullName=${encodeURIComponent(fullName)}`
    });
  }
  // 如果是行驶证，跳转到专门的行驶证上传页面
  else if (docType === '行驶证') {
    // 安全获取参数，避免传递 undefined
    const vehicleLicensePath = loginUserInfo.value?.vehicleLicensePath || '';
    const vehicleLicenseBackPath = loginUserInfo.value?.vehicleLicenseBackPath || '';
    const vehicleOwner = loginUserInfo.value?.vehicleOwner || '';
    const licensePlateNumber = loginUserInfo.value?.licensePlateNumber || '';
    const vehicleRegistrationDate = loginUserInfo.value?.vehicleRegistrationDate || '';

    uni.navigateTo({
      url: `/pages/auth/driving-card?type=${userType.value}&isOptional=${isOptional}&imgUrl=${vehicleLicensePath}&imgUrl2=${vehicleLicenseBackPath}&fullName=${encodeURIComponent(vehicleOwner)}&licensePlateNumber=${encodeURIComponent(licensePlateNumber)}&vehicleRegistrationDate=${encodeURIComponent(vehicleRegistrationDate)}`
    });
  }
  // 如果是环保信息随车清单，跳转到专门的清单上传页面
  else if (docType === '环保信息随车清单') {
    // 安全获取参数，避免传递 undefined
    const environmentalListPath = loginUserInfo.value?.environmentalListPath || '';

    uni.navigateTo({
      url: `/pages/auth/detailed-list?type=${userType.value}&isOptional=${isOptional}&imgUrl=${environmentalListPath}`
    });
  }
  // 如果是道路运输证，跳转到专门的道路运输证上传页面
  else if (docType === '道路运输证') {
    // 安全获取参数，避免传递 undefined
    const transportLicensePath = loginUserInfo.value?.transportLicensePath || '';

    uni.navigateTo({
      url: `/pages/auth/transport-card?type=${userType.value}&isOptional=${isOptional}&imgUrl=${transportLicensePath}`
    });
  }
  // 如果是授权书，跳转到专门的授权书上传页面
  else if (docType === '授权书') {
    // 安全获取参数，避免传递 undefined
    const authorizationPath = loginUserInfo.value?.authorizationPath || '';

    uni.navigateTo({
      url: `/pages/auth/proxy-card?type=${userType.value}&isOptional=${isOptional}&imgUrl=${authorizationPath}`
    });
  }
  // 如果是营业执照，跳转到专门的营业执照上传页面
  else if (docType === '营业执照') {
    // 安全获取参数，避免传递 undefined
    const businessLicensePath = loginUserInfo.value?.businessLicensePath || '';
    const companyName = loginUserInfo.value?.companyName || '';
    const legalPerson = loginUserInfo.value?.legalPerson || '';
    const businessLicenseCode = loginUserInfo.value?.businessLicenseCode || '';
    const taxNumber = loginUserInfo.value?.taxNumber || '';

    uni.navigateTo({
      url: `/pages/auth/business-card?type=${userType.value}&isOptional=${isOptional}&imgUrl=${businessLicensePath}&companyName=${encodeURIComponent(companyName)}&legalPerson=${encodeURIComponent(legalPerson)}&businessLicenseCode=${encodeURIComponent(businessLicenseCode)}&taxNumber=${encodeURIComponent(taxNumber)}`
    });
  }
};

// 完成认证，跳转到首页
const goToIndex = () => {
  loginUserInfo.value.userType = userType.value == 'shipper_personal' ? 1 : userType.value == 'driver' ? 2 : userType.value == 'station' ? 3 : userType.value == 'shipper_company' ? 4 : 0;
  qualificationsAudit(loginUserInfo.value).then((response: any) => {
    uni.hideLoading();
    // 保存用户类型
    setStoragetItem('userType', userType.value);
    if (response.code == 700) {
      uni.showToast({
        title: '认证成功',
        icon: 'success'
      });

      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);
    } else if (response.code == 705) {
      uni.showToast({
        title: response.msg,
        icon: 'none'
      });
      return
    } else {
      uni.reLaunch({
        url: '/pages/auth/in-review'
      });
    }
  })
};

// 返回选择页面
const handleBack = () => {
  uni.navigateBack();
};

const loginUserInfo: any = ref({})
const getLoginUserInfo = () => {
  getLoginUser().then((response: any) => {
    uni.hideLoading();
    if (response.code == 700) {
      loginUserInfo.value = response.result
    }
  })
}

// 页面加载时获取用户类型参数
onLoad((options: any) => {
  const type = options?.userType;
  if (type) {
    userType.value = type;
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

onShow(() => {
  getLoginUserInfo()
})
</script>

<style lang="scss" scoped>
.auth-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-x: hidden; // 防止水平溢出
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;

  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      width: 40rpx;
      height: 40rpx;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 3rpx;
        background-color: #333;
        left: 4rpx;
        border-radius: 2rpx;
      }

      &::before {
        top: 12rpx;
        transform: rotate(-45deg);
      }

      &::after {
        bottom: 12rpx;
        transform: rotate(45deg);
      }
    }

    &:active {
      opacity: 0.7;
    }
  }

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }

  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.auth-result-tip {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  background-color: #E6A23C;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(230, 162, 60, 0.2);
  z-index: 98;

  .tip-icon {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    flex-shrink: 0;

    .tip-icon-inner {
      color: #fff;
      font-size: 24rpx;
      font-weight: bold;
    }
  }

  .tip-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .tip-title {
      font-size: 28rpx;
      color: #fff;
      font-weight: 600;
      margin-bottom: 6rpx;
      line-height: 1.2;
    }

    .tip-desc {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.3;
    }
  }
}

.auth-list {
  flex: 1;
  box-sizing: border-box;
  width: 100%;
}

.content-area {
  padding: 0 30rpx;
}

.auth-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .item-info {
    flex: 1;
    margin-right: 20rpx;

    .item-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 10rpx;
      display: block;
    }

    .item-desc {
      font-size: 24rpx;
      color: #999;
      line-height: 1.4;
      display: block;
    }
  }

  .upload-btn {
    width: 140rpx;
    height: 60rpx;
    background: linear-gradient(90deg, #ff6b88, #ff9cac);
    border-radius: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;

    text {
      color: #fff;
      font-size: 26rpx;
      font-weight: 500;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.divider {
  display: flex;
  align-items: center;
  margin: 30rpx 0;

  text {
    color: #999;
    font-size: 26rpx;
    padding: 0 20rpx;
  }

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #eee;
  }
}

.no-auth-required {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .tip-icon {
    width: 48rpx;
    height: 48rpx;
    background-color: #67C23A;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;

    .tip-icon-inner {
      color: #fff;
      font-size: 32rpx;
    }
  }

  .tip-text {
    font-size: 32rpx;
    color: #67C23A;
    font-weight: 500;
  }
}

.submit-btn {
  margin: 40rpx 0;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);

  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }

  &:active {
    transform: scale(0.98);
  }
}

.bottom-space {
  height: 30rpx;
}

/* #ifdef APP-PLUS */
// 在较小屏幕上的额外优化
@media screen and (max-height: 700px) {
  .auth-result-tip {
    padding: 16rpx 24rpx;
    left: 20rpx;
    right: 20rpx;

    .tip-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;

      .tip-icon-inner {
        font-size: 22rpx;
      }
    }

    .tip-content {
      .tip-title {
        font-size: 26rpx;
        margin-bottom: 4rpx;
      }

      .tip-desc {
        font-size: 22rpx;
      }
    }
  }

  .auth-item {
    padding: 20rpx;
    margin-bottom: 16rpx;

    .item-info {
      .item-title {
        font-size: 28rpx;
      }

      .item-desc {
        font-size: 22rpx;
      }
    }

    .upload-btn {
      width: 120rpx;
      height: 56rpx;

      text {
        font-size: 24rpx;
      }
    }
  }
}

/* #endif */
</style>
