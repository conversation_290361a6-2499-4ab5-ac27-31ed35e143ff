<template>
  <view class="review-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>

    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <text class="title">{{ userType }}资质认证</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 主体内容区域 -->
    <view class="content-area" :style="{ marginTop: headerTotalHeight }">
      <!-- 文档图标 -->
      <view class="document-icon">
        <view class="doc-icon-inner">
          <view class="doc-corner"></view>
          <view class="doc-lines">
            <view class="doc-line"></view>
            <view class="doc-line"></view>
            <view class="doc-line"></view>
          </view>
        </view>
      </view>

      <!-- 提交成功标题 -->
      <view class="success-title">
        <text>提交成功</text>
      </view>

      <!-- 说明文字 -->
      <view class="description">
        <text>我们将尽快完成审核，请耐心等待</text>
      </view>

      <!-- 待审核状态 -->
      <view class="review-status">
        <text>待审核</text>
      </view>
    </view>

    <!-- 底部安全区域占位 -->
    <view class="bottom-space" :style="{ height: safeAreaBottom }"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { getStoragetItem } from '@/utils/storage';

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif

  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif

  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '124rpx';

  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif

  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 获取用户类型
const userType = computed(() => {
  const typeMap: { [key: string]: string } = {
    'shipper_personal': '个人货主',
    'driver': '司机',
    'station': '配货站',
    'shipper_company': '企业货主'
  };
  return typeMap[getStoragetItem('userType')];
});
</script>

<style lang="scss" scoped>
.review-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-x: hidden;
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }

  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  box-sizing: border-box;
}

.document-icon {
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;

  .doc-icon-inner {
    width: 120rpx;
    height: 150rpx;
    background-color: #ff6b88;
    border-radius: 8rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 107, 136, 0.3);

    .doc-corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      border-left: 24rpx solid #ff9cac;
      border-bottom: 24rpx solid #ff9cac;
      border-top: 24rpx solid transparent;
      border-right: 24rpx solid transparent;
    }

    .doc-lines {
      display: flex;
      flex-direction: column;
      gap: 12rpx;
      margin-top: 20rpx;

      .doc-line {
        width: 60rpx;
        height: 4rpx;
        background-color: #fff;
        border-radius: 2rpx;

        &:last-child {
          width: 40rpx;
        }
      }
    }
  }
}

.success-title {
  margin-bottom: 30rpx;

  text {
    font-size: 48rpx;
    color: #ff6b88;
    font-weight: 600;
    letter-spacing: 2rpx;
  }
}

.description {
  margin-bottom: 80rpx;

  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    text-align: center;
  }
}

.review-status {
  width: 300rpx;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);
  opacity: 0.8;

  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }
}

.bottom-space {
  flex-shrink: 0;
}

/* #ifdef APP-PLUS */
// 在较小屏幕上的额外优化
@media screen and (max-height: 700px) {
  .content-area {
    padding: 40rpx 30rpx;
  }

  .document-icon {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 40rpx;

    .doc-icon-inner {
      width: 100rpx;
      height: 120rpx;
    }
  }

  .success-title {
    margin-bottom: 20rpx;

    text {
      font-size: 40rpx;
    }
  }

  .description {
    margin-bottom: 60rpx;

    text {
      font-size: 26rpx;
    }
  }
}

/* #endif */
</style>