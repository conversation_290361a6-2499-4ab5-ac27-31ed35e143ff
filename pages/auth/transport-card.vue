<template>
  <view class="transport-card-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>
    
    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <view class="back-btn" @click="handleBack">
        <view class="back-icon"></view>
      </view>
      <text class="title">道路运输证</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingBottom: safeAreaBottom, marginTop: headerTotalHeight }">
      <!-- 上传必要说明 -->
      <view class="upload-requirement">
        <!-- <text class="required-symbol">*</text> -->
        <text class="requirement-text">上传道路运输证</text>
      </view>
      
      <!-- 图片上传区域 -->
      <view class="upload-section">
        <view class="upload-item">
          <view class="upload-preview-container">
            <cu-upload
              v-model="transportCardImage"
              height="640rpx"
              placeholder="上传道路运输证"
              @success="handleUploadSuccess"
              @fail="handleUploadFail"
            >
              <template #placeholder>
                <view class="upload-placeholder">
                  <view class="upload-icon"></view>
                  <text class="upload-text">上传道路运输证</text>
                </view>
              </template>
            </cu-upload>
          </view>
        </view>
        
        <!-- 小提示文本 -->
        <view class="tip-text">
          <text class="iconfont icon-owner-tishi"></text>
          <text>请上传清晰完整的道路运输证照片</text>
        </view>
      </view>
      
      <!-- 协议同意区域 -->
      <view class="agreement-section">
        <view class="checkbox" @click="isAgreed = !isAgreed">
          <view class="checkbox-inner" :class="{ 'checked': isAgreed }"></view>
        </view>
        <text class="agreement-text">您同意我们收集道路运输证用于承运资质审核</text>
      </view>

      <!-- 提交按钮 -->
      <view 
        class="submit-btn" 
        :class="{ 'disabled': !isFormValid }"
        @click="handleSubmit"
      >
        <text>提交申核</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { saveLoginUser } from "../../api/transport";

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif
  
  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif
  
  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '104rpx';
  
  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif
  
  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 页面参数
const userType = ref('');
const isOptional = ref(false);

// 道路运输证图片
const transportCardImage = ref('');

// 是否同意协议
const isAgreed = ref(false);

// 表单是否有效
const isFormValid = computed(() => {
  // 图片已上传且同意协议
  return transportCardImage.value && isAgreed.value;
});

// 处理返回按钮
const handleBack = () => {
  uni.navigateBack();
};

// 处理上传成功
const handleUploadSuccess = (res: any) => {
  console.log('道路运输证上传成功', res);
  
  // 存储图片路径
  transportCardImage.value = res.url;
  
  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理上传失败
const handleUploadFail = (err: any) => {
  console.error('上传失败', err);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
    duration: 2000
  });
};

// 提交
const handleSubmit = () => {
  // 表单验证
  if (!isFormValid.value) {
    let errorMsg = '';
    
    if (!transportCardImage.value) {
      errorMsg = '请上传道路运输证';
    } else if (!isAgreed.value) {
      errorMsg = '请同意用户协议';
    }
    
    if (errorMsg) {
      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
      return;
    }
  }
  
  // 显示加载中
  uni.showLoading({
    title: '提交中...',
    mask: true
  });
  
  saveLoginUser({
  	transportLicensePath: transportCardImage.value
  }).then((response: any) => {
  	uni.hideLoading()
  	if (response.code == 700) {
  		uni.hideLoading();
  		uni.showToast({
  		  title: '提交成功',
  		  icon: 'success',
  		  duration: 1500
  		});
  		
  		// 提交成功后返回上一页
  		setTimeout(() => {
  		  uni.navigateBack();
  		}, 1500);
  	}
  })
};

// 参数有效性检查辅助函数
const isValidParam = (param: any): boolean => {
  return param && param !== 'undefined' && param !== 'null' && param.trim() !== '';
};

// 页面加载时获取用户类型参数
onLoad((options: any) => {
  if (options?.type) {
    userType.value = options.type;
  }

  if (options?.isOptional) {
    isOptional.value = options.isOptional === 'true';
  }

  // 返显道路运输证图片
  if (isValidParam(options?.imgUrl)) {
    transportCardImage.value = options.imgUrl;
  }

  // 如果有道路运输证图片，说明用户之前已经同意过协议，自动勾选协议同意状态
  if (transportCardImage.value) {
    isAgreed.value = true;
  }

  console.log('道路运输证页面参数接收完成：', {
    userType: userType.value,
    isOptional: isOptional.value,
    transportCardImage: transportCardImage.value,
    isAgreed: isAgreed.value,
    originalParams: {
      imgUrl: options?.imgUrl
    }
  });
});
</script>

<style scoped lang="scss">
.transport-card-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  box-sizing: border-box;
  position: relative;
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;
  
  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      width: 40rpx;
      height: 40rpx;
      position: relative;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 3rpx;
        background-color: #333;
        left: 4rpx;
        border-radius: 2rpx;
      }
      
      &::before {
        top: 12rpx;
        transform: rotate(-45deg);
      }
      
      &::after {
        bottom: 12rpx;
        transform: rotate(45deg);
      }
    }
    
    &:active {
      opacity: 0.7;
    }
  }
  
  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }
  
  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.content-area {
  padding: 20rpx 30rpx 40rpx;
}

// 上传必要说明
.upload-requirement {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  .required-symbol {
    color: #ff6b88;
    font-size: 32rpx;
    margin-right: 6rpx;
  }
  
  .requirement-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    flex: 1;
  }
}

// 上传区域
.upload-section {
  margin-bottom: 40rpx;
  
  .upload-item {
    width: 100%;
    margin-bottom: 20rpx;
    
    .upload-preview-container {
      width: 100%;
      height: 640rpx;
      border-radius: 16rpx;
      overflow: hidden;
    }
  }
  
  .tip-text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      margin-right: 8rpx;
      color: #ff9cac;
      font-size: 28rpx;
    }
  }
}

// 自定义上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .upload-icon {
    width: 80rpx;
    height: 80rpx;
    border: 4rpx dashed #ddd;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    position: relative;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      background-color: #ddd;
    }
    
    &::before {
      width: 4rpx;
      height: 40rpx;
      top: 20rpx;
      left: 38rpx;
    }
    
    &::after {
      width: 40rpx;
      height: 4rpx;
      top: 38rpx;
      left: 20rpx;
    }
  }
  
  .upload-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 协议同意区域
.agreement-section {
  display: flex;
  align-items: center;
  margin: 40rpx 0 30rpx;
  padding: 10rpx 0;
  
  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;
    flex-shrink: 0;
    
    .checkbox-inner {
      width: 24rpx;
      height: 24rpx;
      border-radius: 50%;
      background-color: transparent;
      transition: all 0.2s;
      
      &.checked {
        background-color: #ff6b88;
      }
    }
  }
  
  .agreement-text {
    font-size: 28rpx;
    color: #666;
  }
}

// 提交按钮
.submit-btn {
  margin: 20rpx 0 40rpx;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);
  
  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &.disabled {
    opacity: 0.6;
  }
}
</style>
