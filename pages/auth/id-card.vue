<template>
  <view class="id-card-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>

    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <view class="back-btn" @click="handleBack">
        <view class="back-icon"></view>
      </view>
      <text class="title">{{ docType }}</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingBottom: safeAreaBottom, marginTop: headerTotalHeight }">
      <!-- 身份证信息区域 -->
      <view class="id-card-info-section">
        <view class="info-row">
          <text class="info-label">姓名</text>
          <text class="info-value">{{ idCardInfo.name || '未填写' }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">身份证号</text>
          <text class="info-value">{{ idCardInfo.idNumber || '未填写' }}</text>
        </view>
        <view class="modify-btn" @click="showModifyPopup = true">
          <text>修改身份证信息</text>
          <view class="arrow-icon"></view>
        </view>
      </view>

      <!-- 身份证上传区域 -->
      <view class="upload-section">
        <view class="upload-title">
          <text>请上传清晰的身份证照片</text>
        </view>
        <view class="upload-area">
          <view class="upload-item">
            <text class="upload-label">身份证正面人脸照片</text>
            <cu-upload v-model="frontImage" height="300rpx" placeholder="点击上传正面人脸照片" @success="handleFrontUploadSuccess" @fail="handleUploadFail"></cu-upload>
          </view>
          <view class="upload-item">
            <text class="upload-label">身份证反面</text>
            <cu-upload v-model="backImage" height="300rpx" placeholder="点击上传反面照片" @success="handleBackUploadSuccess" @fail="handleUploadFail"></cu-upload>
          </view>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="save-btn" @click="handleSave">
        <text>保存</text>
      </view>
    </view>

    <!-- 修改身份证信息弹窗 -->
    <uni-popup ref="popup" type="bottom" background-color="#fff" @change="handlePopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">修改身份证信息</text>
          <view class="close-btn" @click="showModifyPopup = false">×</view>
        </view>
        <view class="input-group">
          <text class="input-label">姓名</text>
          <input type="text" v-model="tempName" placeholder="请输入姓名" />
        </view>
        <view class="input-group">
          <text class="input-label">身份证号</text>
          <input type="idcard" v-model="tempIdNumber" placeholder="请输入身份证号" maxlength="18" />
        </view>
        <view class="confirm-btn" @click="updateIdCardInfo">
          <text>确认</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { onLoad, onReady } from '@dcloudio/uni-app';
import { saveLoginUser } from "../../api/transport";

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif

  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif

  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '104rpx';

  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif

  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 页面参数
const userType = ref('');
const docType = ref('');
const isOptional = ref(false);

// 身份证正反面图片
const frontImage = ref('');
const backImage = ref('');

// 身份证信息
const idCardInfo = ref({
  name: '',
  idNumber: ''
});

// 临时编辑数据
const tempName = ref('');
const tempIdNumber = ref('');
const showModifyPopup = ref(false);

// 弹窗引用
const popup = ref(null);

// 监听弹窗显示状态
watch(showModifyPopup, (newVal) => {
  if (newVal) {
    // 弹窗打开时，初始化临时数据
    tempName.value = idCardInfo.value.name;
		if (idCardInfo.value.idNumber) {
			tempIdNumber.value = idCardInfo.value.idNumber; // 直接复制身份证号
		}

    // @ts-ignore
    popup.value.open();
  } else {
    // @ts-ignore
    popup.value?.close();
  }
});

// 处理弹窗状态变化
const handlePopupChange = (e: any) => {
  if (!e.show) {
    showModifyPopup.value = false;
  }
};

// 更新身份证信息
const updateIdCardInfo = () => {
  // 验证身份证号
  if (tempIdNumber.value && !/^\d{17}(\d|X)$/i.test(tempIdNumber.value)) {
    uni.showToast({
      title: '请输入正确的身份证号码',
      icon: 'none'
    });
    return;
  }

  idCardInfo.value.name = tempName.value;

  // 直接保存完整的身份证号，不做遮蔽处理
  if (tempIdNumber.value) {
    idCardInfo.value.idNumber = tempIdNumber.value;
  }

  showModifyPopup.value = false;

  uni.showToast({
    title: '信息已更新',
    icon: 'success'
  });
};



// 处理正面照片上传成功
const handleFrontUploadSuccess = (res: any) => {
  console.log('正面照片上传成功', res);
	frontImage.value = res.url;
  // 这里处理返回值逻辑
  uni.showToast({
    title: '上传成功',
    icon: 'success'
  });
};

// 处理反面照片上传成功
const handleBackUploadSuccess = (res: any) => {
  console.log('反面照片上传成功', res);
	backImage.value = res.url;
  // 这里处理返回值逻辑
  uni.showToast({
    title: '上传成功',
    icon: 'success'
  });
};

// 处理上传失败
const handleUploadFail = (err: any) => {
  console.error('上传失败', err);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none'
  });
};

// 保存按钮处理
const handleSave = () => {
  // 检查是否上传了照片
  if (!frontImage.value || !backImage.value) {
    let errorMsg = '';
    if (!frontImage.value && !backImage.value) {
      errorMsg = '请上传身份证正反面照片';
    } else if (!frontImage.value) {
      errorMsg = '请上传身份证正面照片';
    } else {
      errorMsg = '请上传身份证反面照片';
    }

    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return;
  }

  // 检查是否填写了身份证信息（使用trim去除空格）
  const name = idCardInfo.value.name?.trim() || '';
  const idNumber = idCardInfo.value.idNumber?.trim() || '';

  if (!name || !idNumber) {
    let errorMsg = '';
    if (!name && !idNumber) {
      errorMsg = '请填写姓名和身份证号';
    } else if (!name) {
      errorMsg = '请填写姓名';
    } else {
      errorMsg = '请填写身份证号';
    }

    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return;
  }

  // 基本的身份证号格式校验
  if (!/^\d{17}(\d|X)$/i.test(idNumber)) {
    uni.showToast({
      title: '请输入正确的身份证号码格式',
      icon: 'none'
    });
    return;
  }
	
	// 使用trim后的值提交
	saveLoginUser({
		idCardPath: frontImage.value,
		idCardBackPath: backImage.value,
		fullName: name,
		idCardNumber: idNumber
	}).then((response: any) => {
		if (response.code == 700) {
			uni.showToast({
			  title: '保存成功',
			  icon: 'success'
			});
			
			// 延迟返回上一页
			setTimeout(() => {
			  uni.navigateBack();
			}, 1500);
		}
	})

  // 这里可以调用接口上传所有信息
  // uploadAllInfo({
  //   frontImage: frontImage.value,
  //   backImage: backImage.value,
  //   name: idCardInfo.value.name,
  //   idNumber: idCardInfo.value.idNumber
  // });
};

// 返回按钮处理
const handleBack = () => {
  uni.navigateBack();
};

// 参数有效性检查辅助函数
const isValidParam = (param: any): boolean => {
  return param && param !== 'undefined' && param !== 'null' && param.trim() !== '';
};

// 安全解码URL参数
const safeDecodeURIComponent = (param: any): string => {
  if (!isValidParam(param)) {
    return '';
  }

  try {
    return decodeURIComponent(param);
  } catch (error) {
    console.warn('URL参数解码失败：', param, error);
    return '';
  }
};

// 页面加载
onLoad((options: any) => {
  // 获取参数
  userType.value = options?.type || '';
  docType.value = safeDecodeURIComponent(options?.docType) || '身份证';
  isOptional.value = options?.isOptional === 'true';

  // 返显身份证图片
  if (isValidParam(options?.imgUrl)) {
    frontImage.value = options.imgUrl;
  }

  if (isValidParam(options?.imgUrl2)) {
    backImage.value = options.imgUrl2;
  }

  // 返显身份证信息
  const decodedFullName = safeDecodeURIComponent(options?.fullName);
  if (decodedFullName) {
    idCardInfo.value.name = decodedFullName;
  }

  const decodedIdCardNumber = safeDecodeURIComponent(options?.idCardNumber);
  if (decodedIdCardNumber) {
    idCardInfo.value.idNumber = decodedIdCardNumber;
  }

  console.log('身份证页面参数接收完成：', {
    userType: userType.value,
    docType: docType.value,
    isOptional: isOptional.value,
    frontImage: frontImage.value,
    backImage: backImage.value,
    idCardInfo: JSON.stringify(idCardInfo.value),
    originalParams: {
      imgUrl: options?.imgUrl,
      imgUrl2: options?.imgUrl2,
      fullName: options?.fullName,
      idCardNumber: options?.idCardNumber
    }
  });

  // 如果没有参数则返回
  if (!userType.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 页面准备就绪
onReady(() => {
  // 初始化弹窗
});
</script>

<style lang="scss" scoped>
.id-card-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-x: hidden; // 防止水平溢出
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;

  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      width: 40rpx;
      height: 40rpx;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 3rpx;
        background-color: #333;
        left: 4rpx;
        border-radius: 2rpx;
      }

      &::before {
        top: 12rpx;
        transform: rotate(-45deg);
      }

      &::after {
        bottom: 12rpx;
        transform: rotate(45deg);
      }
    }

    &:active {
      opacity: 0.7;
    }
  }

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }

  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.content-area {
  flex: 1;
  padding: 0 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.id-card-info-section {
  background-color: #fff;
  margin: 20rpx 0;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .info-row {
    display: flex;
    padding: 15rpx 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      width: 160rpx;
      font-size: 28rpx;
      color: #666;
    }

    .info-value {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .modify-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 20rpx;

    text {
      font-size: 24rpx;
      color: #ff6b88;
    }

    .arrow-icon {
      width: 24rpx;
      height: 24rpx;
      position: relative;
      margin-left: 8rpx;

      &::before {
        content: '';
        position: absolute;
        width: 8rpx;
        height: 8rpx;
        border-top: 2rpx solid #ff6b88;
        border-right: 2rpx solid #ff6b88;
        transform: rotate(45deg);
        top: 8rpx;
        left: 4rpx;
      }
    }

    &:active {
      opacity: 0.7;
    }
  }
}

.upload-section {
  background-color: #fff;
  margin: 20rpx 0;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .upload-title {
    margin-bottom: 20rpx;

    text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .upload-area {
    display: flex;
    flex-direction: column;
    gap: 30rpx;

    .upload-item {
      display: flex;
      flex-direction: column;

      .upload-label {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 10rpx;
      }
    }
  }
}

.save-btn {
  margin: 40rpx 0;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);

  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }

  &:active {
    transform: scale(0.98);
  }
}

.popup-content {
  padding: 30rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .close-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 40rpx;
      color: #999;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .input-group {
    margin-bottom: 20rpx;

    .input-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
      display: block;
    }

    input {
      width: 100%;
      height: 80rpx;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #333;
      box-sizing: border-box;
    }
  }

  .confirm-btn {
    margin-top: 40rpx;
    height: 88rpx;
    background: linear-gradient(90deg, #ff6b88, #ff9cac);
    border-radius: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    text {
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      letter-spacing: 2rpx;
    }

    &:active {
      transform: scale(0.98);
    }
  }
}

/* #ifdef APP-PLUS */
// 在较小屏幕上的额外优化
@media screen and (max-height: 700px) {
  .upload-section {
    padding: 20rpx;

    .upload-area {
      gap: 20rpx;
    }
  }

  .id-card-info-section {
    padding: 20rpx;

    .info-row {
      padding: 10rpx 0;
    }
  }

  .save-btn {
    margin: 30rpx 0;
    height: 80rpx;

    text {
      font-size: 30rpx;
    }
  }
}

/* #endif */
</style>
