<template>
  <view class="drive-card-container">
    <!-- 顶部安全区域 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop }"></view>

    <!-- 标题栏 -->
    <view class="header" :style="{ top: safeAreaTop }">
      <view class="back-btn" @click="handleBack">
        <view class="back-icon"></view>
      </view>
      <text class="title">驾驶证认证</text>
      <view class="title-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingBottom: safeAreaBottom, marginTop: headerTotalHeight }">
      <!-- 证件类型选择 -->
      <!-- <view class="card-type-selector">
        <text class="selector-title">选择上传证件类型</text>
        <view class="type-options">
          <view class="type-option" :class="{ 'active': cardType === 'paper' }" @click="handleCardTypeChange('paper')">
            <text>纸质版</text>
          </view>
          <view class="type-option" :class="{ 'active': cardType === 'digital' }" @click="handleCardTypeChange('digital')">
            <text>电子版</text>
          </view>
        </view>
      </view> -->

      <!-- 驾驶证上传区域 -->
      <view class="upload-section">
        <view class="upload-area" :class="{ 'upload-area-digital': cardType === 'digital' }">
          <!-- 正面上传 -->
          <view class="upload-item" :class="{ 'upload-item-digital': cardType === 'digital' }">
            <view class="upload-preview-container" :class="{ 'upload-preview-digital': cardType === 'digital' }">
              <cu-upload v-model="frontImage" :height="cardType === 'paper' ? '360rpx' : '450rpx'" :placeholder="cardType === 'paper' ? '上传驾驶证主页' : '上传驾驶证主页'" @success="handleFrontUploadSuccess" @fail="handleUploadFail">
                <template #placeholder>
                  <view class="upload-placeholder">
                    <view class="upload-icon"></view>
                    <text class="upload-text">上传驾驶证主页</text>
                  </view>
                </template>
              </cu-upload>
            </view>
          </view>

          <!-- 副页上传 -->
          <view class="upload-item" :class="{ 'upload-item-digital': cardType === 'digital' }">
            <view class="upload-preview-container" :class="{ 'upload-preview-digital': cardType === 'digital' }">
              <cu-upload v-model="backImage" :height="cardType === 'paper' ? '360rpx' : '450rpx'" :placeholder="cardType === 'paper' ? '上传驾驶证副页' : '上传驾驶证副页'" @success="handleBackUploadSuccess" @fail="handleUploadFail">
                <template #placeholder>
                  <view class="upload-placeholder">
                    <view class="upload-icon"></view>
                    <text class="upload-text">上传驾驶证副页</text>
                  </view>
                </template>
              </cu-upload>
            </view>
          </view>
        </view>

        <!-- 小提示文本 -->
        <view v-if="cardType === 'digital'" class="tip-text">
          <text class="iconfont icon-owner-tishi"></text>
          <text>您可以在交警12123APP上查询驾驶证电子版正副页</text>
        </view>
      </view>

      <!-- 驾驶证信息区域 -->
      <view class="license-info-section">
        <view class="section-header">
          <text class="section-title">驾驶证信息</text>
          <view class="edit-btn" @click="openEditDialog">
            <text class="edit-text">编辑</text>
            <text class="iconfont icon-owner-bianji"></text>
          </view>
        </view>

        <view class="info-container">
          <view class="info-row">
            <view class="info-label">姓名</view>
            <view class="info-value">{{ driverInfo.name || '未填写' }}</view>
          </view>
          <!-- <view class="info-row">
            <view class="info-label">驾驶证号</view>
            <view class="info-value">{{ driverInfo.licenseNumber || '未填写' }}</view>
          </view> -->
          <!-- <view class="info-row">
            <view class="info-label">准驾车型</view>
            <view class="info-value">{{ driverInfo.vehicleType || '未填写' }}</view>
          </view> -->
          <!-- <view class="info-row">
            <view class="info-label">初次领证日期</view>
            <view class="info-value">{{ driverInfo.initialDate || '未填写' }}</view>
          </view> -->
          <!-- <view class="info-row">
            <view class="info-label">有效起始日期</view>
            <view class="info-value">{{ driverInfo.validFromDate || '未填写' }}</view>
          </view> -->
          <!-- <view class="info-row">
            <view class="info-label">有效结束日期</view>
            <view class="info-value">{{ driverInfo.validUntilDate || '未填写' }}</view>
          </view> -->
        </view>
      </view>

      <!-- 用户协议 -->
      <view class="agreement-section">
        <view class="checkbox" @click="isAgreed = !isAgreed">
          <view class="checkbox-inner" :class="{ 'checked': isAgreed }"></view>
        </view>
        <text class="agreement-text">您同意我们收集驾驶证用于承运资质审核</text>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-btn" :class="{ 'disabled': !isFormValid }" @click="handleSubmit">
        <text>提交申核</text>
      </view>
    </view>

    <!-- 编辑弹窗 -->
    <view class="edit-dialog" v-if="showEditDialog">
      <view class="dialog-mask" @click="closeEditDialog"></view>
      <view class="dialog-container" :style="{ paddingBottom: safeAreaBottom }">
        <view class="dialog-header">
          <text class="dialog-title">编辑驾驶证信息</text>
          <view class="close-btn" @click="closeEditDialog">×</view>
        </view>

        <view class="dialog-content">
          <view class="form-item">
            <view class="form-label">姓名</view>
            <input class="form-input" type="text" v-model="tempDriverInfo.name" placeholder="请输入姓名" />
          </view>

          <!-- <view class="form-item">
            <view class="form-label">驾驶证号</view>
            <input class="form-input" type="text" v-model="tempDriverInfo.licenseNumber" placeholder="请输入驾驶证号" />
          </view>

          <view class="form-item">
            <view class="form-label">准驾车型</view>
            <input class="form-input" type="text" v-model="tempDriverInfo.vehicleType" placeholder="请输入准驾车型，如：A2" />
          </view>

          <view class="form-item">
            <view class="form-label">初次领证日期</view>
            <picker class="form-picker" mode="date" :value="tempDriverInfo.initialDate" @change="onInitialDateChange" fields="day" style="position: relative; z-index: 10000;">
              <view class="picker-value">
                <text>{{ tempDriverInfo.initialDate || '请选择日期' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <view class="form-label">有效期起始日期</view>
            <picker class="form-picker" mode="date" :value="tempDriverInfo.validFromDate" @change="onValidFromDateChange" fields="day" style="position: relative; z-index: 10000;">
              <view class="picker-value">
                <text>{{ tempDriverInfo.validFromDate || '请选择日期' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <view class="form-label">有效期截止日期</view>
            <picker class="form-picker" mode="date" :value="tempDriverInfo.validUntilDate" @change="onValidUntilDateChange" fields="day" style="position: relative; z-index: 10000;">
              <view class="picker-value">
                <text>{{ tempDriverInfo.validUntilDate || '请选择日期' }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view> -->
        </view>

        <view class="dialog-footer">
          <view class="cancel-btn" @click="closeEditDialog">取消</view>
          <view class="confirm-btn" @click="saveDriverInfo">确认</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { saveLoginUser } from "../../api/transport";

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = systemInfo.statusBarHeight * 2;

// 计算安全区域
const safeAreaTop = computed(() => {
  // #ifdef APP-PLUS
  return statusBarHeight + 'rpx';
  // #endif

  // #ifndef APP-PLUS
  return '0rpx';
  // #endif
});

const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom > 0) {
    return (systemInfo.safeAreaInsets.bottom * 2) + 'rpx';
  }
  // #endif

  return '0rpx';
});

// 计算标题栏总高度（安全区域 + 标题栏高度）
const headerTotalHeight = computed(() => {
  // 标题栏高度默认为94rpx (30rpx padding-top + 30rpx padding-bottom + 约34rpx内容高度)
  // 添加额外的安全边距10rpx，确保不会被标题栏覆盖
  const titleBarHeight = '104rpx';

  // #ifdef APP-PLUS
  return `calc(${safeAreaTop.value} + ${titleBarHeight})`;
  // #endif

  // #ifndef APP-PLUS
  return titleBarHeight;
  // #endif
});

// 页面参数
const userType = ref('');
const isOptional = ref(false);

// 证件类型（纸质版/电子版）
const cardType = ref('paper'); // 默认为纸质版

// 驾驶证正反面图片 - 分别存储纸质版和电子版
const paperFrontImage = ref('');
const paperBackImage = ref('');
const digitalFrontImage = ref('');
const digitalBackImage = ref('');

// 当前显示的图片，根据cardType动态选择
const frontImage = computed(() => {
  return cardType.value === 'paper' ? paperFrontImage.value : digitalFrontImage.value;
});

const backImage = computed(() => {
  return cardType.value === 'paper' ? paperBackImage.value : digitalBackImage.value;
});

// 协议同意状态
const isAgreed = ref(false);

// 驾驶证信息
const driverInfo = reactive({
  name: '',                // 姓名
  licenseNumber: '',       // 驾驶证号
  vehicleType: '',         // 准驾车型
  initialDate: '',         // 初次领证日期
  validFromDate: '',       // 有效起始日期
  validUntilDate: ''       // 有效结束日期
});

// 临时存储编辑中的驾驶证信息
const tempDriverInfo = reactive({
  name: '',
  licenseNumber: '',
  vehicleType: '',
  initialDate: '',
  validFromDate: '',
  validUntilDate: ''
});

// 编辑弹窗显示控制
const showEditDialog = ref(false);

// 表单是否有效的计算属性
const isFormValid = computed(() => {
  // 验证图片上传
  // const imagesValid = cardType.value === 'paper'
  //   ? (paperFrontImage.value && paperBackImage.value)
  //   : (digitalFrontImage.value && digitalBackImage.value);
		
	const imagesValid = paperFrontImage.value && paperBackImage.value;

  // 验证驾驶证信息
  const driverInfoValid = driverInfo.name 
		// && driverInfo.licenseNumber &&
  //   driverInfo.vehicleType &&
  //   driverInfo.initialDate &&
  //   driverInfo.validFromDate &&
  //   driverInfo.validUntilDate
		;

  // 验证协议是否同意
  return imagesValid && driverInfoValid && isAgreed.value;
});

// 处理返回按钮
const handleBack = () => {
  uni.navigateBack();
};

// 处理正面照片上传成功
const handleFrontUploadSuccess = (res: any) => {
  console.log('驾驶证主页上传成功', res);

  // 根据当前类型存储图片路径
  // if (cardType.value === 'paper') {
    paperFrontImage.value = res.url;
  // } else {
  //   digitalFrontImage.value = res.tempFilePaths[0];
  // }

  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理反面照片上传成功
const handleBackUploadSuccess = (res: any) => {
  console.log('驾驶证副页上传成功', res);

  // 根据当前类型存储图片路径
  // if (cardType.value === 'paper') {
    paperBackImage.value = res.url;
  // } else {
  //   digitalBackImage.value = res.tempFilePaths[0];
  // }

  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });
};

// 处理上传失败
const handleUploadFail = (err: any) => {
  console.error('上传失败', err);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
    duration: 2000
  });
};

// 打开编辑弹窗
const openEditDialog = () => {
  // 复制当前信息到临时变量
  tempDriverInfo.name = driverInfo.name;
  // tempDriverInfo.licenseNumber = driverInfo.licenseNumber;
  // tempDriverInfo.vehicleType = driverInfo.vehicleType;
  // tempDriverInfo.initialDate = driverInfo.initialDate;
  // tempDriverInfo.validFromDate = driverInfo.validFromDate;
  // tempDriverInfo.validUntilDate = driverInfo.validUntilDate;

  console.log('打开编辑弹窗，临时数据：', JSON.stringify(tempDriverInfo));
  showEditDialog.value = true;
};

// 关闭编辑弹窗
const closeEditDialog = () => {
  showEditDialog.value = false;
};

// 保存驾驶证信息
const saveDriverInfo = () => {
  console.log('保存前临时数据：', JSON.stringify(tempDriverInfo));

  // 验证必填字段
  if (!tempDriverInfo.name 
		// ||
  //   !tempDriverInfo.licenseNumber ||
  //   !tempDriverInfo.vehicleType ||
  //   !tempDriverInfo.initialDate ||
  //   !tempDriverInfo.validFromDate ||
  //   !tempDriverInfo.validUntilDate
		) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 保存信息
  driverInfo.name = tempDriverInfo.name;
  // driverInfo.licenseNumber = tempDriverInfo.licenseNumber;
  // driverInfo.vehicleType = tempDriverInfo.vehicleType;
  // driverInfo.initialDate = tempDriverInfo.initialDate;
  // driverInfo.validFromDate = tempDriverInfo.validFromDate;
  // driverInfo.validUntilDate = tempDriverInfo.validUntilDate;

  console.log('保存后数据：', JSON.stringify(driverInfo));

  // 关闭弹窗
  closeEditDialog();

  // 提示成功
  uni.showToast({
    title: '信息已保存',
    icon: 'success',
    duration: 1500
  });
};

// 参数有效性检查辅助函数
const isValidParam = (param: any): boolean => {
  return param && param !== 'undefined' && param !== 'null' && param.trim() !== '';
};

// 安全解码URL参数
const safeDecodeURIComponent = (param: any): string => {
  if (!isValidParam(param)) {
    return '';
  }

  try {
    return decodeURIComponent(param);
  } catch (error) {
    console.warn('URL参数解码失败：', param, error);
    return '';
  }
};

// 页面加载时获取用户类型参数
onLoad((options: any) => {
  if (options?.type) {
    userType.value = options.type;
  }

  if (options?.isOptional) {
    isOptional.value = options.isOptional === 'true';
  }

  // 返显驾驶证图片
  if (isValidParam(options?.imgUrl)) {
    paperFrontImage.value = options.imgUrl;
  }

  if (isValidParam(options?.imgUrl2)) {
    paperBackImage.value = options.imgUrl2;
  }

  // 返显驾驶证信息
  const decodedFullName = safeDecodeURIComponent(options?.fullName);
  if (decodedFullName) {
    driverInfo.name = decodedFullName;
  }

  // 如果有驾驶证图片，说明用户之前已经同意过协议，自动勾选协议同意状态
  if (paperFrontImage.value || paperBackImage.value) {
    isAgreed.value = true;
  }

  console.log('驾驶证页面参数接收完成：', {
    userType: userType.value,
    isOptional: isOptional.value,
    paperFrontImage: paperFrontImage.value,
    paperBackImage: paperBackImage.value,
    driverInfo: JSON.stringify(driverInfo),
    isAgreed: isAgreed.value,
    originalParams: {
      imgUrl: options?.imgUrl,
      imgUrl2: options?.imgUrl2,
      fullName: options?.fullName
    }
  });
});

// 处理证件类型切换
const handleCardTypeChange = (type: string) => {
  // 切换类型，保持各自的数据状态
  cardType.value = type;
};

// 提交
const handleSubmit = () => {
  // 表单验证
  if (!isFormValid.value) {
    let errorMsg = '';

    const imagesValid = 
			// cardType.value === 'paper' ? 
			(paperFrontImage.value && paperBackImage.value)
      // : (digitalFrontImage.value && digitalBackImage.value)
			;

    if (!imagesValid) {
      errorMsg = '请上传驾驶证正反面照片';
    } else if (!driverInfo.name 
			// ||
   //    !driverInfo.licenseNumber ||
   //    !driverInfo.vehicleType ||
   //    !driverInfo.initialDate ||
   //    !driverInfo.validFromDate ||
   //    !driverInfo.validUntilDate
			) {
      errorMsg = '请填写完整的驾驶证信息';
    } else if (!isAgreed.value) {
      errorMsg = '请同意用户协议';
    }

    if (errorMsg) {
      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
      return;
    }
  }

  // 获取当前类型的图片
  const frontImg = cardType.value === 'paper' ? paperFrontImage.value : digitalFrontImage.value;
  const backImg = cardType.value === 'paper' ? paperBackImage.value : digitalBackImage.value;

  // 显示加载中
  uni.showLoading({
    title: '提交中...',
    mask: true
  });
	
	saveLoginUser({
		driverLicensePath: paperFrontImage.value,
		driverLicenseBackPath: paperBackImage.value
	}).then((response: any) => {
		uni.hideLoading()
		if (response.code == 700) {
			uni.hideLoading();
			uni.showToast({
			  title: '提交成功',
			  icon: 'success',
			  duration: 1500
			});
			
			// 提交成功后返回上一页
			setTimeout(() => {
			  uni.navigateBack();
			}, 1500);
		}
	})
};

// 日期选择器事件处理
const onInitialDateChange = (e: any) => {
  console.log('初次领证日期选择变化：', e.detail.value);
  tempDriverInfo.initialDate = e.detail.value;
};

const onValidFromDateChange = (e: any) => {
  console.log('有效起始日期选择变化：', e.detail.value);
  tempDriverInfo.validFromDate = e.detail.value;
};

const onValidUntilDateChange = (e: any) => {
  console.log('有效结束日期选择变化：', e.detail.value);
  tempDriverInfo.validUntilDate = e.detail.value;
};

onMounted(() => {
  // 设置初始表单数据
  // ... existing code ...
});
</script>

<style lang="scss" scoped>
.drive-card-container {
  min-height: 100vh;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.safe-area-top {
  background-color: #fff;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  text-align: center;
  padding: 30rpx 0;
  position: fixed;
  left: 0;
  right: 0;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  z-index: 99;

  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      width: 40rpx;
      height: 40rpx;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 3rpx;
        background-color: #333;
        left: 4rpx;
        border-radius: 2rpx;
      }

      &::before {
        top: 12rpx;
        transform: rotate(-45deg);
      }

      &::after {
        bottom: 12rpx;
        transform: rotate(45deg);
      }
    }

    &:active {
      opacity: 0.7;
    }
  }

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
    position: relative;
    display: inline-block;
    z-index: 1;
  }

  .title-decoration {
    width: 80rpx;
    height: 12rpx;
    background: linear-gradient(90deg, rgba(255, 155, 172, 0.8), rgba(255, 107, 136, 0.4));
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 6rpx;
    z-index: 0;
  }
}

.content-area {
  padding: 20rpx 30rpx 40rpx;
}

// 证件类型选择器
.card-type-selector {
  margin-bottom: 30rpx;

  .selector-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 20rpx;
    display: block;
  }

  .type-options {
    display: flex;
    width: 100%;
    height: 80rpx;
    background-color: #f0f0f0;
    border-radius: 12rpx;
    overflow: hidden;

    .type-option {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 30rpx;
      position: relative;
      transition: all 0.3s;

      &.active {
        background-color: #fff;
        color: #ff6b88;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 上传区域
.upload-section {
  margin-bottom: 40rpx;

  .upload-area {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    // 电子版水平布局
    &.upload-area-digital {
      flex-direction: row;
      gap: 30rpx;
    }

    .upload-item {
      width: 100%;

      // 电子版样式调整
      &.upload-item-digital {
        width: 50%;
      }

      .upload-preview-container {
        width: 100%;
        height: 360rpx;
        border-radius: 16rpx;
        overflow: hidden;

        // 电子版样式调整
        &.upload-preview-digital {
          height: 450rpx; // 增加高度，更适合电子驾照的竖向展示
        }
      }
    }
  }

  .tip-text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconfont {
      margin-right: 8rpx;
      color: #ff9cac;
      font-size: 28rpx;
    }
  }
}

// 自定义上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  .upload-icon {
    width: 80rpx;
    height: 80rpx;
    border: 4rpx dashed #ddd;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    position: relative;

    &::before,
    &::after {
      content: '';
      position: absolute;
      background-color: #ddd;
    }

    &::before {
      width: 4rpx;
      height: 40rpx;
      top: 20rpx;
      left: 38rpx;
    }

    &::after {
      width: 40rpx;
      height: 4rpx;
      top: 38rpx;
      left: 20rpx;
    }
  }

  .upload-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 驾驶证信息区域
.license-info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }

    .edit-btn {
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 30rpx;

      .edit-text {
        font-size: 26rpx;
        color: #666;
        margin-right: 6rpx;
      }

      .iconfont {
        font-size: 24rpx;
        color: #666;
      }

      &:active {
        opacity: 0.7;
      }
    }
  }

  .info-container {
    .info-row {
      display: flex;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f5f5f5;
      justify-content: space-between;
      align-items: center;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        width: 180rpx;
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
      }

      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        text-align: right;
        padding-left: 20rpx;
      }
    }
  }
}

// 用户协议
.agreement-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 10rpx 0;

  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;
    flex-shrink: 0;

    .checkbox-inner {
      width: 24rpx;
      height: 24rpx;
      border-radius: 50%;
      background-color: transparent;
      transition: all 0.2s;

      &.checked {
        background-color: #ff6b88;
      }
    }
  }

  .agreement-text {
    font-size: 28rpx;
    color: #666;
  }
}

// 提交按钮
.submit-btn {
  margin: 20rpx 0 40rpx;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b88, #ff9cac);
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 136, 0.3);

  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
  }

  &:active {
    transform: scale(0.98);
  }

  &.disabled {
    opacity: 0.6;
  }
}

// 编辑弹窗
.edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 900;

  .dialog-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .dialog-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    transform: translateY(0);
    transition: transform 0.3s;
    z-index: 901;

    .dialog-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30rpx;
      position: relative;
      border-bottom: 1rpx solid #f5f5f5;

      .dialog-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      .close-btn {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40rpx;
        color: #999;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .dialog-content {
      padding: 30rpx;
      max-height: 70vh;
      overflow-y: auto;

      .form-item {
        margin-bottom: 30rpx;
        position: relative;
        z-index: 1010;

        .form-label {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 15rpx;
        }

        .form-input {
          width: 100%;
          height: 80rpx;
          background-color: #f8f8f8;
          border-radius: 8rpx;
          padding: 0 20rpx;
          box-sizing: border-box;
          font-size: 28rpx;
          color: #333;
          border: none;
        }

        .form-picker {
          width: 100%;
          height: 80rpx;
          background-color: #f8f8f8;
          border-radius: 8rpx;
          padding: 0 20rpx;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          border: none;

          .picker-value {
            font-size: 28rpx;
            color: #333;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .picker-arrow {
              font-size: 24rpx;
              color: #999;
            }
          }
        }
      }
    }

    .dialog-footer {
      display: flex;
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #f5f5f5;

      .cancel-btn,
      .confirm-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
      }

      .cancel-btn {
        background-color: #f8f8f8;
        color: #666;
        margin-right: 20rpx;
      }

      .confirm-btn {
        background: linear-gradient(90deg, #ff6b88, #ff9cac);
        color: #fff;
      }
    }
  }
}

// 日期选择包装器
.date-picker-wrapper {
  width: 100%;

  .form-picker {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .iconfont {
      font-size: 36rpx;
      color: #999;
    }
  }
}

// 日期选择弹层
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;

  .date-picker-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .date-picker-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;

    .date-picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f5f5f5;

      .title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      .cancel-btn,
      .confirm-btn {
        font-size: 30rpx;
        padding: 10rpx 20rpx;
      }

      .cancel-btn {
        color: #666;
      }

      .confirm-btn {
        color: #ff6b88;
      }
    }

    .date-picker-view {
      width: 100%;
      height: 400rpx;

      .picker-item {
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        color: #333;
      }
    }
  }
}

.form-picker {
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;

  .picker-value {
    font-size: 28rpx;
    color: #333;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .picker-arrow {
      font-size: 24rpx;
      color: #999;
    }
  }
}

// 确保picker显示在最上层
uni-picker {
  z-index: 9999 !important;
}
</style>