<template>
	<view class="orders">
		<view class="header-bg"></view>
		<view class="search-region" :style="{ top: systemInfo.safeAreaInsets.top * 2 + 'rpx' }">
			<view class="search-box">
				<up-search
					placeholder="搜索我的订单"
					v-model="searchVal"
					height="70rpx"
					:showAction="false"
					bgColor="#fff"
					borderColor="#333"
					searchIconColor="#333"
					@search="searchHandle"
				></up-search>
			</view>
			<view class="filter-box">
				<view class="iconfont icon-owner-a-guangqi_guolv11x"></view>
				<text>筛选</text>
			</view>
		</view>
		<view class="order-tab" :style="{ top: systemInfo.safeAreaInsets.top * 2 + 100 + 'rpx' }">
			<cu-tab-swiper
				:tabList="tabList"
				:height="swiperHeight"
				:triggerPro="triggerPro"
				:loadStatusPro="loadStatusPro"
				@refresh="refresh"
				@loadMore="loadMore"
				@tabChange="tabChange"
			>
				<template #all>
          <orderItem :orderList="orderList" @clickItem="orderClick"></orderItem>
				</template>
        <template #transit>
          <orderItem :orderList="transitOrders" @clickItem="orderClick"></orderItem>
        </template>
        <template #completed>
          <orderItem :orderList="completedOrders" @clickItem="orderClick"></orderItem>
        </template>
        <template #cancelled>
          <orderItem :orderList="cancelledOrders" @clickItem="orderClick"></orderItem>
        </template>
			</cu-tab-swiper>
		</view>
	</view>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from 'vue';
import { onShow } from '@dcloudio/uni-app';
import orderItem from './components/orderItem.vue';
import { orderInfoPage } from "../../api/transport";

const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 70 + 100 + 'rpx';
// 这个用来拉长背景色
const heightHeaderBg = systemInfo.safeAreaInsets.top * 2 + 70 + 100 + 50 + 'rpx';
console.log(1, systemInfo);

const swiperHeight = ref('150rpx');

const searchVal = ref('');
const searchHandle = async (val: any) => {
  searchVal.value = val;
  await fetchOrders(1);
};
const tabList = [
	{
		text: '全部',
		value: '0'
	},
	{
		text: '运输中',
		value: '1'
	},
	{
		text: '已完成',
		value: '2'
	},
	{
		text: '已取消',
		value: '3'
	}
];

// 当前激活的标签
const activeTab = ref('0');
// 订单列表
const orderList = ref<any[]>([]);
// 当前页码
const currentPage = ref(1);
// 每页数量
const pageSize = ref(10);
const itemCount = ref(0);
const itemTotal = 24;
onShow(() => {
	// #ifdef H5
	swiperHeight.value = `calc(100vh - ${heightHeader} - var(--tab-bar-height) - env(safe-area-inset-bottom))`;
	// #endif
	// #ifdef APP-PLUS
	swiperHeight.value = `calc(100vh - ${heightHeader})`;
	// #endif
});
// 计算属性，根据不同标签筛选订单
const transitOrders = computed(() => orderList.value.filter(order => order.status === 'transit'));
const completedOrders = computed(() => orderList.value.filter(order => order.status === 'completed'));
const cancelledOrders = computed(() => orderList.value.filter(order => order.status === 'cancelled'));

// 获取订单数据
const fetchOrders = async (page: number) => {
  try {
    const response:any = await orderInfoPage({
      page,
      limit: pageSize.value,
      orderStatus: activeTab.value,
      keyword: searchVal.value
    });

    if (response.code === 700) {
      if (page === 1) {
        orderList.value = response.result || [];
      } else {
        orderList.value = [...orderList.value, ...(response.result || [])];
      }

      if (!response.result || response.result.length < pageSize.value) {
        loadStatusPro.value = 'no-more';
      } else {
        loadStatusPro.value = 'more';
      }
    } else {
      console.error('获取订单列表失败，错误码:', response.code);
      loadStatusPro.value = 'more';
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    loadStatusPro.value = 'more';
  }
};
// 下拉刷新
const triggerPro = ref(true);
const refresh = async (activeTab: any) => {
  triggerPro.value = true;
  currentPage.value = 1;
  await fetchOrders(1);
  triggerPro.value = false;
};
// 上拉加载
const loadStatusPro = ref('loading');
const loadMore = async (activeTab: any) => {
  if (loadStatusPro.value === 'more') {
    currentPage.value++;
    await fetchOrders(currentPage.value);
  }
};
const tabChange = async (tab: any) => {
  if (tab === 0) {
    activeTab.value = '0'
  } else if (tab === 1) {
    activeTab.value = '1'
  } else if (tab === 2) {
    activeTab.value = '2'
  } else if (tab === 3) {
    activeTab.value = '99'
  } else {
    activeTab.value = '0'
  }

  loadStatusPro.value = 'loading';
  currentPage.value = 1;
  await fetchOrders(1);
};
const getInitData = async () => {
  await fetchOrders(1);
};
// 点击订单item
const orderClick = (item: any) => {
	uni.navigateTo({
		url: '/pageCom/orders/completed',
    success: (res) => {
      // 使用 eventChannel 发送数据
      res.eventChannel.emit('orderId', { id: item.id });
    }
	});
};
onMounted(() => {
	getInitData();
});
</script>

<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeaderBg);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
.search-region {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 70rpx;
	z-index: 1;
	display: flex;
	align-items: center;
	padding: 0rpx 32rpx;
	box-sizing: border-box;
	.search-box {
		flex: 1;
	}
	.filter-box {
		width: 100rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.iconfont {
			font-size: 34rpx;
			color: #333;
		}
		text {
			font-size: 22rpx;
			color: #333;
		}
	}
}
.order-tab {
	width: 100%;
	position: fixed;
	z-index: 1;
}
</style>
