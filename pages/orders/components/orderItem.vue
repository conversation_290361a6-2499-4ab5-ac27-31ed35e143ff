<template>
	<view class="order-list">
    <view class="order-item" v-for="(item, index) in orderList" :key="item.id || index" @click="clickItem(item)">
			<view class="status-box">
				<view class="status-left">
					<image src="@/static/images/yikoujia.png" class="yikoujia"></image>
<!--
          <text class="text">{{ item.type || '一口价' }}</text>
-->
				</view>
        <view class="status-right">{{ getOrderStatusText(item.orderStatus) }}</view>
			</view>
			<view class="driver-info">
				<view class="driver-left">
					<image src="@/static/images/head.png" class="head"></image>
          <text class="name">{{ item.driverName }}</text>
          <text class="car"><!--{{ item.carNumber || '辽PD4640' }} | -->{{ item.vehicleLength }}{{ item.vehicleType }}</text>
				</view>
				<view class="driver-right">
					<view class="icon">
						<up-icon name="phone-fill" size="18"></up-icon>
					</view>
				</view>
			</view>
			<view class="trajectory-info">
				<view class="trajectory-left">
					<view class="start">
            <view class="start-address">{{ item.loadingAddressName || '潍坊市 寒亭区 潍坊盛大美港' }}</view>
            <view class="start-time">{{ item.loadingDate }} {{item.earliestLoadingTime}} -  {{item.latestLoadingTime}}</view>
					</view>
					<view class="start end">
            <view class="start-address">{{ item.unloadingAddressName || '天津市 西青区 天津市心理学花色材料有限责任公司' }}</view>
            <view class="start-time">{{ item.loadingDate }} {{item.earliestLoadingTime}} -  {{item.latestLoadingTime}}</view>
					</view>
				</view>
				<view class="trajectory-right">
					<view class="iconfont icon-owner-guiji"></view>
					<text>查看轨迹</text>
				</view>
			</view>
			<view class="price-info">
				订金
        <text>¥{{ item.depositAmount }}</text>
				(已退还司机) 运费
        <text>¥{{ item.bidAmount }}</text>
				(已选择线下支付)
			</view>
			<view class="handle-box">
<!--
				<view class="btn-span">删除订单</view>
-->
				<view class="btn-span">再来一单</view>
<!--
				<view class="btn-span btn-span-red">评价司机</view>
-->
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
// 定义接收的属性
const props = defineProps<{
  orderList: Array<{
    id?: number;
    loadingAddressName?: string;
    unloadingAddressName?: string;
    cargoName?: string;
    packagingMethod?: string;
    vehicleLength?: string;
    vehicleType?: string;
    bidType?: string;
    bidAmount?: string;
    depositAmount?: number;
    loadingDate?: string;
    driverName?: string;
    orderStatus?: string;
    paymentMethod?: string;
    earliestLoadingTime?: string;
    latestLoadingTime?: string;
  }>;
}>();
const getOrderStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '运输中',
    2: '已完成',
    99: '已取消',
  };
  return statusMap[status] || '未知状态';
};
const emit = defineEmits(['clickItem']);
const clickItem = (item: any) => {
	emit('clickItem', item);
};
</script>

<style lang="scss" scoped>
.order-list {
	width: 100%;
	.order-item {
		background-color: #fff;
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		border-radius: 26rpx;
		margin-top: 20rpx;
		&:first-child {
			margin-top: 0rpx;
		}
		.status-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0rpx;
			border-bottom: 2rpx solid $uni-text-color-grey;
			.status-left {
				display: flex;
				align-items: center;
				.yikoujia {
					width: 50rpx;
					height: 50rpx;
				}
				.text {
					font-size: $uni-font-size-base-30;
					color: #000;
					margin-left: 16rpx;
				}
			}
			.status-right {
				font-size: $uni-font-size-sm;
				color: $uni-text-color-grey;
			}
		}
		.driver-info {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 0rpx;
			border-bottom: 2rpx solid $uni-text-color-grey;
			.driver-left {
				display: flex;
				align-items: center;
				.head {
					width: 60rpx;
					height: 60rpx;
					border-radius: 10rpx;
				}
				.name {
					font-size: $uni-font-size-base;
					color: $uni-text-color;
					margin-left: 10rpx;
				}
				.car {
					font-size: $uni-font-size-base;
					color: $uni-text-color-grey;
					margin-left: 10rpx;
				}
			}
			.driver-right {
				.icon {
					width: 50rpx;
					height: 50rpx;
					border: 2rpx solid #000;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
		.trajectory-info {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 0rpx;
			border-bottom: 2rpx solid $uni-text-color-grey;
			.trajectory-left {
				width: 540rpx;
				padding-left: 30rpx;
				.start {
					position: relative;
					&:before {
						content: '';
						width: 14rpx;
						height: 14rpx;
						background-color: #4997e9;
						border-radius: 50%;
						position: absolute;
						left: -30rpx;
						top: 8rpx;
					}
					.start-address {
						font-size: $uni-font-size-base;
						line-height: $uni-font-size-base;
						color: $uni-text-color;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					.start-time {
						font-size: $uni-font-size-sm;
						line-height: $uni-font-size-sm;
						color: $uni-text-color-grey;
						margin-top: 10rpx;
					}
				}
				.end {
					margin-top: 20rpx;
					&:before {
						background-color: #e9534c;
					}
				}
			}
			.trajectory-right {
				width: 160rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				box-shadow: -14rpx 0rpx 10rpx -10rpx #ccc;
				.iconfont {
					font-size: 50rpx;
				}
				text {
					font-size: 22rpx;
					margin-top: 10rpx;
				}
			}
		}
		.price-info {
			font-size: $uni-font-size-sm;
			line-height: $uni-font-size-sm;
			color: $uni-text-color-grey;
			text-align: right;
			padding: 20rpx 0rpx;
			text {
				color: #e9534c;
				font-weight: 600;
			}
		}
		.handle-box {
			padding-bottom: 20rpx;
			display: flex;
			justify-content: flex-end;
			.btn-span {
				font-size: $uni-font-size-base;
				line-height: $uni-font-size-base;
				padding: 12rpx 12rpx;
				box-sizing: border-box;
				border: 2rpx solid #333;
				border-radius: 8rpx;
				margin-left: 10rpx;
			}
			.btn-span-red {
				background-color: #e7473f;
				color: #fff;
				border: none;
			}
		}
	}
}
</style>
