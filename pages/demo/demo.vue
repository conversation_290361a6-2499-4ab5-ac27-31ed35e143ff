<!-- tab -->
<template>
	<view class="tab">
		<view style="padding: 0 24rpx">
			<view class="v-tabs">
				<view
					v-for="(item, index) in tabList"
					:key="index"
					class="custom-tab"
					:class="activeTab === index ? `active  active${index}` : `active${index}`"
					@click="changeTab($event, index)"
				>
					{{ item }}
				</view>
				<view class="underline" :animation="animationData"></view>
			</view>
		</view>
		<swiper class="swiper" :circular="false" :indicator-dots="false" :autoplay="false" @change="change" @transition="transition" :current="activeTab" :duration="200">
			<swiper-item v-for="(item, index) in tabList" :item-id="String(index)" :key="index">
				<!-- <slot :name="item"></slot> -->
				23443
			</swiper-item>
		</swiper>
	</view>
</template>

<script lang="ts" setup>
import { onMounted, getCurrentInstance, ref, reactive, nextTick } from 'vue';
const activeTab = ref(0);
const tabList = ref(['全部', '未完成', '已完成', '已取消']);
const underlineTranslateX = ref(0);
const widthList = ref([]);
const screenWidth = ref(0);

let animationData = ref<any>({});
let animation = reactive<any>({});

const tabWidth: any = ref('');

onMounted(async () => {
	// 初始化动画
	animation = uni.createAnimation();
	// 初始化每个tab宽度
	let fixNum: any = (100 / tabList.value.length).toFixed(2);
	tabWidth.value = fixNum + '%';
	//储存宽度
	setTimeout(async () => {
		for (let i = 0; i < tabList.value.length; i++) {
			const instance = getCurrentInstance();
			const query = uni.createSelectorQuery().in(this);
			const data: any = await new Promise((resolve) => {
				query.select(`.active${i}`).boundingClientRect(resolve).exec();
			});
			widthList.value.push(data.width);
			underlineTranslateX.value = widthList.value[0] / 2 - 8; //这里的8是下划线宽度的一半
			startX.value = JSON.parse(JSON.stringify(underlineTranslateX.value));
		}
		screenWidth.value = uni.getSystemInfoSync().windowWidth;
		antimate(underlineTranslateX.value);
	}, 50);
});

const antimate = (y: any) => {
	animationData.value = {};
	animation.translateX(y).step({
		// 执行动画移动过程所需时间
		duration: 300
	});
	animationData.value = animation.export();
};

// 滑块位置
const changeTab = (_event: any, index: any) => {
	activeTab.value = index;
	let width = 0;
	for (let i = 0; i <= index; i++) {
		if (widthList.value[i] != 'undefined') {
			width += Number(widthList.value[i]);
		}
	}
	width -= widthList.value[index] / 2; //这里是tab宽度减去自身的1/2
	// width += index - 8;
	width -= 8;
	underlineTranslateX.value = width;
	antimate(underlineTranslateX.value);
	startX.value = JSON.parse(JSON.stringify(underlineTranslateX.value));
	//changeTab改变时 取消页面滑动改变
	transitionB.value = false;
	setTimeout(() => {
		transitionB.value = true;
	}, 300);
};

//翻页
const change = (e) => {
	if (e.detail) {
		changeTab('', e.detail.current);
	}
};

//滑块随着页面翻动而移动
const startX = ref(0); // 触摸开始时的 X 坐标
const transitionB = ref(true); // 触摸开始时的 X 坐标
const transition = (e) => {
	if (e.detail.dx < 0 && activeTab.value != 0 && transitionB.value) {
		//避免第一个翻页
		//滚动长度是margin的一般 自身的一半以及 滚动的下一个目标的一半
		underlineTranslateX.value = startX.value + (e.detail.dx / screenWidth.value) * (32 + widthList.value[activeTab.value] / 2 + widthList.value[activeTab.value - 1] / 2);
	} else if (e.detail.dx > 0 && activeTab.value != tabList.value.length - 1 && transitionB.value) {
		//滚动长度是margin的一般 自身的一半以及 滚动的下一个目标的一半  右滑是加1
		underlineTranslateX.value = startX.value + (e.detail.dx / screenWidth.value) * (32 + widthList.value[activeTab.value] / 2 + widthList.value[activeTab.value + 1] / 2);
		//向右滑动
	}
	antimate(underlineTranslateX.value);
};
</script>
<style lang="scss" scoped>
.v-tabs {
	display: flex;
	align-items: center;
	position: relative;
	perspective: 1000;
	.custom-tab {
		width: v-bind(tabWidth);
		text-align: center;
		font-size: 32rpx;
		color: #969ba0;
	}
	.active {
		color: #171b1e;
	}
	.underline {
		width: 32rpx;
		height: 8rpx;
		background: #0078e6;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		position: absolute;
		bottom: -8rpx;
		left: 0;
		transition: width 0.3s, transform 0.3s;
	}
}

.tab {
	height: 100%;
}
::v-deep .swiper {
	height: 300rpx;
}
</style>
