<template>
  <view class="first-page">
    <!-- 顶部LOGO/品牌区 -->
    <view class="logo-bar">
      <image class="logo" src="@/static/images/loading_car.gif" mode="aspectFit" />
      <text class="brand">自由发货</text>
    </view>
    <view class="slogan">让货运更简单</view>
    <!-- 大标题 -->
    <view class="main-title">自由发货，货运APP</view>
    <!-- 副标题 -->
    <view class="sub-title">高效找车 · 安全托运 · 省心省钱</view>
    <!-- 特色介绍 -->
    <view class="features">
      <view class="feature-item">
        <u-icon name="car" size="40" color="#7ac251" />
        <text>多种车型</text>
      </view>
      <view class="feature-item">
        <u-icon name="map" size="40" color="#e84138" />
        <text>全程可追踪</text>
      </view>
      <view class="feature-item">
        <u-icon name="order" size="40" color="#3a7afe" />
        <text>一键下单</text>
      </view>
    </view>
    <view class="desc">全国覆盖 · 百万司机 · 极速响应</view>
    <!-- 底部气泡点缀 -->
    <view class="bubbles">
      <view class="bubble bubble1"></view>
      <view class="bubble bubble2"></view>
      <view class="bubble bubble3"></view>
      <view class="bubble bubble4"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { setStoragetItem } from '@/utils/storage';
import { getLoginUser } from "@/api/transport";
import { ws } from '@/utils/websocket';

const convertUserType = (backendUserType: number | string): string => {
  const typeMap: { [key: string]: string } = {
    '1': 'shipper_personal',  // 个人货主
    '2': 'driver',            // 司机
    '3': 'station',           // 配货站
    '4': 'shipper_company'    // 企业货主
  };

  const typeKey = String(backendUserType);
  const frontendType = typeMap[typeKey];

  return frontendType || '';
};

onLoad(() => {
  setTimeout(async () => {
    console.log('🚀 启动页延迟完成，开始检查用户登录状态');

    // 检查用户登录状态
    const isLoggedIn = ws.checkLoginStatus();

    console.log('👤 用户登录状态检查结果:', isLoggedIn);

    if (isLoggedIn) {
      const resultLoginUser: any = await getLoginUser();
      setStoragetItem('userInfo', resultLoginUser.result);
      // 处理用户类型
      if (resultLoginUser.result.userType) {
        // 转换后端数字类型为前端字符串类型
        const frontendUserType = convertUserType(resultLoginUser.result.userType);

        if (frontendUserType && resultLoginUser.result.auditStatus) {
          // 用户已有身份类型，保存转换后的类型
          setStoragetItem('userType', frontendUserType);
          console.log('✅ 登录成功，用户类型:', {
            backend: resultLoginUser.result.userType,
            frontend: frontendUserType
          });
          if (resultLoginUser.result.auditStatus == 1) {
            // 已知身份类型，但是认证审核中，跳转到审核中页面
            uni.reLaunch({
              url: '/pages/auth/in-review'
            });
          } else if (resultLoginUser.result.auditStatus == 3) {
            // 已知身份类型，但是认证未通过，跳转到选择身份页面
            uni.reLaunch({
              url: '/pages/entrance/entrance'
            });
          } else {
            // 跳转到首页（WebSocket连接将在首页建立）  
            console.log('🚀 跳转到首页，WebSocket连接将在首页建立');
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }
        } else {
          // 未知的用户类型，需要选择身份
          console.log('⚠️ 登录成功，但用户类型未知:', resultLoginUser.result.userType);
          uni.reLaunch({
            url: '/pages/entrance/entrance'
          });
        }
      } else {
        // 用户没有身份类型，需要选择身份
        console.log('⚠️ 登录成功，但用户需要选择身份类型');
        uni.reLaunch({
          url: '/pages/entrance/entrance'
        });
      }
    } else {
      // 用户未登录，跳转到登录页（数据清理将在登录页统一处理）
      console.log('❌ 用户未登录，跳转到登录页');
      uni.reLaunch({
        url: '/pages/login/login'
      });
    }
  }, 2000)
})
</script>

<style lang="scss" scoped>
.first-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #ffe6e6 0%, #fff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 40rpx;
  position: relative;
  box-sizing: border-box;
}

.logo-bar {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;

  .logo {
    width: 80rpx;
    height: 80rpx;
    margin-right: 18rpx;
    border-radius: 20rpx;
    background: #fff;
  }

  .brand {
    font-size: 38rpx;
    font-weight: bold;
    color: #e84138;
    letter-spacing: 2rpx;
  }
}

.slogan {
  font-size: 28rpx;
  color: #7ac251;
  margin-bottom: 36rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 24rpx;
  text-align: center;
}

.sub-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  text-align: center;
}

.features {
  display: flex;
  justify-content: center;
  gap: 60rpx;
  margin-top: 60rpx;

  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 26rpx;
    color: #333;

    u-icon {
      margin-bottom: 10rpx;
    }
  }
}

.desc {
  margin-top: 60rpx;
  font-size: 26rpx;
  color: #999;
  letter-spacing: 2rpx;
  text-align: center;
}

.bubbles {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 60rpx;
  display: flex;
  justify-content: center;
  pointer-events: none;
}

.bubble {
  border-radius: 50%;
  opacity: 0.18;
  margin: 0 12rpx;

  &.bubble1 {
    width: 38rpx;
    height: 38rpx;
    background: #e84138;
  }

  &.bubble2 {
    width: 24rpx;
    height: 24rpx;
    background: #7ac251;
  }

  &.bubble3 {
    width: 32rpx;
    height: 32rpx;
    background: #3a7afe;
  }

  &.bubble4 {
    width: 18rpx;
    height: 18rpx;
    background: #e84138;
  }
}
</style>