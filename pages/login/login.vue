<template>
  <view class="login-container">
    <view class="login-content">
      <view class="logo-box">
        <image class="logo" src="/static/images/loading_car.gif" mode="aspectFit"></image>
        <text class="app-name">运通达</text>
      </view>

      <view class="form-box">
        <view class="form-item">
          <view class="input-label">手机号码</view>
          <up-input placeholder="请输入手机号" border="surround" v-model="formState.phone" prefixIcon="phone" class="input-field"></up-input>
        </view>

        <view class="form-item">
          <view class="input-label">验证码</view>
          <view class="verify-code-input">
            <up-input placeholder="请输入验证码" border="surround" v-model="formState.verifyCode" class="code-input-field"></up-input>
            <up-button type="primary" size="mini" class="code-btn" :disabled="countdown > 0" @click="getVerifyCode" :loading="sendingCode">{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}</up-button>
          </view>
        </view>

        <!-- 协议勾选区域 -->
        <view class="protocol-agreement">
          <view class="agreement-radio" @click="toggleAgreement">
            <view class="radio-icon" :class="{ 'checked': agreeProtocol }">
              <view class="radio-dot" v-if="agreeProtocol"></view>
            </view>
            <view class="agreement-text">
              <text class="agreement-content">
                我已阅读并同意<view class="protocol-link" @click.stop="handleProtocolLinkClick">《用户服务协议》</view>、<view class="protocol-link" @click.stop="handleProtocolLinkClick">《个人信息保护政策》</view>、<view class="protocol-link" @click.stop="handleProtocolLinkClick">《用户授权协议》</view>，未注册手机号将自动创建<text class="brand-name">运通达账号</text>
              </text>
            </view>
          </view>
        </view>

        <view class="login-btn">
          <up-button type="primary" text="登 录" @click="handleLogin" :loading="loading"></up-button>
        </view>
      </view>
    </view>

    <view class="footer">
      <text class="copyright">© 2025 危险品运输平台 版权所有</text>
    </view>

    <!-- 协议确认弹窗 -->
    <cu-confirm-dialog
      ref="protocolDialog"
      title=""
      :content="protocolDialogContent"
      cancel-text="不同意"
      confirm-text="同意并继续"
      :show-cancel="true"
      @cancel="handleProtocolCancel"
      @confirm="handleProtocolConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { reactive, ref, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { currRoute } from '@/utils/routeTool';
import { login, sendVerifyCodeSms } from '@/api/common';
import { setStoragetItem, removeStoragetItem } from '@/utils/storage';
import { mobileLogin } from "../../api/common";
import { getLoginUser } from "../../api/transport";
import { ws } from '@/utils/websocket';

const formState: any = reactive({
  phone: '',
  verifyCode: ''
});
const loading = ref(false);
const sendingCode = ref(false);
const countdown = ref(0);
const agreeProtocol = ref(false); // 协议勾选状态
let timer: any = null;

// 协议确认弹窗引用
const protocolDialog = ref(null);

// 协议弹窗内容
const protocolDialogContent = ref('我已阅读并同意《服务及交易协议》、《个人信息保护政策》，未注册手机号将自动创建运通达账号');

/**
 * 清理用户数据
 * 确保进入登录页时是干净的状态，适用于所有进入登录页的场景：
 * 1. 启动时检查未登录
 * 2. 用户主动退出登录
 * 3. token过期被踢出
 * 4. 其他需要重新登录的场景
 */
const clearUserData = () => {
  console.log('🧹 登录页：清理残留的用户数据');

  // 清理所有用户相关的存储数据
  removeStoragetItem('x-token');
  removeStoragetItem('userInfo');
  removeStoragetItem('userType');

  // 断开WebSocket连接（如果有的话）
  if (ws.isConnected()) {
    console.log('🔌 登录页：断开残留的WebSocket连接');
    ws.disconnect();
  }

  console.log('✅ 登录页：用户数据清理完成，准备开始新的登录流程');
}

// 页面加载时清理数据
onLoad(() => {
  clearUserData();
});

/**
 * 转换用户类型：后端数字类型 → 前端字符串类型
 * @param backendUserType 后端返回的用户类型数字
 * @returns 前端使用的用户类型字符串
 */
const convertUserType = (backendUserType: number | string): string => {
  const typeMap: { [key: string]: string } = {
    '1': 'shipper_personal',  // 个人货主
    '2': 'driver',            // 司机
    '3': 'station',           // 配货站
    '4': 'shipper_company'    // 企业货主
  };

  const typeKey = String(backendUserType);
  const frontendType = typeMap[typeKey];

  return frontendType || '';
};

// 验证手机号格式
const validatePhone = (phone: string): boolean => {
  if (!phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
      duration: 2000
    });
    return false;
  }
  const phoneReg = /^1[3-9]\d{9}$/;
  if (!phoneReg.test(phone)) {
    uni.showToast({
      title: '手机号格式不正确',
      icon: 'none',
      duration: 2000
    });
    return false;
  }
  return true;
};

// 切换协议勾选状态
const toggleAgreement = () => {
  agreeProtocol.value = !agreeProtocol.value;
};

// 处理协议弹窗取消
const handleProtocolCancel = () => {
  console.log('用户不同意协议');
  // 弹窗会自动关闭，不需要额外操作
};

// 处理协议弹窗确认
const handleProtocolConfirm = () => {
  console.log('用户同意协议并继续');
  // 自动勾选协议
  agreeProtocol.value = true;
  // 显示提示消息，告知用户协议已同意
  uni.showToast({
    title: '协议已同意，请点击登录',
    icon: 'success',
    duration: 2000
  });
};

// 处理协议链接点击
const handleProtocolLinkClick = () => {
  console.log('点击协议链接，跳转到协议列表页面');
  uni.navigateTo({
    url: '/pages/login/agreement'
  });
};

// 获取验证码
const getVerifyCode = async () => {
  if (!validatePhone(formState.phone)) return;

  sendingCode.value = true;
  try {
    const result: any = await sendVerifyCodeSms({
      mobile: formState.phone,
    });
    uni.hideLoading();
    if (result.code == 700) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
        duration: 2000
      });

      // 开始倒计时
      countdown.value = 60;
      timer = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value--;
        } else {
          clearInterval(timer);
          timer = null;
        }
      }, 1000);
    } else {
      uni.showToast({
        title: result.msg || '发送验证码失败',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none',
      duration: 2000
    });
  } finally {
    sendingCode.value = false;
  }
};

// 登录
const handleLogin = async () => {
  if (!validatePhone(formState.phone)) return;

  if (!formState.verifyCode) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  if (!agreeProtocol.value) {
    protocolDialog.value?.open();
    return;
  }

  loading.value = true;
  try {
    // 构建登录参数，兼容原来的接口
    const paramData = {
      mobile: formState.phone,
      verifyCode: formState.verifyCode,
      // type: 'sms'  // 使用验证码登录
    };

    const result: any = await mobileLogin(paramData);
    uni.hideLoading();
    if (result.code == 700) {
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      });

      // 保存用户信息和token
      setStoragetItem('x-token', result.result.token);
      // 登录成功后获取用户信息（用户信息在登录接口中没有返回）
      const resultLoginUser: any = await getLoginUser();
      setStoragetItem('userInfo', resultLoginUser.result);
      // 处理用户类型
      if (resultLoginUser.result.userType) {
        // 转换后端数字类型为前端字符串类型
        const frontendUserType = convertUserType(resultLoginUser.result.userType);

        if (frontendUserType && resultLoginUser.result.auditStatus) {
          // 用户已有身份类型，保存转换后的类型
          setStoragetItem('userType', frontendUserType);
          console.log('✅ 登录成功，用户类型:', {
            backend: resultLoginUser.result.userType,
            frontend: frontendUserType
          });
          if (resultLoginUser.result.auditStatus == 1) {
            // 已知身份类型，但是认证审核中，跳转到审核中页面
            uni.reLaunch({
              url: '/pages/auth/in-review'
            });
          } else if (resultLoginUser.result.auditStatus == 3) {
            // 已知身份类型，但是认证未通过，跳转到选择身份页面
            uni.reLaunch({
              url: '/pages/entrance/entrance'
            });
          } else {
            // 跳转到首页（WebSocket连接将在首页建立）  
            console.log('🚀 跳转到首页，WebSocket连接将在首页建立');
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }
        } else {
          // 未知的用户类型，需要选择身份
          console.log('⚠️ 登录成功，但用户类型未知:', resultLoginUser.result.userType);
          uni.reLaunch({
            url: '/pages/entrance/entrance'
          });
        }
      } else {
        // 用户没有身份类型，需要选择身份
        console.log('⚠️ 登录成功，但用户需要选择身份类型');
        uni.reLaunch({
          url: '/pages/entrance/entrance'
        });
      }
    } else {
      uni.showToast({
        title: resultLoginUser.msg || '登录失败',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none',
      duration: 2000
    });
  } finally {
    loading.value = false;
  }
};

// 在组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style scoped lang="scss">
.login-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f0f4ff, #e6eeff);
  position: relative;

  .login-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 50rpx;
  }

  .logo-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60rpx;

    .logo {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 20rpx;
      border-radius: 20rpx;
      box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
    }

    .app-name {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      letter-spacing: 2rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }
  }

  .form-box {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx;
    padding: 50rpx 40rpx;
    box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);

    .form-item {
      margin-bottom: 35rpx;

      .input-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 15rpx;
        font-weight: 500;
      }

      .input-field,
      .code-input-field {
        height: 90rpx;
        border-radius: 12rpx;
        background-color: #f8f9fa;
        border: 1px solid #eaeaea;

        :deep(.u-input__content__field-wrapper) {
          padding-left: 20rpx;
        }
      }

      .verify-code-input {
        display: flex;
        flex-direction: row;
        align-items: center;

        .code-input-field {
          flex: 1;
        }

        .code-btn {
          margin-left: 20rpx;
          height: 90rpx;
          width: 180rpx;
          border-radius: 12rpx;
          font-size: 24rpx;
          font-weight: 500;
          background: linear-gradient(135deg, #4b7bec, #3867d6);
          border: none;

          &:disabled {
            background: #a5b1c2;
            opacity: 0.8;
          }
        }
      }
    }

    .protocol-agreement {
      margin-top: 30rpx;
      margin-bottom: 20rpx;

      .agreement-radio {
        display: flex;
        align-items: flex-start;
        cursor: pointer;

        .radio-icon {
          width: 32rpx;
          height: 32rpx;
          border: 2rpx solid #ddd;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 16rpx;
          margin-top: 4rpx;
          flex-shrink: 0;
          transition: all 0.3s ease;
          background-color: #fff;

          &.checked {
            background-color: #fff;
            border-color: #4b7bec;
          }

          .radio-dot {
            width: 16rpx;
            height: 16rpx;
            background-color: #4b7bec;
            border-radius: 50%;
            transition: all 0.3s ease;
          }
        }

        .agreement-text {
          flex: 1;
          line-height: 1.6;

          .agreement-content {
            font-size: 24rpx;
            color: #666;
            line-height: 1.6;

            .protocol-link {
              color: #4b7bec;
              text-decoration: none;
              display: inline;
              cursor: pointer;
              transition: opacity 0.3s ease;

              &:active {
                opacity: 0.7;
              }
            }

            .brand-name {
              color: #333;
              font-weight: 500;
            }
          }
        }
      }
    }

    .login-btn {
      margin-top: 60rpx;

      :deep(.u-button) {
        height: 100rpx;
        border-radius: 16rpx;
        font-size: 32rpx;
        font-weight: bold;
        background: linear-gradient(135deg, #4b7bec, #3867d6);
        border: none;
        box-shadow: 0 10rpx 20rpx rgba(59, 103, 214, 0.2);
        letter-spacing: 4rpx;
      }
    }
  }

  .footer {
    padding: 40rpx 0;
    text-align: center;

    .copyright {
      font-size: 24rpx;
      color: #7f8c8d;
    }
  }
}
</style>
