<template>
  <view class="agreement-detail-page">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: systemInfo.statusBarHeight * 2 + 'rpx' }"></view>
    
    <!-- 头部导航 -->
    <view class="header-container" :style="{ top: systemInfo.statusBarHeight * 2 + 'rpx' }">
      <view class="header">
        <view class="back-icon" @click="goBack">
          <text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
        </view>
        <view class="title">{{ agreementTitle }}</view>
      </view>
    </view>

    <!-- 协议内容区域 -->
    <scroll-view class="content-container" scroll-y="true" :style="{ top: (systemInfo.statusBarHeight * 2 + 88 + 20) + 'rpx' }">
      <view class="content-wrapper">
        <rich-text class="agreement-content" :nodes="agreementContent"></rich-text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getAgreementById } from '@/utils/agreementData';

// 获取系统信息
const systemInfo: any = uni.getSystemInfoSync();

// 响应式数据
const agreementTitle = ref('协议详情');
const agreementContent = ref('');
const agreementId = ref('');

// 页面加载时获取协议内容
onLoad((options: any) => {
  console.log('协议详情页面参数:', options);

  if (options.id) {
    agreementId.value = options.id;
    loadAgreementContent(options.id);
  } else {
    // 如果没有传递协议ID，显示错误信息
    agreementTitle.value = '参数错误';
    agreementContent.value = '<p style="text-align: center; color: #999; margin-top: 200rpx;">协议参数错误，请返回重试。</p>';
  }
});

// 加载协议内容
const loadAgreementContent = (id: string) => {
  try {
    const agreementData = getAgreementById(id);
    agreementTitle.value = agreementData.title;
    agreementContent.value = agreementData.content;
    
    console.log('加载协议内容:', {
      id,
      title: agreementData.title,
      contentLength: agreementData.content.length
    });
  } catch (error) {
    console.error('加载协议内容失败:', error);
    agreementTitle.value = '加载失败';
    agreementContent.value = '<p style="text-align: center; color: #999; margin-top: 100px;">协议内容加载失败，请返回重试。</p>';
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style scoped lang="scss">
.agreement-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.status-bar {
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header-container {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 99;
}

.header {
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  position: relative;

  .back-icon {
    position: absolute;
    left: 30rpx;
    z-index: 10;

    .iconfont {
      font-size: 40rpx;
      color: #333;
    }
  }

  .title {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    padding: 0 80rpx; // 给左右留出空间避免与返回按钮重叠
  }
}

.content-container {
  background-color: #fff;
  // margin: 20rpx;
  border-radius: 16rpx;
  position: fixed;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  width: calc(100vw - 40rpx);
  max-width: calc(100vw - 40rpx);
  overflow: hidden;
  box-sizing: border-box;
}

.content-wrapper {
  padding: 40rpx 30rpx;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.agreement-content {
  line-height: 1.8;
  color: #333;
  width: 100% !important;
  max-width: 100% !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow: hidden !important;
  box-sizing: border-box !important;

  // 全局样式，影响rich-text内部元素
  :deep(h2) {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin: 40rpx 0 30rpx 0;
    text-align: center;
    border-bottom: 2px solid #4b7bec;
    padding-bottom: 20rpx;
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
  }

  :deep(h3) {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin: 30rpx 0 20rpx 0;
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
  }

  :deep(p) {
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
    margin: 16rpx 0;
    text-align: justify;
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  :deep(strong) {
    font-weight: 600;
    color: #333;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
  }

  :deep(div) {
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  // 列表样式
  :deep(ul), :deep(ol) {
    margin: 16rpx 0;
    padding-left: 40rpx;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  :deep(li) {
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
    margin: 8rpx 0;
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  // 确保所有可能的元素都不会溢出
  :deep(*) {
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
  }
}
</style>
