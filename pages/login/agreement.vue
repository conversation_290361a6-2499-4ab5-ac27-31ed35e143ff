<template>
  <view class="agreement-page">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: systemInfo.statusBarHeight + 'px' }"></view>

    <!-- 头部导航 -->
    <view class="header-container">
      <view class="header">
        <view class="back-icon" @click="goBack">
          <text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
        </view>
        <view class="title">运通达服务及交易协议、隐私政策</view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <!-- 主标题 -->
      <view class="main-title">
        <text class="title-text">服务及交易协议、隐私政策</text>
      </view>

      <!-- 协议列表 -->
      <view class="agreement-list">
        <view
          v-for="(agreement, index) in agreementList"
          :key="index"
          class="agreement-item"
          @click="handleAgreementClick(agreement)"
        >
          <text class="agreement-name">{{ agreement.name }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 获取系统信息
const systemInfo: any = uni.getSystemInfoSync();

// 协议列表数据
const agreementList = ref([
  {
    id: 'transport',
    name: '《货物运输协议》',
    url: '/pages/agreement/transport'
  },
  {
    id: 'authorization',
    name: '《用户授权协议》',
    url: '/pages/agreement/authorization'
  },
  {
    id: 'security',
    name: '《安全服务及敏感个人信息处理协议》',
    url: '/pages/agreement/security'
  },
  {
    id: 'service',
    name: '《运通达用户服务协议》',
    url: '/pages/agreement/service'
  },
  {
    id: 'privacy',
    name: '《运通达个人信息保护政策(货主版)》',
    url: '/pages/agreement/privacy'
  }
]);

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};

// 处理协议点击事件
const handleAgreementClick = (agreement: any) => {
  console.log('点击协议:', agreement.name);

  // 跳转到协议详情页面，传递协议ID参数
  uni.navigateTo({
    url: `/pages/login/agreement-detail?id=${agreement.id}`
  });
};
</script>

<style scoped lang="scss">
.agreement-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.status-bar {
  background-color: #fff;
}

.header-container {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.header {
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  position: relative;

  .back-icon {
    position: absolute;
    left: 30rpx;
    z-index: 10;

    .iconfont {
      font-size: 40rpx;
      color: #333;
    }
  }

  .title {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    padding: 0 80rpx; // 给左右留出空间避免与返回按钮重叠
  }
}

.content-container {
  flex: 1;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
}

.main-title {
  text-align: center;
  margin-bottom: 60rpx;

  .title-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
  }
}

.agreement-list {
  .agreement-item {
    padding: 30rpx 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f9fa;
    }

    .agreement-name {
      font-size: 30rpx;
      color: #333;
      line-height: 1.5;
      text-decoration: underline;
      text-decoration-color: #333;
      text-underline-offset: 4rpx;
      display: block;
    }
  }
}
</style>
