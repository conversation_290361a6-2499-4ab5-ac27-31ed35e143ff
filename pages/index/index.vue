<template>
  <view>
    <!-- 司机端 -->
    <view v-if="userType === 'driver'">
      <!-- 页面内容 -->
      <HomeDriver v-if="currentBarDriver === 0" />
      <DistributionDriver v-if="currentBarDriver === 1" />
      <TodayDriver v-if="currentBarDriver === 2" />
      <OrderList v-if="currentBarDriver === 3" />
      <MyDriver v-if="currentBarDriver === 4" />
      <!-- tabbar -->
      <up-tabbar :value="currentBarDriver" @change="tabBarChangeDriver" :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" inactiveColor="#707070" activeColor="#ea9518">
        <up-tabbar-item v-for="(item, index) in tabBarListDriver" :key="index" :text="item.text">
          <template #active-icon>
            <image class="u-page__item__slot-icon" :src="item.activeIcon"></image>
          </template>
          <template #inactive-icon>
            <image class="u-page__item__slot-icon" :src="item.icon"></image>
          </template>
        </up-tabbar-item>
      </up-tabbar>
    </view>
    <!-- 配货站 -->
    <view v-else-if="userType === 'station'">
      <!-- 页面内容 -->
      <Transport v-if="currentBarStation === 0" />
      <HomeDriver v-if="currentBarStation === 1" />
      <DistributionDriver v-if="currentBarStation === 2" />
      <TodayDriver v-if="currentBarStation === 3" />
      <StationMy v-if="currentBarStation === 4" />
      <!-- tabbar -->
      <up-tabbar :value="currentBarStation" @change="tabBarChangeStation" :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" inactiveColor="#707070" activeColor="#ea9518">
        <up-tabbar-item v-for="(item, index) in tabBarListStation" :key="index" :text="item.text">
          <template #active-icon>
            <image class="u-page__item__slot-icon" :src="item.activeIcon"></image>
          </template>
          <template #inactive-icon>
            <image class="u-page__item__slot-icon" :src="item.icon"></image>
          </template>
        </up-tabbar-item>
      </up-tabbar>
    </view>
    <!-- 货主端 -->
    <view v-else>
      <!-- 页面内容 -->
      <Transport v-if="currentBar === 0" />
      <Orders v-if="currentBar === 1" />
      <My v-if="currentBar === 2" />
      <!-- tabbar -->
      <up-tabbar :value="currentBar" @change="tabBarChange" :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" inactiveColor="#707070" activeColor="#ea9518">
        <up-tabbar-item v-for="(item, index) in tabBarList" :key="index" :text="item.text">
          <template #active-icon>
            <image class="u-page__item__slot-icon" :src="item.activeIcon"></image>
          </template>
          <template #inactive-icon>
            <image class="u-page__item__slot-icon" :src="item.icon"></image>
          </template>
        </up-tabbar-item>
      </up-tabbar>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getStoragetItem, setStoragetItem } from '@/utils/storage'
import { ws } from '@/utils/websocket'

const userType = ref(getStoragetItem('userType'))

// 页面加载时建立WebSocket连接
onLoad(() => {
  console.log('📱 进入首页，开始建立WebSocket连接')

  // 检查用户登录状态
  const isLoggedIn = ws.checkLoginStatus()

  if (isLoggedIn) {
    console.log('✅ 用户信息完整，建立WebSocket连接')
    ws.onUserLogin()
  } else {
    console.log('❌ 用户信息不完整，跳转到登录页')
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
})

// 货主端数据 start
import Transport from '@/pageCom/transport/transport.vue'
import Orders from '@/pageCom/orders/orders.vue'
import My from '@/pageCom/my/personal.vue'
const tabBarList = ref([
  {
    text: '发货',
    icon: '/static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo.png',
    activeIcon: '/static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo_selected.png',
  },
  {
    text: '订单',
    icon: '/static/images/tabbar/dingdanjihe.png',
    activeIcon: '/static/images/tabbar/dingdanjihe_selected.png',
  },
  {
    text: '我的',
    icon: '/static/images/tabbar/wode.png',
    activeIcon: '/static/images/tabbar/wode_selected.png',
  }
])
const currentBar = ref(0)
const tabBarChange = (val: any) => {
  currentBar.value = val
}
// 货主端数据 end

// 司机端数据 start
import HomeDriver from '@/pageComDriver/home/<USER>'
import DistributionDriver from '@/pageComDriver/distribution/distribution.vue'
import TodayDriver from '@/pageComDriver/today/today.vue'
import MyDriver from '@/pageComDriver/my/my.vue'
import OrderList from '@/pageComDriver/orders/orders.vue'
const tabBarListDriver = ref([
  {
    text: '首页',
    icon: '/static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo.png',
    activeIcon: '/static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo_selected.png',
  },
  {
    text: '配货大厅',
    icon: '/static/images/tabbar/dingdanjihe.png',
    activeIcon: '/static/images/tabbar/dingdanjihe_selected.png',
  },
  {
    text: '当天货源',
    icon: '/static/images/tabbar/dingdanjihe.png',
    activeIcon: '/static/images/tabbar/dingdanjihe_selected.png',
  },
  {
    text: '订单',
    icon: '/static/images/tabbar/dingdanjihe.png',
    activeIcon: '/static/images/tabbar/dingdanjihe_selected.png',
  },
  {
    text: '我的',
    icon: '/static/images/tabbar/wode.png',
    activeIcon: '/static/images/tabbar/wode_selected.png',
  }
])
const currentBarDriver = ref(0)
const tabBarChangeDriver = (val: any) => {
  currentBarDriver.value = val
}
// 司机端数据 end
// 配货站数据 start
import StationOrders from '@/pageComStation/orders/orders.vue'
import StationMy from '@/pageComStation/my/my.vue'
const tabBarListStation = ref([
  {
    text: '发货',
    icon: '/static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo.png',
    activeIcon: '/static/images/tabbar/cangpeitubiao_fahuohuanhuofahuo_selected.png',
  },
  {
    text: '首页',
    icon: '/static/images/tabbar/shouye.png',
    activeIcon: '/static/images/tabbar/shouye_selected.png',
  },
  {
    text: '配货大厅',
    icon: '/static/images/tabbar/dingdanjihe.png',
    activeIcon: '/static/images/tabbar/dingdanjihe_selected.png',
  },
  {
    text: '当天货源',
    icon: '/static/images/tabbar/dingdanjihe.png',
    activeIcon: '/static/images/tabbar/dingdanjihe_selected.png',
  },
  {
    text: '我的',
    icon: '/static/images/tabbar/wode.png',
    activeIcon: '/static/images/tabbar/wode_selected.png',
  }
])
const currentBarStation = ref(1)
const tabBarChangeStation = (val: any) => {
  currentBarStation.value = val
}
// 配货站数据 end
</script>
<style scoped lang="scss">
:deep(.u-tabbar) {
  .u-tabbar__content {
    .u-tabbar__content__item-wrapper {
      .u-tabbar-item {
        .u-tabbar-item__icon {
          .u-page__item__slot-icon {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .u-safe-bottom {
      height: env(safe-area-inset-bottom);
    }
  }
}
</style>
