<template>
  <view class="invoice">
    <view class="order-list">
      <scroll-view
          :scroll-y="true"
          refresher-background="#f3f3f3"
          :refresher-enabled="isLoadingTop"
          :refresher-triggered="trigger"
          @refresherrefresh="refresherrefresh"
          show-scrollbar="false"
      >
        <view class="order-item" v-for="item in orderList" :key="item.id" @tap="goPendingDetail(item)">
          <view class="position-price">
            <view class="position">
              <text class="start-position">{{ item.loadingAddressName || '未知' }}</text>
              <view class="iconfont icon-owner-a-xiangyoujiantou"></view>
              <text class="start-position">{{ item.unloadingAddressName || '未知' }}</text>
            </view>
            <view class="price">
              <text class="small">¥</text>
              <text class="big">{{ item.bidAmount || 0 }}</text>
              <text class="small">/趟</text>
            </view>
          </view>
          <view class="goods-info">
            <text class="name-info">{{ item.cargoName || '暂无信息' }}</text>
            <text class="time">{{ formatCreateTime(item.createTime) }}</text>
          </view>
<!--          <view class="price-tag">
            <text class="tag">一口价</text>
            <text class="tag">电话议价</text>
          </view>-->
<!--          <view class="message">
            <view class="message-left">
              <image src="../../../static/images/xiaofeiji.png"></image>
              <text class="name">{{ item.userName || '未知用户' }}</text>
              <text class="text">{{ item.message || '暂无消息' }}</text>
            </view>
            <up-icon name="arrow-right"></up-icon>
          </view>-->
          <view class="btn-region">
            <view class="btn-span">编辑</view>
            <view class="btn-span">删除</view>
          </view>
        </view>
      </scroll-view>
    </view>
    <uni-load-more iconType="auto" :status="loadStatus"></uni-load-more>
    <view class="order-tips">新发货源24小时后自动删除</view>
  </view>
</template>

<script setup lang="ts">
import { onReachBottom, onPageScroll } from '@dcloudio/uni-app';
import {onMounted, ref} from 'vue';
import { orderInfoPage } from '../../../api/transport';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// 订单列表
const orderList = ref<any[]>([]);
// 当前页码
const currentPage = ref(1);
// 每页数量
const pageSize = ref(10);
// 是否开启下拉刷新
const isLoadingTop = ref(true);
// 刷新状态
const trigger = ref(false);

// 获取订单列表
const fetchOrderList = async (page: number) => {
  try {
    const response: any = await orderInfoPage({
      page,
      limit: pageSize.value
    });
    uni.hideLoading();
    if (response.code === 700) {
      if (page === 1) {
        orderList.value = response.result || [];
      } else {
        orderList.value = [...orderList.value, ...(response.result || [])];
      }
      if (!response.result || response.result.length < pageSize.value) {
        loadStatus.value = 'no-more';
      } else {
        loadStatus.value = 'more';
      }
    }else {
      console.error('获取订单列表失败');
      loadStatus.value = 'more';
    }

  } catch (error) {
    console.error('获取订单列表失败:', error);
    loadStatus.value = 'more';
  }
};
// 格式化创建时间的函数
const formatCreateTime = (createTime: string | undefined) => {
  if (!createTime) {
    return '刚刚';
  }
  const diffMinutes = dayjs().diff(dayjs(createTime), 'minute');

  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes} 分钟前`;
  } else if (diffMinutes < 1440) {
    const diffHours = Math.floor(diffMinutes / 60);
    return `${diffHours} 小时前`;
  } else if (diffMinutes < 10080) {
    const diffDays = Math.floor(diffMinutes / 1440);
    return `${diffDays} 天前`;
  } else {
    return dayjs(createTime).format('YYYY-MM-DD');
  }
};
// 下拉刷新被触发
const refresherrefresh = async () => {
  currentPage.value = 1;
  trigger.value = true;
  await fetchOrderList(currentPage.value);
  trigger.value = false;
};
// 滚动到底部加载更多
const loadMore = async () => {
  if (loadStatus.value === 'no-more') return;
  loadStatus.value = 'loading';
  currentPage.value++;
  await fetchOrderList(currentPage.value);
};
onPageScroll((e) => {
	if (e.scrollTop == 0) {
		isLoadingTop.value = true;
	} else if (e.scrollTop > 0) {
		isLoadingTop.value = false;
	}
});

// more、loading、no-more
const loadStatus = ref('loading');

onReachBottom(() => {
  loadMore();

});
// 页面加载时获取第一页数据
onMounted(() => {
  fetchOrderList(currentPage.value);
});

// 详情页
const goPendingDetail = (item: any) => {
	uni.navigateTo({
		url: '/pageCom/transport/pending_order_detail',
    success: (res) => {
      // 使用 eventChannel 发送数据
      res.eventChannel.emit('orderId', { id: item.id });
    }
	});
};
</script>

<style scoped lang="scss">
.invoice {
	min-height: 0;
	width: 100%;
	height: 100%;
}
.order-list {
	width: 100%;
	height: 100%;
	// background-color: #fff;
	border-bottom-left-radius: 10rpx;
	border-bottom-right-radius: 10rpx;
	// padding: 0rpx 32rpx;
	// box-sizing: border-box;

	.order-item {
		padding: 32rpx 32rpx;
		box-sizing: border-box;
		// border-bottom: 2rpx solid $uni-border-color;
		margin-top: 20rpx;
		background-color: #fff;
		&:last-child {
			border-bottom: none;
		}
		.position-price {
			width: 100%;
			display: flex;
			align-items: flex-end;
			justify-content: space-between;
			.position {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.start-position {
					font-size: 32rpx;
					font-weight: 600;
				}
				.iconfont {
					font-weight: 700;
					margin: 0 20rpx;
				}
			}
			.price {
				width: 200rpx;
				color: #ff8c00;
				text-align: right;
				.small {
					font-size: 28rpx;
				}
				.big {
					font-size: 36rpx;
					font-weight: 700;
				}
			}
		}
		.goods-info {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			margin-top: 8rpx;
			.name-info {
				flex: 1;
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}
			.time {
				width: 150rpx;
				font-size: $uni-font-size-base;
				color: $uni-text-color-grey;
				text-align: right;
			}
		}
		.price-tag {
			display: flex;
			align-items: center;
			margin-top: 8rpx;
			.tag {
				padding: 2rpx 6rpx;
				box-sizing: border-box;
				border: 2rpx solid #ff8c00;
				color: #ff8c00;
				font-size: $uni-font-size-sm;
				border-radius: 2rpx;
				margin-right: 12rpx;
			}
		}
		.message {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 10rpx;
			.message-left {
				flex: 1;
				display: flex;
				align-items: center;
				image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 8rpx;
				}
				text {
					font-size: $uni-font-size-base;
					color: $uni-text-color-placeholder;
				}
				.name {
					color: #ff8c00;
				}
			}
			:deep(.u-icon--right) {
				width: 30rpx;
			}
		}
		.btn-region {
			width: 100%;
			display: flex;
			justify-content: flex-end;
			margin-top: 14rpx;
			.btn-span {
				padding: 10rpx 24rpx;
				box-sizing: border-box;
				margin-left: 20rpx;
				border: 2rpx solid $uni-border-color;
				border-radius: 10rpx;
			}
		}
	}
}
.order-tips {
	text-align: center;
	margin-top: 60rpx;
	font-size: $uni-font-size-base-30;
	color: $uni-text-color-placeholder;
	padding-bottom: 60rpx;
}
</style>
