<template>
	<view class="history">
		<view class="order-list">
			<scroll-view
				:scroll-y="true"
				refresher-background="#f3f3f3"
				:refresher-enabled="isLoadingTop"
				:refresher-triggered="trigger"
				@refresherrefresh="refresherrefresh"
				show-scrollbar="false"
			>
				<view class="order-item" v-for="item in allOrders" :key="item" @tap="goHistoryDetail(item)">
					<view class="status-tag">已成交下架</view>
					<view class="address">
						<text>{{ item.loadingAddressName }}</text>
						<view class="iconfont icon-owner-a-xiangy<PERSON><PERSON><PERSON><PERSON>"></view>
						<text>{{ item.unloadingAddressName }}</text>
					</view>
					<view class="info">
<!--
						<view class="tag">一口价</view>
-->
						<text>{{ item.vehicleLength }}米 {{ item.vehicleType }} {{ item.maxWeight }}吨{{ item.maxVolume }}方/{{ item.cargoName }}</text>
					</view>
					<view class="handle-box">
						<view class="time">09-26 16:08</view>
						<view class="btn-region">
<!--
							<view class="btn-span">存为常发</view>
-->
							<view class="btn-span">再来一单</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<uni-load-more iconType="auto" :status="loadStatus"></uni-load-more>
	</view>
</template>

<script setup lang="ts">
import { onReachBottom, onPageScroll } from '@dcloudio/uni-app';
import {computed, onMounted, ref} from 'vue';
import {orderInfoPage} from "../../../api/transport";
import UniLoadMore from "../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";
// 是否开启下来刷新
const isLoadingTop = ref(true);
// 刷新状态
const trigger = ref(true);
// 下拉刷新被触发
const refresherrefresh = (e: any) => {
	trigger.value = true;
  currentPage.value = 1;
  try {
    fetchOrders();
  } catch (error) {
    console.error('刷新订单列表失败:', error);
  } finally {
    trigger.value = false;
  }
};
onPageScroll((e) => {
	if (e.scrollTop == 0) {
		isLoadingTop.value = true;
	} else if (e.scrollTop > 0) {
		isLoadingTop.value = false;
	}
});

// more、loading、no-more
const loadStatus = ref('loading');

// 当前页码
const currentPage = ref(1);
// 每页数量
const pageSize = ref(10);
// 所有订单列表
const allOrders = ref<any[]>([]);
// 计算属性，筛选出已完成订单
const completedOrders = computed(() => allOrders.value.filter(order => order.status === 1));

// 获取订单列表
const fetchOrders = async () => {
  try {
    const response:any = await orderInfoPage({
      page: currentPage.value,
      limit: pageSize.value
    });
    if (response.code === 700) {
      if (currentPage.value === 1) {
        allOrders.value = response.result || [];
      } else {
        allOrders.value = [...allOrders.value, ...(response.result || [])];
      }
      if (!response.result || response.result.length < pageSize.value) {
        loadStatus.value = 'no-more';
      } else {
        loadStatus.value = 'more';
      }
    } else {
      console.error('获取订单列表失败，错误码:', response.code);
      loadStatus.value = 'more';
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    loadStatus.value = 'more';
  }
};
onReachBottom(async () => {
  if (loadStatus.value === 'more') {
    currentPage.value++;
    await fetchOrders();
  }
});
// 详情页
const goHistoryDetail = (item: any) => {
	uni.navigateTo({
		url: '/pageCom/transport/history_detail',
    success: (res) => {
      // 使用 eventChannel 发送数据
      res.eventChannel.emit('orderId', { id: item.id });
    }
	});
};

// 存为常发
const saveAsRegular = (item: any) => {
  // 实现存为常发逻辑
  console.log('存为常发:', item);
};

// 再来一单
const orderAgain = (item: any) => {
  // 实现再来一单逻辑
  console.log('再来一单:', item);
};
onMounted(async () => {
  await fetchOrders();
});
</script>

<style scoped lang="scss">
.history {
	padding-bottom: 60rpx;
}
.order-list {
	width: 100%;
	.order-item {
		width: 100%;
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		background-color: #fff;
		position: relative;
		margin-top: 20rpx;
		border-radius: 10rpx;
		.status-tag {
			background-color: #eeeeee;
			color: $uni-text-color-grey;
			position: absolute;
			right: 0;
			top: 0;
			padding: 2rpx 20rpx;
			box-sizing: border-box;
			border-bottom-left-radius: 10rpx;
			border-top-right-radius: 10rpx;
			font-size: $uni-font-size-sm;
		}
		.address {
			display: flex;
			align-items: center;
			padding-top: 32rpx;
			text {
				font-size: 32rpx;
				font-weight: 600;
			}
			.iconfont {
				font-weight: 700;
				margin: 0 20rpx;
			}
		}
		.info {
			display: flex;
			align-items: center;
			padding: 20rpx 0rpx 30rpx 0rpx;
			border-bottom: 2rpx solid $uni-text-color-grey;
			.tag {
				padding: 0rpx 4rpx;
				box-sizing: border-box;
				color: $uni-text-color-light;
				border: 2rpx solid $uni-text-color-light;
				border-radius: 6rpx;
			}
			text {
				flex: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				-o-text-overflow: ellipsis;
			}
		}
		.handle-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0rpx 32rpx 0rpx;
			.time {
				font-size: $uni-font-size-base;
				color: $uni-text-color-grey;
			}
			.btn-region {
				display: flex;
				.btn-span {
					padding: 10rpx 24rpx;
					box-sizing: border-box;
					margin-left: 20rpx;
					border: 2rpx solid $uni-border-color;
					border-radius: 10rpx;
					margin-left: 20rpx;
				}
			}
		}
	}
}
</style>
