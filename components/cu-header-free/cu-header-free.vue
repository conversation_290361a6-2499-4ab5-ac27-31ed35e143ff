<template>
  <view>
    <view class="header-bg-flag" v-if="!isScroll"></view>
    <view class="header-bg" :class="isScroll ? '' : 'fixed-top'">
      <view class="header-container" :style="{ top: systemInfo.safeAreaInsets.top * 2 - 1 + 'rpx' }">
        <view class="header-container-left">
          <slot name="leftContainer">
            <up-icon name="arrow-left" size="34rpx" @tap="goBack"></up-icon>
          </slot>
        </view>
        <view class="header-container-right">
          <slot name="rightContainer"></slot>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
const props = defineProps({
  // 组件标题栏高度
  titleBarHeight: {
    type: Number,
    default: 80
  },
  titleBarBg: {
    type: String,
    default: () => {
      // return 'linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4)';
      return '';
    }
  },
  isScroll: {
    type: Boolean,
    default: () => {
      return false
    }
  }
});
const systemInfo: any = uni.getSystemInfoSync();
const titleBarHeightActual = computed(() => {
  return systemInfo.safeAreaInsets.top * 2 + props.titleBarHeight + 'rpx';
});
const titleBarBgActual = computed(() => {
  return props.titleBarBg;
});
// const headerTop = systemInfo.safeAreaInsets.top * 2 + 'rpx'
const goBack = () => {
  uni.navigateBack();
}
</script>

<style lang="scss" scoped>
.fixed-top {
  position: fixed !important;
  left: 0 !important;
  // top: v-bind(headerTop) !important;
  z-index: 9 !important;
}

.header-bg-flag {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(titleBarHeightActual);
  background-color: #f5f5f5;
  z-index: 8;
}

.header-bg {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(titleBarHeightActual);
  background: v-bind(titleBarBgActual);
  // background-color: rgb(245, 245, 245);
  z-index: 1;

  .header-container {
    position: absolute;
    left: 0rpx;
    width: 100%;
    height: 80rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0rpx 28rpx;
    box-sizing: border-box;
  }
}
</style>
