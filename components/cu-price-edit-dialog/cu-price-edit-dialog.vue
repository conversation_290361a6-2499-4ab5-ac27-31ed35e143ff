<template>
  <uni-popup ref="popup" type="bottom" :mask-click="false" @change="change">
    <view class="price-edit-dialog">
      <!-- 头部 -->
      <view class="dialog-header">
        <view class="header-btn" @click="handleCancel">
          <text class="cancel-text">取消</text>
        </view>
        <view class="header-title">
          <text class="title-text">我的出价</text>
        </view>
        <view class="header-btn" @click="handleConfirm">
          <text class="confirm-text">确定</text>
        </view>
      </view>

      <!-- 运力提示 -->
      <view class="transport-tips">
        <view class="tips-icon">
          <text class="iconfont icon-owner-tishi"></text>
        </view>
        <text class="tips-text">运力紧张，当前运价较高，发货前请确认</text>
      </view>

      <!-- 价格输入区域 -->
      <view class="price-input-section">
        <view class="input-container">
          <text class="input-label">必填，请输入总价</text>
          <view class="price-input-wrapper">
            <input
              class="price-input"
              type="number"
              :placeholder="inputPlaceholder"
              v-model="currentPrice"
              @input="handlePriceInput"
            />
            <text class="currency">元/</text>

            <!-- 单位选择器 -->
            <view class="unit-selector">
              <view
                class="unit-item"
                :class="{ 'unit-active': item.selected }"
                v-for="(item, index) in unitList"
                :key="item.id"
                @click="selectUnit(index)"
              >
                <text class="unit-text">{{ item.text }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 总运费提示 -->
        <view class="total-tips" v-if="currentPrice">
          <text class="tips-text">总运费{{ currentPrice }}元。</text>
        </view>
      </view>

      <!-- 订金输入区域 -->
      <view class="deposit-section" v-if="showDeposit">
        <view class="section-title">
          <text>订金（可选）</text>
        </view>
        <view class="deposit-input-wrapper">
          <input
            class="deposit-input"
            type="number"
            placeholder="请输入订金金额"
            v-model="currentDeposit"
            @input="handleDepositInput"
          />
          <text class="currency">元</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

// 定义props
const props = defineProps({
  // 是否显示订金输入
  showDeposit: {
    type: Boolean,
    default: true,
  },
  // 初始价格
  initialPrice: {
    type: [String, Number],
    default: '',
  },
  // 初始订金
  initialDeposit: {
    type: [String, Number],
    default: '',
  },
  // 初始选中的单位文本
  initialUnitText: {
    type: String,
    default: '趟',
  },
  // 单位列表
  units: {
    type: Array,
    default: () => [
      { id: 1, text: '趟', selected: true },
      { id: 2, text: '吨', selected: false },
      { id: 3, text: '方', selected: false },
    ],
  },
})

// 定义emit事件
const emit = defineEmits(['cancel', 'confirm'])

// 弹窗引用
const popup = ref(null)

// 单位列表
const unitList = ref([
  { id: 1, text: '趟', selected: true },
  { id: 2, text: '吨', selected: false },
  { id: 3, text: '方', selected: false },
])

// 当前价格
const currentPrice = ref('')
// 当前订金
const currentDeposit = ref('')

// 监听 props 变化，实时更新数据
watch(
  () => [props.initialPrice, props.initialDeposit, props.initialUnitText],
  () => {
    console.log('Props 发生变化，重新初始化数据')
    initData()
  },
  { immediate: false }
)

// 输入提示文字
const inputPlaceholder = computed(() => {
  const selectedUnit = unitList.value.find((item) => item.selected)
  return `必填，请输入总价`
})

// 初始化数据
const initData = () => {
  console.log('弹窗初始化数据 - props:', {
    initialPrice: props.initialPrice,
    initialDeposit: props.initialDeposit,
    initialUnitText: props.initialUnitText
  })

  // 设置单位列表
  if (props.units && props.units.length > 0) {
    unitList.value = props.units.map(unit => ({ ...unit }))
  }

  // 设置初始选中单位（通过中文文本匹配）
  unitList.value.forEach((item) => {
    item.selected = item.text === props.initialUnitText
  })

  // 设置初始价格和订金
  currentPrice.value = String(props.initialPrice || '')
  currentDeposit.value = String(props.initialDeposit || '')

  console.log('弹窗初始化完成 - 当前数据:', {
    currentPrice: currentPrice.value,
    currentDeposit: currentDeposit.value,
    selectedUnit: unitList.value.find(item => item.selected)?.text
  })
}

// 选择单位
const selectUnit = (index: number) => {
  unitList.value.forEach((item, i) => {
    item.selected = i === index
  })
}

// 处理价格输入
const handlePriceInput = (e: any) => {
  currentPrice.value = e.detail?.value || e.target?.value || ''
}

// 处理订金输入
const handleDepositInput = (e: any) => {
  currentDeposit.value = e.detail?.value || e.target?.value || ''
}

// 打开弹窗
const open = () => {
  initData()
  popup.value?.open()
}

// 关闭弹窗
const close = () => {
  popup.value?.close()
}

// 弹窗状态变化
const change = (e: any) => {
  // 可以在这里添加状态变化处理
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  close()
}

// 处理确认
const handleConfirm = () => {
  // 验证价格
  if (!currentPrice.value || Number(currentPrice.value) <= 0) {
    uni.showToast({
      title: '请输入有效的价格',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 获取选中的单位
  const selectedUnit = unitList.value.find((item) => item.selected)

  // 返回数据
  const result = {
    price: Number(currentPrice.value),
    deposit: currentDeposit.value ? Number(currentDeposit.value) : 0,
    unitId: selectedUnit?.id || 1,
    unitText: selectedUnit?.text || '趟',
  }

  emit('confirm', result)
  close()
}

// 暴露方法给父组件
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.price-edit-dialog {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);

  // 头部
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 2rpx solid #f5f5f5;

    .header-btn {
      min-width: 120rpx;

      &:last-child {
        text-align: right;
      }

      .cancel-text {
        font-size: 32rpx;
        color: #666666;
      }

      .confirm-text {
        font-size: 32rpx;
        color: #ff8c00;
        font-weight: 500;
      }
    }

    .header-title {
      flex: 1;
      text-align: center;

      .title-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #333333;
      }
    }
  }

  // 运力提示
  .transport-tips {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    background-color: #fff7e6;
    margin: 0 32rpx;
    margin-top: 24rpx;
    border-radius: 12rpx;

    .tips-icon {
      margin-right: 12rpx;

      .iconfont {
        font-size: 32rpx;
        color: #ff8c00;
      }
    }

    .tips-text {
      font-size: 28rpx;
      color: #ff8c00;
      flex: 1;
    }
  }

  // 价格输入区域
  .price-input-section {
    padding: 32rpx;

    .input-container {
      border: 4rpx solid #ff8c00;
      border-radius: 12rpx;
      padding: 24rpx;

      .input-label {
        font-size: 28rpx;
        color: #999999;
        margin-bottom: 16rpx;
        display: block;
      }

      .price-input-wrapper {
        display: flex;
        align-items: center;

        .price-input {
          flex: 1;
          font-size: 36rpx;
          color: #333333;
          border: none;
          outline: none;
          background: transparent;
        }

        .currency {
          font-size: 32rpx;
          color: #333333;
          margin-right: 16rpx;
        }

        .unit-selector {
          display: flex;
          align-items: center;
          gap: 8rpx;

          .unit-item {
            padding: 12rpx 24rpx;
            border-radius: 8rpx;
            border: 2rpx solid #e5e5e5;
            background-color: #f8f8f8;

            .unit-text {
              font-size: 28rpx;
              color: #666666;
            }

            &.unit-active {
              background-color: #ff8c00;
              border-color: #ff8c00;

              .unit-text {
                color: #ffffff;
              }
            }
          }
        }
      }
    }

    .total-tips {
      margin-top: 16rpx;

      .tips-text {
        font-size: 28rpx;
        color: #ff8c00;
      }
    }
  }

  // 订金区域
  .deposit-section {
    padding: 0 32rpx 32rpx;

    .section-title {
      margin-bottom: 16rpx;

      text {
        font-size: 32rpx;
        color: #333333;
        font-weight: 500;
      }
    }

    .deposit-input-wrapper {
      display: flex;
      align-items: center;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx;
      padding: 24rpx;

      .deposit-input {
        flex: 1;
        font-size: 32rpx;
        color: #333333;
        border: none;
        outline: none;
        background: transparent;
      }

      .currency {
        font-size: 28rpx;
        color: #666666;
      }
    }
  }
}
</style>
