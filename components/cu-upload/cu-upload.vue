<template>
  <view
    class="image-uploader"
    :class="{ 'disabled': !editable }"
    :style="containerStyle"
    @click="handleClick"
  >
    <!-- 已上传图片预览 -->
    <image v-if="modelValue" :src="modelValue" mode="aspectFit" class="preview-image"></image>

    <!-- 未上传时的占位内容 -->
    <view v-else class="placeholder-content">
      <slot name="placeholder">
        <view class="upload-icon"></view>
        <text class="upload-text">{{ getPlaceholderText }}</text>
      </slot>
    </view>

    <!-- 不可编辑状态的遮罩 -->
    <view v-if="!editable && modelValue" class="disabled-mask">
      <view class="disabled-content">
        <text class="disabled-text">点击查看大图</text>
        <text class="disabled-icon">🔍</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { upload } from '@/api/common';

const props = defineProps({
  // v-model值，存储图片路径
  modelValue: {
    type: String,
    default: ''
  },
  // 组件宽度
  width: {
    type: String,
    default: '100%'
  },
  // 组件高度
  height: {
    type: String,
    default: '400rpx'
  },
  // 上传按钮文本
  placeholder: {
    type: String,
    default: '点击上传图片'
  },
  // 背景图片
  backgroundImage: {
    type: String,
    default: ''
  },
  // 背景颜色，当没有背景图时使用
  backgroundColor: {
    type: String,
    default: '#ffffff'
  },
  // 选择图片的来源类型
  sourceType: {
    type: Array,
    default: () => ['album', 'camera']
  },
  // 是否压缩图片
  sizeType: {
    type: Array,
    default: () => ['original', 'compressed']
  },
  // 最大可选择图片数量
  count: {
    type: Number,
    default: 1
  },
  // 是否可编辑（可上传图片）
  editable: {
    type: Boolean,
    default: true
  }
});

// 容器样式计算属性
const containerStyle = computed(() => {
  return {
    width: props.width,
    height: props.height,
    backgroundImage: props.backgroundImage ? `url(${props.backgroundImage})` : 'none',
    backgroundColor: props.backgroundColor || '#ffffff'
  };
});

// 占位符文本计算属性
const getPlaceholderText = computed(() => {
  if (!props.editable) {
    return '暂无图片'
  }
  return props.placeholder || '点击上传图片'
});

const emit = defineEmits(['update:modelValue', 'success', 'fail']);

// 统一的点击处理函数
const handleClick = () => {
  if (props.editable) {
    // 可编辑状态：选择上传图片
    handleChooseImage()
  } else if (props.modelValue) {
    // 不可编辑状态且有图片：预览大图
    handlePreviewImage()
  }
}

// 预览图片
const handlePreviewImage = () => {
  if (!props.modelValue) return

  uni.previewImage({
    urls: [props.modelValue],
    current: props.modelValue,
    success: () => {
      console.log('图片预览成功')
    },
    fail: (err) => {
      console.error('图片预览失败:', err)
      uni.showToast({
        title: '图片预览失败',
        icon: 'none',
        duration: 2000
      })
    }
  })
}

// 选择图片
const handleChooseImage = () => {
  uni.chooseImage({
    count: props.count,
    sizeType: props.sizeType,
    sourceType: props.sourceType,
    success: async (res) => {
      try {
        // 保存本地路径，无论上传成功与否都有备用值
        const tempFilePath = res.tempFilePaths[0];

        // 创建FormData并直接传递文件路径信息
        const uploadData = {
          tempFilePaths: res.tempFilePaths,
          tempFiles: res.tempFiles
        };

        // 使用修改后的upload函数上传
        const result = await upload(uploadData);
        uni.hideLoading();
        // 检查上传结果，仅当确实获取到服务器URL时才使用
        if (result && result.successful) {
          emit('update:modelValue', result.result.url);
          // 触发成功事件
          emit('success', result?.result || null);
        } else {
          emit('update:modelValue', tempFilePath);
        }
      } catch (error) {
        // 发生错误时，也使用本地临时文件路径
        const tempFilePath = res.tempFilePaths[0];
        emit('update:modelValue', tempFilePath);

        uni.showToast({
          title: '图片上传处理异常',
          icon: 'none'
        });

        // 触发失败事件
        emit('fail', error);
      }
    },
    fail: (err) => {
      // 触发失败事件
      emit('fail', err);
    }
  });
};
</script>

<style lang="scss" scoped>
.image-uploader {
  position: relative;
  overflow: hidden;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #ddd;
  background-size: cover;
  background-position: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 30rpx;
    box-sizing: border-box;

    .upload-icon {
      width: 100rpx;
      height: 100rpx;
      border: 4rpx dashed #ddd;
      border-radius: 12rpx;
      margin-bottom: 20rpx;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        background-color: #ddd;
      }

      &::before {
        width: 4rpx;
        height: 50rpx;
        top: 25rpx;
        left: 48rpx;
      }

      &::after {
        width: 50rpx;
        height: 4rpx;
        top: 48rpx;
        left: 25rpx;
      }
    }

    .upload-text {
      font-size: 28rpx;
      color: #999;
      text-align: center;
    }
  }

  &:active {
    opacity: 0.8;
  }

  // 禁用状态样式
  &.disabled {
    .placeholder-content {
      opacity: 0.6;

      .upload-icon {
        border-color: #ccc;

        &::before,
        &::after {
          background-color: #ccc;
        }
      }

      .upload-text {
        color: #ccc;
      }
    }

    // 有图片时可以点击预览，所以不设置 cursor: not-allowed
    &:not(:has(.disabled-mask)) {
      cursor: not-allowed;
    }
  }

  // 禁用状态遮罩
  .disabled-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16rpx;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.5);
    }

    .disabled-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;

      .disabled-text {
        color: #fff;
        font-size: 24rpx;
        background-color: rgba(0, 0, 0, 0.7);
        padding: 12rpx 20rpx;
        border-radius: 12rpx;
        text-align: center;
      }

      .disabled-icon {
        font-size: 32rpx;
        opacity: 0.8;
      }
    }
  }
}
</style>