<template>
	<view>
		<view class="header-container" :style="{ height: safeAreaInsets.top * 2 + 'rpx' }"></view>
		<view class="cu-header" :style="{ top: safeAreaInsets.top * 2 - 1 + 'rpx' }">
			<view class="left-btn" @tap="cancel">
				<slot name="leftBtn">取消</slot>
			</view>
			<view class="title">
				<slot name="title">装货地址</slot>
			</view>
			<view class="right-btn">
				<slot name="rightBtn">
					<view class="iconfont icon-owner-dituxuandian"></view>
					<view class="text">地图选点</view>
				</slot>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
const { safeAreaInsets } = uni.getSystemInfoSync();
const cancel = () => {
	uni.navigateBack();
};
</script>

<style scoped lang="scss">
.header-container {
	width: 100%;
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	background-color: $uni-bg-color;
	z-index: 1;
}

.cu-header {
	position: fixed;
	width: 100%;
	height: 80rpx;
	padding: 0px 20rpx;
	box-sizing: border-box;
	display: flex;
	// align-items: flex-start;
	align-items: center;
	justify-content: space-between;
	background-color: $uni-bg-color;
	z-index: 1;

	.left-btn {
		width: 100rpx;
		font-size: $uni-font-size-lg;
		color: $uni-text-color;
	}

	.title {
		font-size: $uni-font-size-big;
		font-weight: bold;
	}

	.right-btn {
		width: 100rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.iconfont {
			font-size: $uni-font-size-lg;
		}

		.text {
			font-size: $uni-font-size-sm;
			color: $uni-text-color;
		}
	}
}
</style>
