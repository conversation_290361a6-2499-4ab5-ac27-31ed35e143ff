<template>
	<view>
		<view class="cell-row">
			<view class="cell-left">
				<slot name="label">车辆</slot>
			</view>
			<view class="cell-right">
				<view class="show">
					<slot name="showText">整车6.8米 高栏</slot>
				</view>
				<view class="handle" v-if="isHandle">
					<text>
						<slot name="handleText">去支付</slot>
					</text>
					<up-icon name="arrow-right" size="24rpx"></up-icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
const props = defineProps({
	isHandle: {
		type: Boolean,
		default: false
	}
});
</script>

<style scoped lang="scss">
.cell-row {
	width: 100%;
	display: flex;
	justify-content: space-between;
	padding: 10rpx 0rpx;
	box-sizing: border-box;
	.cell-left {
		width: 200rpx;
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}
	.cell-right {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		.show {
			font-size: $uni-font-size-base;
			color: $uni-text-color-grey;
			text-align: right;
		}
		.handle {
			display: flex;
			align-items: center;
			margin-left: 20rpx;
			text {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}
		}
	}
}
</style>
