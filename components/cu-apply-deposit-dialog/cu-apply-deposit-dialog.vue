<template>
  <uni-popup ref="popup" type="center" :mask-click="false" @change="change">
    <view class="apply-deposit-dialog">
      <!-- 标题区域 -->
      <view class="dialog-header">
        <text class="dialog-title">{{ dialogTexts.title }}</text>
      </view>
      
      <!-- 主要内容区域 -->
      <view class="dialog-content">
        <!-- 申请原因输入框 -->
        <view class="form-item">
          <view class="form-label">申请原因</view>
          <textarea
            v-model="formData.amountRemarks"
            class="form-textarea"
            :placeholder="dialogTexts.placeholder"
            :maxlength="200"
            :show-confirm-bar="false"
            :auto-height="true"
            :cursor-spacing="20"
          />
          <view class="char-count">{{ formData.amountRemarks.length }}/200</view>
        </view>
        
        <!-- 图片上传区域 -->
        <view class="form-item">
          <view class="form-label">相关图片</view>
          <view class="upload-container">
            <!-- 图片列表和上传按钮在同一行 -->
            <view class="image-upload-row">
              <!-- 已上传的图片列表 -->
              <view
                class="image-item"
                v-for="(image, index) in formData.amountImages"
                :key="index"
              >
                <view class="image-wrapper">
                  <image
                    :src="image"
                    mode="aspectFill"
                    class="preview-image"
                    @click="previewImage(image, index)"
                  />
                  <view class="delete-btn" @click="deleteImage(index)">
                    <text class="delete-icon">×</text>
                  </view>
                </view>
              </view>

              <!-- 上传按钮 -->
              <view
                v-if="formData.amountImages.length < 3"
                class="upload-btn"
                @click="chooseImage"
              >
                <view class="upload-placeholder">
                  <view class="upload-icon">📷</view>
                  <text class="upload-text">上传图片</text>
                </view>
              </view>
            </view>
          </view>
          <view class="upload-tip">{{ dialogTexts.uploadTip }}</view>
        </view>
      </view>
      
      <!-- 按钮区域 -->
      <view class="dialog-footer">
        <view class="btn-cancel" @click="handleCancel">
          <text>取消</text>
        </view>
        <view
          class="btn-confirm"
          :class="{ 'disabled': !canSubmit }"
          @click="handleConfirm"
        >
          <text>{{ dialogTexts.confirmText }}</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { upload } from '@/api/common'

// 定义props
const props = defineProps({
  // 调用者类型：owner-货主，driver-司机
  type: {
    type: String,
    default: 'driver', // 默认为司机
    validator: (value) => ['owner', 'driver'].includes(value)
  }
})

// 定义emit事件
const emit = defineEmits(['cancel', 'confirm', 'upload-success', 'upload-fail'])

// 弹窗引用
const popup = ref(null)

// 表单数据
const formData = ref({
  amountRemarks: '',
  amountImages: [] // 改为数组存储多个图片URL
})

// 计算是否可以提交
const canSubmit = computed(() => {
  return formData.value.amountRemarks.trim().length > 0
})

// 动态文案
const dialogTexts = computed(() => {
  if (props.type === 'owner') {
    return {
      title: '申请扣订金',
      placeholder: '请输入申请扣订金的原因',
      confirmText: '确认申请',
      uploadTip: '请上传相关证明图片，最多3张（可选）'
    }
  } else {
    return {
      title: '申请退订金',
      placeholder: '请输入申请退订金的原因',
      confirmText: '确认申请',
      uploadTip: '请上传相关证明图片，最多3张（可选）'
    }
  }
})

// 打开弹窗方法
const open = () => {
  // 重置表单数据
  formData.value = {
    amountRemarks: '',
    amountImages: []
  }
  popup.value?.open()
}

// 关闭弹窗方法
const close = () => {
  popup.value?.close()
}

// 弹窗状态变化
const change = (e) => {
  // 可以在这里添加弹窗状态变化的处理逻辑
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  close()
}

// 处理确认按钮点击
const handleConfirm = () => {
  if (!canSubmit.value) {
    uni.showToast({
      title: '请输入申请原因',
      icon: 'none'
    })
    return
  }
  
  emit('confirm', {
    amountRemarks: formData.value.amountRemarks.trim(),
    amountImages: formData.value.amountImages.join(',') // 将数组转为逗号分隔的字符串
  })
  close()
}

// 选择图片
const chooseImage = () => {
  const remainingCount = 3 - formData.value.amountImages.length

  uni.chooseImage({
    count: remainingCount,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '上传中...',
          mask: true
        })

        // 逐个上传图片
        for (let i = 0; i < res.tempFilePaths.length; i++) {
          const tempFilePath = res.tempFilePaths[i]

          try {
            // 创建上传数据
            const uploadData = {
              tempFilePaths: [tempFilePath],
              tempFiles: [res.tempFiles[i]]
            }

            // 上传图片
            const result = await upload(uploadData)

            if (result && result.successful) {
              // 上传成功，添加到图片列表
              formData.value.amountImages.push(result.result.url)
              emit('upload-success', result.result)
            } else {
              // 上传失败，使用本地路径
              formData.value.amountImages.push(tempFilePath)
            }
          } catch (error) {
            console.error('图片上传失败:', error)
            // 上传失败，使用本地路径
            formData.value.amountImages.push(tempFilePath)
            emit('upload-fail', error)
          }
        }

        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('选择图片失败:', error)
        uni.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      emit('upload-fail', error)
    }
  })
}

// 删除图片
const deleteImage = (index) => {
  formData.value.amountImages.splice(index, 1)
}

// 预览图片
const previewImage = (currentImage, currentIndex) => {
  uni.previewImage({
    urls: formData.value.amountImages,
    current: currentIndex,
    success: () => {
      console.log('图片预览成功')
    },
    fail: (err) => {
      console.error('图片预览失败:', err)
      uni.showToast({
        title: '图片预览失败',
        icon: 'none'
      })
    }
  })
}

// 将方法暴露给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.apply-deposit-dialog {
  width: 640rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  
  // 标题区域
  .dialog-header {
    padding: 30rpx 30rpx 10rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .dialog-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  // 内容区域
  .dialog-content {
    padding: 30rpx;
    max-height: 600rpx;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .form-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        font-weight: 500;
      }
      
      .form-textarea {
        width: 100%;
        min-height: 120rpx;
        padding: 20rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #333;
        background-color: #fafafa;
        box-sizing: border-box;
        line-height: 1.5;
        
        &:focus {
          border-color: #e84138;
          background-color: #fff;
        }
      }
      
      .char-count {
        text-align: right;
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }
      
      .upload-container {
        .image-upload-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;
          align-items: center;

          .image-item {
            .image-wrapper {
              position: relative;
              width: 160rpx;
              height: 160rpx;
              border-radius: 12rpx;
              overflow: hidden;
              background-color: #f8f9fa;
              border: 2rpx solid #e9ecef;
              padding: 4rpx;
              box-sizing: border-box;

              .preview-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8rpx;
              }

              .delete-btn {
                position: absolute;
                top: -6rpx;
                right: -6rpx;
                width: 32rpx;
                height: 32rpx;
                background-color: #ff4757;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
                border: 2rpx solid #fff;

                .delete-icon {
                  color: #fff;
                  font-size: 20rpx;
                  font-weight: bold;
                  line-height: 1;
                }
              }
            }
          }

          .upload-btn {
            width: 160rpx;
            height: 160rpx;
            border: 2rpx dashed #ddd;
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fafafa;
            transition: all 0.3s ease;

            &:active {
              background-color: #f0f0f0;
              border-color: #ccc;
              transform: scale(0.98);
            }
          }
        }
      }
      
      .upload-tip {
        font-size: 24rpx;
        color: #999;
        margin-top: 12rpx;
      }
    }
  }
  
  // 按钮区域
  .dialog-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .btn-cancel,
    .btn-confirm {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:active {
        background-color: #f5f5f5;
      }
    }
    
    .btn-cancel {
      color: #666;
      border-right: 1rpx solid #f0f0f0;
    }
    
    .btn-confirm {
      color: #e84138;
      font-weight: 500;
      
      &.disabled {
        color: #ccc;
        cursor: not-allowed;
        
        &:active {
          background-color: transparent;
        }
      }
    }
  }
}

// 上传占位符样式
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  
  .upload-icon {
    font-size: 40rpx;
    margin-bottom: 8rpx;
  }
  
  .upload-text {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
