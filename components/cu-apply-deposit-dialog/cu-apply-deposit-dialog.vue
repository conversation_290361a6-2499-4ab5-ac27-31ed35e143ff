<template>
  <uni-popup ref="popup" type="center" :mask-click="false" @change="change">
    <view class="apply-deposit-dialog">
      <!-- 标题区域 -->
      <view class="dialog-header">
        <text class="dialog-title">申请退订金</text>
      </view>
      
      <!-- 主要内容区域 -->
      <view class="dialog-content">
        <!-- 申请原因输入框 -->
        <view class="form-item">
          <view class="form-label">申请原因</view>
          <textarea
            v-model="formData.amountRemarks"
            class="form-textarea"
            placeholder="请输入申请退订金的原因"
            :maxlength="200"
            :show-confirm-bar="false"
            :auto-height="true"
            :cursor-spacing="20"
          />
          <view class="char-count">{{ formData.amountRemarks.length }}/200</view>
        </view>
        
        <!-- 图片上传区域 -->
        <view class="form-item">
          <view class="form-label">相关图片</view>
          <view class="upload-container">
            <cu-upload
              v-model="formData.amountImages"
              height="200rpx"
              width="200rpx"
              placeholder="上传相关图片"
              @success="handleUploadSuccess"
              @fail="handleUploadFail"
            >
              <template #placeholder>
                <view class="upload-placeholder">
                  <view class="upload-icon">📷</view>
                  <text class="upload-text">上传图片</text>
                </view>
              </template>
            </cu-upload>
          </view>
          <view class="upload-tip">请上传相关证明图片（可选）</view>
        </view>
      </view>
      
      <!-- 按钮区域 -->
      <view class="dialog-footer">
        <view class="btn-cancel" @click="handleCancel">
          <text>取消</text>
        </view>
        <view 
          class="btn-confirm" 
          :class="{ 'disabled': !canSubmit }"
          @click="handleConfirm"
        >
          <text>确认申请</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import CuUpload from '../cu-upload/cu-upload.vue'

// 定义props
const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: '申请退订金'
  }
})

// 定义emit事件
const emit = defineEmits(['cancel', 'confirm', 'upload-success', 'upload-fail'])

// 弹窗引用
const popup = ref(null)

// 表单数据
const formData = ref({
  amountRemarks: '',
  amountImages: ''
})

// 计算是否可以提交
const canSubmit = computed(() => {
  return formData.value.amountRemarks.trim().length > 0
})

// 打开弹窗方法
const open = () => {
  // 重置表单数据
  formData.value = {
    amountRemarks: '',
    amountImages: ''
  }
  popup.value?.open()
}

// 关闭弹窗方法
const close = () => {
  popup.value?.close()
}

// 弹窗状态变化
const change = (e) => {
  // 可以在这里添加弹窗状态变化的处理逻辑
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  close()
}

// 处理确认按钮点击
const handleConfirm = () => {
  if (!canSubmit.value) {
    uni.showToast({
      title: '请输入申请原因',
      icon: 'none'
    })
    return
  }
  
  emit('confirm', {
    amountRemarks: formData.value.amountRemarks.trim(),
    amountImages: formData.value.amountImages
  })
  close()
}

// 处理图片上传成功
const handleUploadSuccess = (result) => {
  emit('upload-success', result)
}

// 处理图片上传失败
const handleUploadFail = (error) => {
  emit('upload-fail', error)
}

// 将方法暴露给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.apply-deposit-dialog {
  width: 640rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  
  // 标题区域
  .dialog-header {
    padding: 30rpx 30rpx 10rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .dialog-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  // 内容区域
  .dialog-content {
    padding: 30rpx;
    max-height: 600rpx;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .form-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        font-weight: 500;
      }
      
      .form-textarea {
        width: 100%;
        min-height: 120rpx;
        padding: 20rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #333;
        background-color: #fafafa;
        box-sizing: border-box;
        line-height: 1.5;
        
        &:focus {
          border-color: #e84138;
          background-color: #fff;
        }
      }
      
      .char-count {
        text-align: right;
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }
      
      .upload-container {
        display: flex;
        align-items: center;
      }
      
      .upload-tip {
        font-size: 24rpx;
        color: #999;
        margin-top: 12rpx;
      }
    }
  }
  
  // 按钮区域
  .dialog-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .btn-cancel,
    .btn-confirm {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:active {
        background-color: #f5f5f5;
      }
    }
    
    .btn-cancel {
      color: #666;
      border-right: 1rpx solid #f0f0f0;
    }
    
    .btn-confirm {
      color: #e84138;
      font-weight: 500;
      
      &.disabled {
        color: #ccc;
        cursor: not-allowed;
        
        &:active {
          background-color: transparent;
        }
      }
    }
  }
}

// 上传占位符样式
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  
  .upload-icon {
    font-size: 40rpx;
    margin-bottom: 8rpx;
  }
  
  .upload-text {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
