<template>
  <uni-popup ref="popup" type="center" :mask-click="false" @change="change">
    <view class="confirm-dialog">
      <!-- 标题区域 -->
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
      </view>
      
      <!-- 主要内容区域 -->
      <view class="dialog-content">
        <text class="content-text">{{ content }}</text>
        
        <!-- 重要提示区域 -->
        <view class="important-tips" v-if="importantTips">
          <view class="tips-icon">
            <text class="iconfont icon-owner-tishi"></text>
          </view>
          <text class="tips-text">{{ importantTips }}</text>
        </view>
      </view>
      
      <!-- 按钮区域 -->
      <view class="dialog-footer">
        <view 
          class="btn-cancel" 
          v-if="showCancel"
          @click="handleCancel"
        >
          <text>{{ cancelText }}</text>
        </view>
        <view 
          class="btn-confirm" 
          :class="{ 'btn-full': !showCancel }"
          @click="handleConfirm"
        >
          <text>{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 定义props
const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: ''
  },
  // 弹窗内容
  content: {
    type: String,
    default: ''
  },
  // 重要提示内容
  importantTips: {
    type: String,
    default: ''
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '我再想想'
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确定'
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  }
});

// 定义emit事件
const emit = defineEmits(['cancel', 'confirm']);

// 弹窗引用
const popup = ref(null);

// 打开弹窗方法
const open = () => {
  popup.value.open();
};

// 关闭弹窗方法
const close = () => {
  popup.value.close();
};

// 弹窗状态变化
const change = (e) => {
  // 可以在这里添加弹窗状态变化的处理逻辑
};

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel');
  close();
};

// 处理确认按钮点击
const handleConfirm = () => {
  emit('confirm');
  close();
};

// 将方法暴露给父组件
defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.confirm-dialog {
  width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  
  // 标题区域
  .dialog-header {
    padding: 30rpx 30rpx 10rpx;
    text-align: center;
    
    .dialog-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  // 内容区域
  .dialog-content {
    padding: 30rpx;
    
    .content-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
      text-align: center;
      display: block;
    }
    
    // 重要提示区域
    .important-tips {
      margin-top: 20rpx;
      background-color: #FFF5F5;
      padding: 20rpx;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      
      .tips-icon {
        margin-right: 10rpx;
        
        .iconfont {
          color: #f56c6c;
          font-size: 28rpx;
        }
      }
      
      .tips-text {
        flex: 1;
        font-size: 24rpx;
        color: #f56c6c;
        line-height: 1.5;
      }
    }
  }
  
  // 按钮区域
  .dialog-footer {
    display: flex;
    border-top: 1px solid #EBEEF5;
    
    .btn-cancel, .btn-confirm {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      text {
        font-size: 32rpx;
      }
    }
    
    .btn-cancel {
      border-right: 1px solid #EBEEF5;
      
      text {
        color: #666;
      }
    }
    
    .btn-confirm {
      text {
        color: #f56c6c;
        font-weight: 500;
      }
    }
    
    .btn-full {
      flex: 1;
    }
  }
}
</style> 