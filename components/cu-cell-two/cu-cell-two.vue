<template>
	<view class="cell-row">
		<view class="cell-label">
			<slot name="label"></slot>
		</view>
		<view class="cell-value">
			<slot name="value"></slot>
		</view>
	</view>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
.cell-row {
	width: 100%;
	display: flex;
	padding: 10rpx 0rpx;
	.cell-label {
		width: 150rpx;
		font-size: $uni-font-size-base;
		color: $uni-text-color-grey;
	}
	.cell-value {
		flex: 1;
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}
}
</style>
