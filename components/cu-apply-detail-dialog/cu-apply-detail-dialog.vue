<template>
  <uni-popup ref="popup" type="center" :mask-click="false" @change="change">
    <view class="apply-detail-dialog">
      <!-- 标题区域 -->
      <view class="dialog-header">
        <text class="dialog-title">{{ title }}</text>
      </view>
      
      <!-- 主要内容区域 -->
      <view class="dialog-content">
        <!-- 申请原因 -->
        <view class="detail-section" v-if="applyReason">
          <view class="section-label">申请原因</view>
          <view class="reason-content">{{ applyReason }}</view>
        </view>
        
        <!-- 相关图片 -->
        <view class="detail-section" v-if="imageList.length > 0">
          <view class="section-label">相关图片</view>
          <view class="image-list">
            <view 
              class="image-item" 
              v-for="(image, index) in imageList" 
              :key="index"
              @click="previewImage(image, index)"
            >
              <image 
                :src="image" 
                mode="aspectFill" 
                class="preview-image"
              />
            </view>
          </view>
        </view>
        
        <!-- 确认内容 -->
        <view class="confirm-content" v-if="content">
          <text class="content-text">{{ content }}</text>
        </view>
      </view>
      
      <!-- 按钮区域 -->
      <view class="dialog-footer">
        <view 
          class="btn-cancel" 
          v-if="showCancel"
          @click="handleCancel"
        >
          <text>{{ cancelText }}</text>
        </view>
        <view 
          class="btn-confirm" 
          :class="{ 'btn-full': !showCancel }"
          @click="handleConfirm"
        >
          <text>{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义props
const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: ''
  },
  // 确认内容
  content: {
    type: String,
    default: ''
  },
  // 申请原因
  applyReason: {
    type: String,
    default: ''
  },
  // 申请图片（逗号分隔的字符串）
  applyImages: {
    type: String,
    default: ''
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确定'
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  }
})

// 定义emit事件
const emit = defineEmits(['cancel', 'confirm'])

// 弹窗引用
const popup = ref(null)

// 处理图片列表
const imageList = computed(() => {
  if (!props.applyImages || props.applyImages.trim() === '') {
    return []
  }
  return props.applyImages.split(',').map(url => url.trim()).filter(url => url !== '')
})

// 打开弹窗方法
const open = () => {
  popup.value?.open()
}

// 关闭弹窗方法
const close = () => {
  popup.value?.close()
}

// 弹窗状态变化
const change = (e) => {
  // 可以在这里添加弹窗状态变化的处理逻辑
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  close()
}

// 处理确认按钮点击
const handleConfirm = () => {
  emit('confirm')
  close()
}

// 预览图片
const previewImage = (currentImage, currentIndex) => {
  uni.previewImage({
    urls: imageList.value,
    current: currentIndex,
    success: () => {
      console.log('图片预览成功')
    },
    fail: (err) => {
      console.error('图片预览失败:', err)
      uni.showToast({
        title: '图片预览失败',
        icon: 'none'
      })
    }
  })
}

// 将方法暴露给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.apply-detail-dialog {
  width: 640rpx;
  max-width: 90vw;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  
  // 标题区域
  .dialog-header {
    padding: 30rpx 30rpx 10rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .dialog-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  // 内容区域
  .dialog-content {
    padding: 30rpx;
    max-height: 600rpx;
    overflow-y: auto;
    
    .detail-section {
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-label {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 16rpx;
      }
      
      .reason-content {
        padding: 20rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        border: 1rpx solid #e9ecef;
      }
      
      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        
        .image-item {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
          overflow: hidden;
          background-color: #f8f9fa;
          border: 2rpx solid #e9ecef;
          padding: 4rpx;
          box-sizing: border-box;
          
          .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4rpx;
          }
          
          &:active {
            transform: scale(0.95);
            transition: transform 0.2s;
          }
        }
      }
    }
    
    .confirm-content {
      margin-top: 20rpx;
      
      .content-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        text-align: center;
        display: block;
      }
    }
  }
  
  // 按钮区域
  .dialog-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .btn-cancel,
    .btn-confirm {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:active {
        background-color: #f5f5f5;
      }
    }
    
    .btn-cancel {
      color: #666;
      border-right: 1rpx solid #f0f0f0;
    }
    
    .btn-confirm {
      color: #e84138;
      font-weight: 500;
      
      &.btn-full {
        border-right: none;
      }
    }
  }
}
</style>
