/**
 * 路由拦截，通常也是登录拦截
 * 可以设置路由白名单，或者黑名单，看业务需要选哪一个
 * 我这里应为大部分都可以随便进入，所以使用黑名单
 */
import { getTabBarIndex, resetTabBarIndex, getNeedLoginPages, needLoginPages as _needLoginPages } from '@/utils/routeTool';
import { getStoragetItem } from '@/utils/storage';

// TODO Check
const loginRoute = '/pages/login/login';

const isLogined = () => {
	const token = getStoragetItem('x-token');
	return token ? true : false;
};

const isDev = true;
let currentTabIndex = 0;

// 黑名单登录拦截器 - （适用于大部分页面不需要登录，少部分页面需要登录）
const navigateToInterceptor = {
	// 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
	invoke(route: any) {
		// console.log(url) // /pages/route-interceptor/index?name=feige&age=30
		const path = route.url.split('?')[0];
		let needLoginPages: string[] = [];
		// 为了防止开发时出现BUG，这里每次都获取一下。生产环境可以移到函数外，性能更好
		if (isDev) {
			needLoginPages = getNeedLoginPages();
		} else {
			needLoginPages = _needLoginPages;
		}
		const isNeedLogin = needLoginPages.includes(path);
		const hasLogin = isLogined();
		if (!isNeedLogin || hasLogin) {
			currentTabIndex = getTabBarIndex(route.url);
			return true;
		}
		// if (!isNeedLogin) {
		// 	return true;
		// }
		// if (hasLogin) {
		// 	return true;
		// }

		resetTabBarIndex(currentTabIndex); // 手动恢复
		const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(route.url)}`;
		uni.navigateTo({ url: redirectRoute });
		return false;
	}
};

export const routeInterceptor = {
	install() {
		uni.addInterceptor('navigateTo', navigateToInterceptor);
		uni.addInterceptor('reLaunch', navigateToInterceptor);
		uni.addInterceptor('redirectTo', navigateToInterceptor);
		uni.addInterceptor('switchTab', navigateToInterceptor);
	}
};
