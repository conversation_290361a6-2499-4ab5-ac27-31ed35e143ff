<template>
  <!-- <div>配货站-我的车队</div> -->
  <view class="car-team-page">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text></template>
			<template #title>
        <text>我的车队</text>
        <text class="team-name">{{ teamName }}</text>
      </template>
			<template #rightBtn>
				<text class="iconfont icon-owner-shezhi" @click="setPage"></text>
			</template>
		</cu-header>
    <view class="main-container">
      <view class="tip-content">
        <text class="welcome-text">欢迎使用 车队系统</text>
        <view class="item-con" v-for="item in textList" :key="item.id">
          <view class="iconfont icon-owner-duigouxiao"></view>
          <view class="con-text">{{ item.content }}</view>
        </view>
      </view>
      <view class="invite-content">
        <view class="content">
          <view>最多可添加20名司机</view>
          <view @click="changeName">修改队名</view>
        </view>
        <view class="new-driver" @click="addNewDriver">
          <view class="team-img">
            <image :src="addUrl"></image>
          </view>
          <view class="add-text">添加新司机</view>
        </view>
      </view>
      <view class="invite-content">
        <view class="team-list" v-if="addList.length > 0">
          <view class="team-item" v-for="item in addList" :key="item.id">
            <view class="team-img">
              <image :src="item.url"></image>
            </view>
            <view class="info-con">
              <view class="info-name">{{ item.driverName }}</view>
              <view class="info-phone">{{item.driverPhone}}</view>
            </view>
            <view class="btns">
              <text class="btn1" v-if="item.status==3" @click="phoneBtn(item)">打电话</text>
              <text class="btn2" v-else>等待添加</text>
            </view>
          </view>
        </view>
        <view>
          <uni-swipe-action class="team-list" v-if="refusedList.length > 0">
            <uni-swipe-action-item v-for="item in refusedList" :key="item.id" :right-options="options" :show="item.show" @click="clickOption">
              <view class="team-img">
                <image :src="item.url"></image>
              </view>
              <view class="info-con">
                <view class="info-name">{{ item.driverName }}</view>
                <view class="info-phone">{{ item.driverPhone }}</view>
              </view>
              <view class="btns">
                <text class="btn1">已拒绝</text>
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>
        </view>
      </view>
    </view>
    <!-- 添加司机弹窗 -->
    <uni-popup ref="addDriverRef" background-color="#fff" class="global-popup exit-team-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="title-box">
          <view>添加司机</view>
          <view class="iconfont icon-owner-close" @click="closePop"></view>
        </view>
        <view class="pop-content">
          <view class="phone-con">
            <up-input class="pop-input" v-model="phone" placeholder="请输入司机手机号码"></up-input>
            <button class="pop-btn" @click="searchPhone">查找</button>
          </view>
          <view class="search-con" v-if="searchList.length >0">
            <view class="team-list">
              <view class="team-item" v-for="item in searchList" :key="item.id" @click="chooseItem(item)">
                <view class="team-img">
                  <image :src="item.url"></image>
                </view>
                <view class="info-con">
                  <view class="info-name">{{ item.fullName }}</view>
                  <view class="info-phone">{{ item.mobile }}</view>
                </view>
                <view v-if="item.choose" class="iconfont icon-owner-duigou"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="confirm-btn">
          <button class="btn-com confirm-btn" @click="joinIn">邀请加入</button>
        </view>
      </view>
    </uni-popup>

    <!-- 移除成员弹窗 -->
    <uni-popup ref="exitTeamRef" background-color="#fff" class="global-popup exit-team-popup pop-height" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="content-text">
          <view class="text">移除成员后，您将无法查看该司机的订单和位置</view>
        </view>
        <view class="confirm-btn">
          <button class="btn-com" @click="cancel">我在想想</button>
          <button class="btn-com confirm-btn" @click="confirm">确认移除</button>
        </view>
      </view>
    </uni-popup>

    <!-- 修改队名弹窗 -->
    <uni-popup ref="changeNameRef" background-color="#fff" class="global-popup exit-team-popup pop-height1" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="title-box">
          <view>修改车队名称</view>
          <view class="iconfont icon-owner-close" @click="closeChange"></view>
        </view>
        <view class="content-text">
          <view class="input-con">
            <up-input type="text" v-model="carTeamName" placeholder="请输入车队名称"></up-input>
          </view>
        </view>
        <view class="confirm-btn">
          <button class="btn-com btn-width" @click="confirmName">确认</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref, reactive,computed} from 'vue';
import {getDriverList,selectDriver,inviteDriver,removeInvite,getTeamName,updateTeamName} from "../../api/userCenter";
const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const teamName = ref('')
const addUrl = '../../static/images/addMem.png'

const textList = [
  {
    id:1,
    content: '准确掌握司机位置'
  },
  {
    id:2,
    content: '司机订单随时可查'
  }
]

// 通过接口获取--获取之后给每一项加一个choose=false
const searchList = ref([
])

const allDriverList = ref([])

const addList = computed(() => allDriverList.value.filter(item => item.status !== 2));

const refusedList = computed(() => allDriverList.value.filter(item => item.status === 2));

const options = ref([
  {
    text: `移除成员`,
    style: {
      backgroundColor: 'red',
      color: '#fff',
      fontSize: '26rpx'
    }
  }
])

// 修改队名
const changeNameRef = ref(null)
const changeName = () => {
  changeNameRef.value.open('bottom');
}
const carTeamName = ref('')
const confirmName = async () => {
  // 点击确认修改名称调接口
  try {
    const response:any = await updateTeamName({
		  teamName: carTeamName.value
	  });
    uni.hideLoading();
    if (response.code === 700) {
       uni.showToast({
        title: response.msg || '修改成功',
        icon: 'none',
        duration: 2000
      });
      getTeamNameInit();
      changeNameRef.value.close();
    } else {
      console.error('修改车队名称失败:', response.code);
    }
  } catch (error) {
    console.error('修改车队名称失败:', error);
  }

  
}
const closeChange = () => {
  changeNameRef.value.close();
}

// 移除成员
const exitTeamRef = ref(null)
const clickIndex = ref(0)
const removeDirverId = ref(0)
const clickOption = (data:any) => {
    exitTeamRef.value.open('bottom');
    removeDirverId.value = refusedList.value[data.index].id
    console.log(removeDirverId.value,'qqqqqqq')
}

// 确认移除
const confirm = async() => {
  try {
    exitTeamRef.value.close();
    const response:any = await removeInvite({
		  id: removeDirverId.value
	  });
    uni.hideLoading();
    if (response.code === 700) {
       uni.showToast({
        title: response.msg || '移除成功',
        icon: 'none',
        duration: 2000
      });
      initData();
    } else {
      console.error('移除成功失败:', response.code);
    }
  } catch (error) {
    console.error('移除成功失败:', error);
  }
}
// 点击我在想想
const cancel = () => {
  removeDirverId.vaule = -1
  exitTeamRef.value.close();
}

// 点击打电话
const phoneBtn = (item:any) => {
  uni.makePhoneCall({phoneNumber:item.driverPhone})
}

const addDriverRef = ref(null)
const addNewDriver = (data:any) => {
  console.log('index', data)
  addDriverRef.value.open('bottom');
}
// 点击搜索手机号
const phone = ref('')
const searchPhone = async () => {
  try {
    const response:any = await selectDriver({
		  phone: phone.value
	  });
    uni.hideLoading();
    if (response.code === 700) {
         searchList.value = response.result
    } else {
      console.error('获取司机列表失败:', response.code);
    }
  } catch (error) {
    console.error('获取司机列表失败:', error);
  }
}



const closePop = () => {
  addDriverRef.value.close();
}

let chooseId = ''
const chooseItem = (item:any) => {
  chooseId = item.id
  searchList.value.forEach((child:any) => {
    child.choose = false
  })
  item.choose = true
}
// 点击邀请加入
const joinIn = async () => {
  try {
    const driverId = searchList.value[clickIndex.value].id
    const response:any = await inviteDriver({driverId:driverId});
    uni.hideLoading();
    if (response.code === 700) {
         initData();
    } else {
      uni.showToast({
        title: response.msg || '邀请司机失败',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('邀请司机失败:', error);
  }
};

// 点击设置
const setPage = () => {
  uni.redirectTo({
    url: '/pageComDriver/my/setPermission'
  })
}

const initData = async () => {
  try {
    const response:any = await getDriverList();
    uni.hideLoading();
    if (response.code === 700) {
         allDriverList.value = response.result
    } else {
      console.error('获取司机列表失败:', response.code);
    }
  } catch (error) {
    console.error('获取司机列表失败:', error);
  }
};

const getTeamNameInit = async () => {
  try {
    const response:any = await getTeamName();
    uni.hideLoading();
    if (response.code === 700) {
         teamName.value = response.msg
    } else {
      console.error('获取车队名称失败:', response.code);
    }
  } catch (error) {
    console.error('获取车队名称失败:', error);
  }
};

onMounted(() => {
	initData();
  searchPhone();
  getTeamNameInit();
});
</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
  .team-name {
    display: block;
    font-size: 24rpx;
    color: #e58814;
    text-align: center;
  }
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding-top: v-bind(heightHeader);
  .tip-content {
    background-color: #fff2f2;
    color: #333;
    padding: 20rpx;
    // height: 100rpx;
    .tip-text {
      padding-left: 20rpx;
    }
    .item-con {
      margin-top: 20rpx;
      padding-left: 20rpx;
      .iconfont {
        display: inline-block;
        color: #e58814;
        margin-right: 20rpx;
        font-size: 24rpx;
      }
      .con-text {
        display: inline-block;
        font-size: 24rpx;
      }
    }
    
  }
  .welcome-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
  .invite-content {
    padding: 20rpx;
    .content {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .new-driver {
      padding: 20rpx 30rpx;
      background-color: #fff;
      display: flex;
      margin-top: 20rpx;
      border-radius:10rpx;
      .team-img {
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
        image {
          width: 80rpx;
          height: 80rpx;
          display: inline-block;
        }
      }
      .add-text {
        font-size: 32rpx;
        line-height: 80rpx;
      }
    }
  }
}
.team-list {
  .team-item {
    padding: 20rpx 0;
    display: flex;
    flex-direction: row;
    .iconfont {
      font-size: 40rpx;
      line-height: 60rpx;
      font-weight: 600;
      color: #67c23a;
    }
    .team-img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      image {
        width: 80rpx;
        height: 80rpx;
        border-radius:40rpx;
        display: inline-block;
      }
    }
    .info-con {
      flex: 1;
      font-size: 28rpx;
      .info-phone {
        font-size: 24rpx;
      }
    }
    .btns{
      // width: 200rpx;
      :deep(.u-button) {
        display: inline-block;
        width: 100rpx;
        background-color: transparent;
        border: none;
      }
      :deep(.u-button:after){
        border: none;
      }
      .btn1 {
        font-size: 30rpx;
        color: #e58814;
        height: 60rpx;
        line-height: 60rpx;
        vertical-align: middle;
      }
      .btn2 {
        font-size: 30rpx;
        color: red;
        height: 60rpx;
        line-height: 60rpx;
        vertical-align: middle;
      }
    }
  }
  :deep(.uni-swipe_text--center){
    padding: 20rpx 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    .team-img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      image {
        width: 80rpx;
        height: 80rpx;
        border-radius:40rpx;
        display: inline-block;
      }
    }
    .info-con {
      flex: 1;
      font-size: 28rpx;
      .info-phone {
        font-size: 24rpx;
      }
    }
    .btns{
      .btn1 {
        display: inline-block;
        background-color: transparent;
        border: none;
        color: #e58814;
        height: 60rpx;
        line-height: 60rpx;
        vertical-align: middle;
      }
      .btn1:after {
        border: none;
      }
    }
  }
  :deep(.uni-swipe_button-group) {
    align-items: center;
  }
  :deep(.uni-swipe_button) {
    height: 60rpx;
    padding: 0 15rpx;
  }
}
:deep(.exit-team-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: 0;
    .popup-content {
      height: 500rpx;
      background-color: #fff;
      .pop-content {
        // margin-top: 20rpx;
        padding: 20rpx;
        .phone-con {
          display: flex;
          .pop-input {
            height: 70rpx;
            line-height: 70rpx;
            padding-left: 10rpx;
            border: 1px solid #ccc;
            width: 78%;
            margin-right: 20rpx;
            border-radius: 10rpx;
          }
          .pop-btn {
            height: 70rpx;
            line-height: 70rpx;
          }
        }
        .search-con{
          .team-list {
            height: 200rpx;
            overflow: auto;
          }
        }
      }
      .content-text {
        padding: 40rpx;
        .text {
          text-align: center;
        }
        .input-con {
          width: 100%;
          height: 70rpx;
          line-height: 70rpx;
          border: 1px solid #ccc;
          border-radius: 10rpx;
          padding-left: 10rpx;
        }
      }
      .confirm-btn {
        width: 90%;
        margin-left: 5%;
        position: absolute;
        bottom: 20rpx;
        // height: 60rpx;
        line-height: 60rpx;
        .btn-com {
          display: inline-block;
          width: 90%;
          margin: 0 5%;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          background-color: #8d8585;
          color: #fff;
          border-radius: 5rpx;
        }
        .confirm-btn {
          background-color: #3c9cff;
          color: #fff;
        }
      }
    }
  }
}
:deep(.pop-height) {
  .uni-popup__wrapper {
    .popup-content {
      height: 300rpx;
      .confirm-btn {
        .btn-com {
          width: 40%;
        }
        .btn-width {
          width: 90%;
          background-color: #3c9cff;
          color: #fff;
        }
      }
    }
  }
}
:deep(.pop-height1) {
  .uni-popup__wrapper {
    .popup-content {
      height: 400rpx;
      .confirm-btn {
        .btn-width {
          width: 90%;
          background-color: #3c9cff;
          color: #fff;
        }
      }
    }
  }
}
</style>