<template>
  <!-- 订金钱款去向 -->
  <view class="deposit-destination">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
      <template #leftBtn>
        <text class="iconfont icon-owner-a-fanhui<PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
      </template>
      <template #title>订金钱款去向</template>
      <template #rightBtn>{{ '' }}</template>
    </cu-header>
    <view class="main-container">
      <view class="tip-info">
        <view class="tip-com">
          <span class="left-img">
            <view class="iconfont icon-owner-duigouxiao"></view>
          </span>
          <span class="right-text">{{ tipContent }}</span>
        </view>
        <view class="tip-com price-con">
          <span>订金：</span>
          <span class="text-green"> ¥ {{ money }} ({{ isBack?'退还':'不退还' }})</span>
        </view>
      </view>
      <view class="step-info">
        <u-steps direction="column" dot :current="stepList.length" active-color="#fa3534">
          <u-steps-item v-for="step in stepList" :key="step" :title="step.name" :desc="step.desc">
          </u-steps-item>
        </u-steps>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref, reactive} from 'vue';
const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const tipContent = ref('') // 通过接口获取
tipContent.value = '订金已支付，平台监督中'
const money = ref(100) // 通过接口获取
const isBack = ref(false)
// 通过接口获取steplist
const stepList = ref([{
    name: '订金已支付，平台监控中',
    desc: '2025-06-14 10:10'
  }, {
    name: '货主同意退订金',
    desc: '2025-06-14 10:10'
  }, {
    name: '司机已发起退订金',
    desc: '2025-06-14 10:10'
  }
])
</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding-top: v-bind(heightHeader);
  .tip-info {
    width: 94%;
    margin-left: 3%;
    background-color: #fff;
    border-radius: 10rpx;
    .tip-com {
      height: 80rpx;
      line-height: 80rpx;
      color: #67c23a;
      background-color: #cce8bf;
      padding-left: 20rpx;
      .left-img {
        .iconfont {
          display: inline-block;
          margin-right: 10rpx;
        }
      }
    }
    .price-con {
      background-color: #fff;
      color: #333;
      .text-green {
        color: #67c23a;
      }
    }
  }
  .step-info {
    width: 94%;
    margin-left: 3%;
    margin-top: 50rpx;
    background-color: #fff;
    border-radius:10rpx;
    :deep(.u-steps) {
      // background-color: red;
      margin-bottom: 50rpx;
      padding: 20rpx;
    }
  }
  :deep(.u-steps-item__line) {
    height: 140rpx !important;
  }
  :deep(.u-steps-item--column) {
    margin-bottom: 80rpx;
  }
  // :deep(.u-steps-item__wrapper--column--dot) {
  //   width: 10px;
  //   height: 10px;
  // }
  // :deep(.u-steps-item__wrapper__dot) {
  //   width: 10px;
  //   height: 10px;
  // }
  // :deep(.u-steps-item__line--column) {
  //   left: 5px;
  // }
}
</style>