<template>
  <!-- <div>申请扣定金/申请退定金</div> -->
  <view class="pending-order-detail">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
      <template #leftBtn>
        <text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
      </template>
      <template #title>{{ showObj.topTitle }}</template>
      <template #rightBtn>{{ '' }}</template>
    </cu-header>
    <view class="main-container">
      <view class="tip-content">
        <text class="tip-text">{{ showObj.tipContent }}</text>
      </view>
      <view class="tip-info">
        <view class="tip-com">
          <span class="left-img">
            <view class="iconfont icon-owner-duigouxiao"></view>
          </span>
          <span class="right-text">司机已支付定金</span>
        </view>
        <view class="tip-com price-con">
          <span>订金：</span>
          <span class="text-green"> ¥ {{ money }} ({{ isBack ? '退还' : '不退还' }})</span>
        </view>
      </view>
      <view class="detail-text">
        <view class="detail-tit">详情描述</view>
        <view v-if="isSubmit" class="detail-con">详情描述文字112312312312</view>
        <view v-else class="detail-con">
          <up-textarea
              class="custom-textarea"
              height="260rpx"
              v-model="remarksVal"
              placeholder="请输入详情"
              :count="true"
              :maxlength="100"
          ></up-textarea>
        </view>
      </view>
      <view class="detail-text">
        <view class="detail-tit">图片</view>
        <view class="detail-con">
          <view v-if="isSubmit">
            <span v-for="item in imageList" :key="item" class="image-item">
              <image :src="item.url"></image>
            </span>
          </view>
          <view v-else class="detail-image">
            <span class="image-item" v-for="item in editImages" :key="item.id">
              <cu-upload
                  v-model="item.detailImage"
                  height="260rpx"
                  count="3"
                  placeholder="上传图片"
                  @success="handleImageSuccess(item)"
                  @fail="handleUploadFail"
              >
                <template #placeholder>
                  <view class="upload-placeholder">
                    <view class="upload-icon"></view>
                    <text class="upload-text">上传图片</text>
                  </view>
                </template>
              </cu-upload>
              <span v-if="item.detailImage" class="close-btn">
                <view class="iconfont icon-owner-guanbi"></view>
              </span>
            </span>
          </view>
        </view>
      </view>
    </view>
    <view class="agree-btn">
      <button class="btn" v-if="depositStatus == 1 && userInfo.userType == 1">申请扣订金</button>
      <button class="btn" v-else-if="depositStatus == 1 && userInfo.userType == 2">申请退订金</button>
      <button class="btn-flex" v-else-if="depositStatus == 6">同意扣除</button>
      <button class="btn-flex" v-else-if="depositStatus == 6">拒绝扣除</button>
      <button class="btn-flex" v-else-if="depositStatus == 6">申请仲裁</button>
      <button class="btn" v-else-if="depositStatus == 2">同意退还</button>
      <button class="btn" v-else-if="depositStatus == 2">拒绝退还</button>
      <button class="btn" v-else-if="depositStatus == 6">撤回扣订金</button>
      <button class="btn" v-else-if="depositStatus == 2">撤回退订金</button>
    </view>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref} from 'vue';
import {editDeposit, editOrderInfo} from "../../api/transport";
import { getStoragetItem } from '@/utils/storage';

const userInfo = ref(getStoragetItem('userInfo'))

const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const cargoObj = {
  topTitle: '申请扣订金',
  tipContent: '您已申请扣除订金，待对方确认'
}
const driverObj = {
  topTitle: '申请退订金',
  tipContent: '您已申请退回订金，待对方确认'
}
const depositStatus = ref(0);
const orderId = ref('');


onMounted(() => {
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;
    orderId.value = options.orderId;
    depositStatus.value = Number(options.depositStatus);
  }
});


// 申请扣订金
const handleSubmit = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 1
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '申请扣订金成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '申请扣订金失败',
      icon: 'none',
      duration: 2000
    });
  }
};
// 申请退订金
const handleSubmitDeposit = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 2
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '申请退订金成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '申请退订金失败',
      icon: 'none',
      duration: 2000
    });
  }
};
// 同意扣除
const handleDeposit = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 3
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '同意扣除成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '同意扣除失败',
      icon: 'none',
      duration: 2000
    });
  }
};
// 拒绝扣除
const handleRefuseDeposit = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 4
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '拒绝扣除成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '拒绝扣除失败',
      icon: 'none',
      duration: 2000
    });
  }
};
// 申请仲裁
const handleApply = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 5
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '申请仲裁成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '申请仲裁失败',
      icon: 'none',
      duration: 2000
    });
  }
};
// 同意退还
const handleRefund = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 6
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '同意退还成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '同意退还失败',
      icon: 'none',
      duration: 2000
    });
  }
}
// 拒绝退还
const handleRefuseRefund = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 7
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '拒绝退还成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '拒绝退还失败',
      icon: 'none',
      duration: 2000
    });
  }
}
//撤回扣订金
const handleWithdrawDeposit = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 8
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '撤回扣订金成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '撤回扣订金失败',
      icon: 'none',
      duration: 2000
    });
  }
}
//撤回退订金
const handleWithdrawDepositBack = async () => {
  let detailImage = editImages.value.map((item) => item.detailImage).join(',');
  const result: any = await editDeposit({
    "id": orderId.value,
    "amountRemarks": remarksVal.value,
    "amountImages": detailImage,
    "depositOperate": 9
  });
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '撤回退订金成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '撤回退订金失败',
      icon: 'none',
      duration: 2000
    });
  }
}
// 通过接口判断身份
const isDriver = ref(false)
// 通过接口判断是否提交过
const isSubmit = ref(false)
let showObj = ref({})
if (isDriver.value) {
  showObj.value = {...driverObj}
} else {
  showObj.value = {...cargoObj}
}
const money = ref(0)
money.value = 100
const isBack = ref(false)
const remarksVal = ref('')
const detailImage = ref('')
const imageList = ref([
  {
    id: 1,
    url: '../../static/images/head.png'
  }
])
const editImages = ref([
  {
    id: 1,
    detailImage: ''
  }
])

// 处理详情图片上传成功
const handleImageSuccess = (res: any) => {
  console.log('图片上传成功', res);
  // 根据当前id存储图片路径
  editImages.value.forEach((item: any) => {
    if (item.id == res.id) {
      item.detailImage = res.detailImage
    }
  })
  // 这里可以调用上传接口
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  });

  // 上传成功之后，添加新的可以上传的空白入口
  let id = res.id
  editImages.value.push({
    id: id + 1,
    detailImage: ''
  })
  //可以通过id判断能够上传的图片个数
};

// 处理上传失败
const handleUploadFail = (err: any) => {
  console.error('上传失败', err);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
    duration: 2000
  });
};
</script>
<style lang="scss" scoped>
.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

:deep(.cu-header-box) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  background-size: 100% 400rpx;
  z-index: 3;

  .header-container {
    background-color: transparent;
  }

  .cu-header {
    background-color: transparent;
  }
}

.main-container {
  width: 100%;
  position: relative;
  z-index: 1;
  padding-top: v-bind(heightHeader);
}

.tip-content {
  background-color: #fff2f2;
  color: #ff6b88;
  padding: 20rpx;
  height: 100rpx;

  .tip-text {
    padding-left: 20rpx;
  }
}

.tip-info {
  width: 94%;
  margin-left: 3%;

  .tip-com {
    height: 80rpx;
    line-height: 80rpx;
    color: #67c23a;
    background-color: #cce8bf;
    padding-left: 20rpx;

    .left-img {
      .iconfont {
        display: inline-block;
        margin-right: 10rpx;
      }
    }
  }

  .price-con {
    background-color: #fff;
    color: #333;

    .text-green {
      color: #67c23a;
    }
  }
}

.detail-text {
  width: 94%;
  margin-left: 3%;

  .detail-tit {
    margin: 20rpx 0;
  }

  .detail-con {
    font-size: 24rpx;
    color: #8e8787;

    .custom-textarea {
      background-color: $uni-bg-color-grey;

      .u-textarea__field {
        .uni-textarea-wrapper {
          .uni-textarea-textarea {
            color: #000;
            font-size: $uni-font-size-lg;
            font-weight: bold;
          }
        }
      }

      .u-textarea__count {
        background-color: $uni-bg-color-grey !important;
      }
    }

    .detail-image {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;

      .image-item {
        // flex-grow: 1;
        width: 120rpx;
        height: 120rpx;
        display: inline-block;
        margin: 0 20rpx;
        position: relative;

        image {
          width: 120rpx;
          height: 120rpx;
        }

        .image-uploader {
          height: 100% !important;
        }

        .close-btn {
          position: absolute;
          top: -10rpx;
          right: -10rpx;
          display: inline-block;
        }
      }
    }
  }
}

.agree-btn {
  position: absolute;
  bottom: 10rpx;
  width: 94%;
  margin-left: 3%;
  display: flex;
  flex-direction: row;

  .btn {
    width: 100%;
  }
}
</style>
