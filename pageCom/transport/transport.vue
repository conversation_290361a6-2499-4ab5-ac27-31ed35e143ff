<template>
	<view class="transport">
		<view class="header-container" :style="{ paddingTop: systemInfo.safeAreaInsets.top * 2 + 'rpx' }">
			<view class="header-container-box">
				<view class="left">VIP待开通</view>
				<view class="right">待办</view>
			</view>
		</view>
		<view class="main-container" :style="{ top: systemInfo.safeAreaInsets.top * 2 + 100 + 'rpx' }">
			<view class="tabs">
				<view class="tab" :class="activeTab == 1 ? 'active-tab' : ''" @tap="switchTab(1)">发货</view>
				<view class="tab" :class="activeTab == 2 ? 'active-tab' : ''" @tap="switchTab(2)">发货中</view>
				<view class="tab" :class="activeTab == 3 ? 'active-tab' : ''" @tap="switchTab(3)">发货历史</view>
			</view>
		</view>
		<view class="main-content" :style="{ marginTop: systemInfo.safeAreaInsets.top * 2 + 100 + 124 + 'rpx', paddingBottom: dynamicPaddingBottom }">
			<Consignment v-if="activeTab == 1"></Consignment>
			<Shopping v-if="activeTab == 2"></Shopping>
			<History v-if="activeTab == 3"></History>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import Consignment from './components/consignment.vue';
import Shopping from './components/shipping.vue';
import History from './components/history.vue';

const systemInfo: any = uni.getSystemInfoSync();
const tabbarH = ref(0);

// 计算动态底部间距：tabbar高度100rpx + 底部安全区域
const dynamicPaddingBottom = ref('100rpx');
const activeTab = ref(null);
const switchTab = (num: number) => {
	activeTab.value = num;
};
const childRef = ref<InstanceType<typeof Consignment> | null>(null);

const callChildMethod = (params: any) => {
  if (childRef.value) {
    childRef.value.receiveCargoInfo(params);
  }
};
defineExpose({
  callChildMethod
});
onMounted(() => {
	setTimeout(() => {
		activeTab.value = 1
	},1000)

	// 动态计算底部间距：tabbar高度100rpx + 底部安全区域
	const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;
	const safeAreaBottomRpx = safeAreaBottom * 2; // px转rpx
	dynamicPaddingBottom.value = `calc(100rpx + ${safeAreaBottomRpx}rpx)`;

	// 保持原有tabbarH逻辑用于其他可能的用途
	tabbarH.value = 50 + safeAreaBottom; // px单位

	// #ifdef APP-PLUS
	console.log('当前是App环境');
	// #endif
});
</script>

<style scoped lang="scss">
.header-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 200rpx;
	background-image: linear-gradient(#ee9209, #f3f3f3);
	z-index: 1;

	.header-container-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		font-size: 32rpx;

		.left {
			padding: 10rpx 10rpx;
			box-sizing: border-box;
			background-color: red;
			border-radius: 10rpx;
			color: #fff;
			font-weight: bolder;
		}
	}
}

.main-container {
	position: fixed;
	left: 0px;
	padding: 0rpx 32rpx;
	box-sizing: border-box;
	width: 100%;
	margin-top: 20rpx;
	z-index: 1;

	.tabs {
		width: 100%;
		display: flex;
		justify-content: space-between;
		background-color: #fff;
		padding: 30rpx 30rpx;
		box-sizing: border-box;
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;

		.active-tab {
			position: relative;

			&::after {
				content: '';
				width: 60rpx;
				height: 8rpx;
				background-color: #ee9209;
				border-radius: 2rpx;
				position: absolute;
				bottom: -8px;
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}
}
.main-content {
	width: 100%;
	padding: 0rpx 32rpx;
	box-sizing: border-box;
	position: absolute;
}
</style>
