<template>
	<view :class="prohibitRoll ? 'prohibit-roll' : ''">
		<cu-header>
			<template #leftBtn>
				<text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
			</template>
			<template #title>货物信息</template>
			<template #rightBtn>{{ '' }}</template>
		</cu-header>
		<view class="main-container" :style="{ paddingBottom: safeAreaInsetsBottom + 200 + 'rpx' }">
			<view class="card-container">
				<view class="name-info">
					<view class="label-left">
						<text class="symbol">*</text>
						<text class="label">货物名称</text>
					</view>
					<view class="value-right" @tap="toggle">
						<text class="value ellipsis" v-if="cargoInfo.name">{{ cargoInfo.name }}</text>
						<text class="value-empty" v-if="!cargoInfo.name">请输入</text>
						<text class="iconfont icon-owner-youji<PERSON><PERSON>"></text>
					</view>
				</view>
				<view class="package-info">
					<view class="label-left">
						<text class="symbol">*</text>
						<text class="label">包装方式</text>
					</view>
					<view class="type-list">
						<view class="type-item" :class="item.selected ? 'type-item-active' : ''" v-for="(item, index) in typeList" :key="index" @click="typeClick(index)">{{ item.name }}</view>
					</view>
				</view>
			</view>
			<view class="card-container">
				<view class="weight-volume">
					<view class="label-left">
						<text class="symbol">*</text>
						<text class="label">总重量/体积</text>
						<text class="text">（至少填一项）</text>
					</view>
					<view class="weight">
						<view class="weight-label">重量（吨）</view>
						<view class="weight-interval">
							<input type="number" v-model="cargoInfo.weightStart" placeholder="0-999" class="input-num" @input="inputNumListern($event, 0, 999, 'weightStart')" />
							<text>一</text>
							<input type="number" v-model="cargoInfo.weightEnd" placeholder="0-999" class="input-num" @input="inputNumListern($event, 0, 999, 'weightEnd')" />
						</view>
					</view>
					<view class="weight">
						<view class="weight-label">体积（方）</view>
						<view class="weight-interval">
							<input type="number" v-model="cargoInfo.volumeStart" placeholder="0-999" class="input-num" @input="inputNumListern($event, 0, 999, 'volumeStart')" />
							<text>一</text>
							<input type="number" v-model="cargoInfo.volumeEnd" placeholder="0-999" class="input-num" @input="inputNumListern($event, 0, 999, 'volumeEnd')" />
						</view>
					</view>
					<view class="choose-tips">
						<text class="iconfont icon-owner-tishi1"></text>
						<text>司机反馈补全体积（方）信息，成交更快</text>
					</view>
				</view>
			</view>
			<view class="card-container">
				<view class="card-number-box">
					<view class="card-number">
						<view class="label-left">
							<text class="symbol">*</text>
							<text class="label">需要车辆数</text>
						</view>
						<view class="card-stepper">
							<view class="handle iconfont icon-owner-jianqu" @tap="minusHandle" v-if="cargoInfo.cardNum > 1"></view>
							<view class="handle handle-no iconfont icon-owner-jianqu" v-if="cargoInfo.cardNum == 1"></view>
							<view class="input-box">
								<input type="number" v-model="cargoInfo.cardNum" class="input-num-card" @input="inputNumListern($event, 1, 999, 'cardNum')" />
								<text>辆</text>
							</view>
							<view class="handle handle-add iconfont icon-owner-jia" @tap="addHandle" v-if="cargoInfo.cardNum < 999"></view>
							<view class="handle handle-no handle-add iconfont icon-owner-jia" v-if="cargoInfo.cardNum == 999"></view>
						</view>
					</view>
					<!-- <view class="card-tip">填写需要车辆数，仅扣一次发货次数（满帮优车不扣发货次数），可多次成交，曝光更多司机</view> -->
				</view>
			</view>
			<view class="card-container">
				<view class="degree-info">
					<view class="label-left">
						<text class="symbol">*</text>
						<text class="label">温控范围</text>
					</view>
					<view class="type-list">
						<view class="type-item" :class="item.selected ? 'type-item-active' : ''" v-for="(item, index) in temperatureList" :key="index" @click="temperatureClick(index)">
							<view class="text">{{ item.text }}</view>
							<view class="degree" v-if="item.degree">{{ item.degree }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="submit-box" :style="{ bottom: safeAreaInsetsBottom + 'rpx' }">
			<view class="btn" @tap="goBackWithParams">确定</view>
		</view>
		<cu-secure></cu-secure>
		<view class="popup-box">
			<uni-popup ref="popupRef" background-color="#fff" class="global-popup cargo-name-input-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
				<view class="popup-content">
					<view class="title-box">
						<text class="btn cancel" @tap="cancel">取消</text>
						<text class="title">货物名称</text>
						<text class="btn confirm" @tap="confirm">确定</text>
					</view>
					<view class="input-box">
						<uni-easyinput prefixIcon="search" v-model="nameInputVal" placeholder="请如实填写货物名称" :inputBorder="false" :maxlength="12" primaryColor="#c0c4cc"></uni-easyinput>
					</view>
					<view class="tips">
						<text class="iconfont icon-owner-tishi1"></text>
						<text class="text">
							请勿发布及托运危险货物、违禁货物，限运货物需有相关证明文件，否则您可能面临平台禁用账号、行政处罚、国家公诉等不利后果。
						</text>
					</view>
				</view>
			</uni-popup>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue';

const { safeAreaInsets } = uni.getSystemInfoSync();
const safeAreaInsetsBottom = safeAreaInsets.bottom * 2;
const headerHeight = safeAreaInsets.top * 2 + 80 + 'rpx';
const cargoInfo: any = ref({
	name: '',
	weightStart: '',
	weightEnd: '',
	volumeStart: '',
	volumeEnd: '',
	cardNum: 1,
  temperature:'',
  packageType:'',
});
const goBackWithParams = () => {
  try {
    // 获取当前页面栈
    const pages = getCurrentPages();
    if (pages.length > 1) {
      // 获取当前页面实例，页面栈最后一个元素为当前页面
      const currentPage = pages[pages.length - 1];
      // 从页面实例获取事件通道
      const eventChannel = currentPage.$vm.getOpenerEventChannel();
      if (eventChannel && typeof eventChannel.emit === 'function') {
        // 发送数据
        console.log('cargoInfo.value:', JSON.stringify(cargoInfo.value));
        eventChannel.emit('acceptDataFromChild', { cargoInfo: cargoInfo.value });
      } else {
        console.warn('事件通道对象不可用或 emit 方法不存在');
      }
    } else {
      console.warn('页面栈中没有上一页，无法获取事件通道');
    }
  } catch (error) {
    console.error('获取事件通道时出错:', error);
  }
  // 返回上一页
  uni.navigateBack();
  /*const pages = getCurrentPages();
  console.log('当前页面栈:', pages); // 打印页面栈，方便调试

  if (pages.length >= 2) {
    const prevPage = pages[pages.length - 2];
    console.log('上一页实例:', prevPage); // 打印上一页实例

    if (prevPage.$vm) {
      if (typeof prevPage.$vm.callChildMethod === 'function') {
        try {
          prevPage.$vm.callChildMethod(cargoInfo.value);
        } catch (error) {
          console.error('调用上一页的 callChildMethod 方法时出错:', error);
        }
      } else {
        console.warn('上一页的 callChildMethod 方法未定义或不是函数');
      }
    } else {
      console.warn('上一页实例的 $vm 属性不存在');
    }
  } else {
    console.warn('页面栈中没有上一页');
  }*/

};
const typeList = ref([
	{
		id: 1,
		name: '吨包',
		selected: true
	},
	{
		id: 2,
		name: '捆扎',
		selected: false
	},
	{
		id: 3,
		name: '散装',
		selected: false
	},
	{
		id: 4,
		name: '纸箱',
		selected: false
	},
	{
		id: 6,
		name: '木箱',
		selected: false
	},
	{
		id: 7,
		name: '其他',
		selected: false
	}
]);
const typeClick = (index: any) => {
	for (let item of typeList.value) {
		item.selected = false;
	}
	typeList.value[index].selected = true;
  cargoInfo.value.packageType = typeList.value[index].name;

};
const temperatureClick = (index: any) => {
  for (let item of temperatureList.value) {
    item.selected = false;
  }
  temperatureList.value[index].selected = true;
  cargoInfo.value.temperature = temperatureList.value[index].id;
};
const inputNumListern = (event: any, min: number, max: number, val: string) => {
	const number = event.detail.value - 0; //转成数字类型
	if (number < min) {
		nextTick(() => {
			cargoInfo.value[val] = min;
		});
	}
	if (number > max) {
		nextTick(() => {
			cargoInfo.value[val] = max;
		});
	}
};
const minusHandle = () => {
	cargoInfo.value.cardNum = --cargoInfo.value.cardNum;
};
const addHandle = () => {
	cargoInfo.value.cardNum = ++cargoInfo.value.cardNum;
};
const temperatureList: any = ref([
	{
		id: 1,
		text: '冷藏',
		degree: '0-10°C',
		selected: true
	},
	{
		id: 2,
		text: '冷冻',
		degree: '',
		selected: false
	},
	{
		id: 3,
		text: '恒温',
		degree: '10-20°C',
		selected: false
	},
	{
		id: 4,
		text: '常温',
		degree: '环境温度',
		selected: false
	}
]);
const popupRef = ref(null);
const prohibitRoll = ref(false);
const toggle = () => {
	prohibitRoll.value = true;
	nameInputVal.value = cargoInfo.value.name;
	popupRef.value.open('bottom');
};
const cancel = () => {
	prohibitRoll.value = false;
	nameInputVal.value = '';
	popupRef.value.close();
};
const confirm = () => {
	prohibitRoll.value = false;
	cargoInfo.value.name = nameInputVal.value;
	nameInputVal.value = '';
	popupRef.value.close();
};
const nameInputVal = ref('');
onMounted(() => {});
</script>

<style scoped lang="scss">
.icon-owner-a-fanhuizuojiantou {
	color: #000;
	font-size: $uni-font-size-big;
}
.main-container {
	width: 100%;
	position: relative;
	top: v-bind(headerHeight);
	padding: 0rpx $uni-padding-left-right 0rpx $uni-padding-left-right;
	box-sizing: border-box;
	.name-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0px;
		border-bottom: 1rpx solid $uni-text-color-grey;
		.label-left {
			width: 180rpx;
			.symbol {
				color: red;
				margin-right: 4rpx;
			}
			.label {
				color: #000;
				font-size: $uni-font-size-lg;
				font-weight: bold;
			}
		}
		.value-right {
			flex: 1;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			.value {
				width: 420rpx;
				font-size: $uni-font-size-lg;
				text-align: right;
			}
			.value-empty {
				font-size: $uni-font-size-lg;
				color: $uni-text-color-grey;
			}
			.iconfont {
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
				margin-left: 6rpx;
			}
		}
	}
	.package-info {
		display: flex;
		flex-direction: column;
		padding: 30rpx 0px;
		.label-left {
			width: 180rpx;
			.symbol {
				color: red;
				margin-right: 4rpx;
			}
			.label {
				color: #000;
				font-size: $uni-font-size-lg;
				font-weight: bold;
			}
		}
		.type-list {
			display: flex;
			flex-wrap: wrap;
			margin-top: 20rpx;
			.type-item {
				width: 200rpx;
				height: 80rpx;
				text-align: center;
				line-height: 80rpx;
				background-color: $uni-bg-color-grey;
				color: $uni-text-color-placeholder;
				font-size: $uni-font-size-lg;
				margin-right: 16rpx;
				margin-top: 16rpx;
				border-radius: 10rpx;
				border: 1rpx solid $uni-bg-color-grey;
				&:nth-child(3n) {
					margin-right: 0rpx;
				}
			}
			.type-item-active {
				background-color: #ffe4b5;
				color: #ff8c00;
				border: 1rpx solid #ff8c00;
			}
		}
	}
	.weight-volume {
		display: flex;
		flex-direction: column;
		padding: 20rpx 0px 30rpx 0rpx;
		.label-left {
			margin-bottom: 10rpx;
			.symbol {
				color: red;
				margin-right: 4rpx;
			}
			.label {
				color: #000;
				font-size: $uni-font-size-lg;
				font-weight: bold;
			}
			.text {
				font-size: $uni-font-size-base;
				color: $uni-text-color-placeholder;
			}
		}
		.weight {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;
			.weight-label {
				width: 180rpx;
				font-size: $uni-font-size-lg;
				color: $uni-text-color-placeholder;
			}
			.weight-interval {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				text {
					color: $uni-bg-page;
					margin: 0 20rpx;
				}
				.input-num {
					width: 170rpx;
					height: 70rpx;
					line-height: 70rpx;
					text-align: center;
					background-color: $uni-bg-color-grey;
					font-size: $uni-font-size-lg;
					color: #000;
				}
			}
		}
		.choose-tips {
			display: flex;
			align-items: center;
			padding: 10rpx 20rpx;
			box-sizing: border-box;
			width: 100%;
			background-color: #ffe4b5;
			font-size: $uni-font-size-base;
			color: #ff8c00;
			border-radius: 10rpx;
			margin-top: 18rpx;

			.iconfont {
				margin-right: 8rpx;
			}
		}
	}
	.card-number-box {
		// padding-bottom: 30rpx;
		.card-number {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0px 30rpx 0rpx;
			.label-left {
				width: 200rpx;
				.symbol {
					color: red;
					margin-right: 4rpx;
				}
				.label {
					color: #000;
					font-size: $uni-font-size-lg;
					font-weight: bold;
				}
			}
			.card-stepper {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				.handle {
					width: 70rpx;
					height: 70rpx;
					text-align: center;
					line-height: 70rpx;
					background-color: $uni-bg-color-grey;
					border-radius: 10rpx 0rpx 0rpx 10rpx;
					font-size: $uni-font-size-lg;
					color: #000;
				}
				.handle-add {
					border-radius: 0rpx 10rpx 10rpx 0rpx;
				}
				.handle-no {
					color: $uni-text-color-placeholder;
				}
				.input-box {
					height: 70rpx;
					background-color: $uni-bg-color-grey;
					margin: 0 6rpx;
					display: flex;
					align-items: center;
					padding-right: 40rpx;
					font-size: $uni-font-size-lg;
					color: #000;
					.input-num-card {
						width: 70rpx;
						text-align: right;
						margin-right: 8rpx;
						font-weight: bold;
					}
				}
			}
		}
		.card-tip {
			padding: 20rpx;
			box-sizing: border-box;
			background-color: $uni-bg-color-grey;
			font-size: $uni-font-size-sm;
			border-radius: 10rpx;
		}
	}
	.degree-info {
		display: flex;
		flex-direction: column;
		padding: 30rpx 0px;
		.label-left {
			width: 180rpx;
			.symbol {
				color: red;
				margin-right: 4rpx;
			}
			.label {
				color: #000;
				font-size: $uni-font-size-lg;
				font-weight: bold;
			}
		}
		.type-list {
			display: flex;
			flex-wrap: wrap;
			margin-top: 20rpx;
			.type-item {
				width: 200rpx;
				height: 90rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				background-color: $uni-bg-color-grey;
				color: $uni-text-color-placeholder;
				font-size: $uni-font-size-base;
				margin-right: 16rpx;
				margin-top: 16rpx;
				border-radius: 10rpx;
				border: 1rpx solid $uni-bg-color-grey;
				&:nth-child(3n) {
					margin-right: 0rpx;
				}
			}
			.type-item-active {
				background-color: #ffe4b5;
				color: #ff8c00;
				border: 1rpx solid #ff8c00;
			}
		}
	}
}
.submit-box {
	width: 100%;
	padding: 20rpx 30rpx 20rpx 30rpx;
	box-sizing: border-box;
	background-color: $uni-bg-color;
	position: fixed;
	left: 0;
	bottom: 0;

	.btn {
		width: 100%;
		height: 100rpx;
		text-align: center;
		line-height: 100rpx;
		background-color: #ed782f;
		color: #fff;
		font-size: $uni-font-size-lg;
		border-radius: 20rpx;
	}
}
.popup-box {
	:deep(.cargo-name-input-popup) {
		.uni-popup__wrapper {
			.popup-content {
				height: 400rpx;
				.input-box {
					width: 100%;
					padding: 0rpx 20rpx;
					box-sizing: border-box;
					margin-top: 30rpx;
					.uni-easyinput {
						.uni-easyinput__content {
							height: 70rpx;
							background-color: $uni-bg-color-grey !important;
							.uni-easyinput__content-input {
								font-size: $uni-font-size-lg;
							}
						}
					}
				}
				.tips {
					width: 100%;
					padding: 0rpx 20rpx;
					box-sizing: border-box;
					margin-top: 30rpx;
					display: flex;
					align-items: flex-start;
					.iconfont {
						margin-right: 10rpx;
					}
					.text {
						font-size: $uni-font-size-sm;
						.link {
							color: blue;
						}
					}
				}
			}
		}
	}
}
</style>
