<template>
  <view>
    <cu-header>
      <template #leftBtn>
        <text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
      </template>
      <template #title>车长车型</template>
      <template #rightBtn>{{ '' }}</template>
    </cu-header>
    <view
      class="main-container"
      :style="{ paddingBottom: safeAreaInsetsBottom + 200 + 'rpx' }"
    >
      <view class="card-container">
        <view class="card-length">
          <view class="label-left">
            <text class="symbol">*</text>
            <text class="label">车长</text>
            <text class="text">（必填，最多3项）</text>
          </view>
          <view class="option-list">
            <view
              class="option-item"
              :style="{ opacity: !item.isAvailable ? '0.5' : '' }"
              :class="item.selected ? 'option-item-active' : ''"
              v-for="(item, index) in lengthList"
              :key="index"
              @tap="cardLengthClick(index)"
            >
              {{ item.carTypeNames }}
            </view>
          </view>
        </view>
      </view>
      <view class="card-container">
        <view class="card-length">
          <view class="label-left">
            <text class="symbol">*</text>
            <text class="label">车型</text>
            <text class="text">（必填，最多3项）</text>
          </view>
          <view class="option-list">
            <view
              class="option-item"
              :style="{ opacity: !item.isAvailable ? '0.5' : '' }"
              :class="item.selected ? 'option-item-active' : ''"
              v-for="(item, index) in modalList"
              :key="index"
              @tap="modalClick(index)"
            >
              {{ item.typename }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="submit-box" :style="{ bottom: safeAreaInsetsBottom + 'rpx' }">
      <view class="btn" @tap="submit">确定</view>
    </view>
    <cu-secure></cu-secure>
  </view>
</template>

<script setup lang="ts">
import { onShow } from '@dcloudio/uni-app'
import { ref, watch } from 'vue'
import { getModel, getCarLengthList } from '../../api/transport'
import _ from 'lodash'

const { safeAreaInsets } = uni.getSystemInfoSync()
const safeAreaInsetsBottom = safeAreaInsets.bottom * 2
const headerHeight = safeAreaInsets.top * 2 + 80 + 'rpx'
const lengthList: any = ref([])
const modalList: any = ref([])
const cardLengthSelected = ref([])
const modalSelected = ref([])
const cardLengthClick = (index: any) => {
  if (!lengthList.value[index].isAvailable) {
    uni.showToast({
      title: '当前项不可选',
      icon: 'none',
    })
    return
  }
  if (
    cardLengthSelected.value.length == 3 &&
    !lengthList.value[index].selected
  ) {
    uni.showToast({
      title: '最多选择三个车长',
      icon: 'none',
    })
    return
  }
  lengthList.value[index].selected = !lengthList.value[index].selected
  if (lengthList.value[index].selected) {
    cardLengthSelected.value.push(lengthList.value[index])
  } else {
    for (let i in cardLengthSelected.value) {
      if (cardLengthSelected.value[i].id == lengthList.value[index].id) {
        cardLengthSelected.value.splice(Number(i), 1)
      }
    }
  }
  checkModelData()
}
// 更新处理车型数据
const checkModelData = () => {
  let modelArr = []
  for (let item of cardLengthSelected.value) {
    for (let itm of item.carTypes) {
      modelArr.push(itm.id)
    }
  }
  modelArr = _.uniq(modelArr)
  for (let modalItem of modalList.value) {
    if (!modelArr.includes(modalItem.id)) {
      modalItem.isAvailable = false
      modalItem.selected = false
    } else {
      if (!modalItem.selected) {
        modalItem.isAvailable = true
        modalItem.selected = false
      }
    }
    if (modelArr.length == 0) {
      modalItem.isAvailable = true
      modalItem.selected = false
    }
  }
  for (let modalItem of modalList.value) {
    for (let i in modalSelected.value) {
      if (modalSelected.value[i].id == modalItem.id && !modalItem.selected) {
        modalSelected.value.splice(Number(i), 1)
      }
    }
  }
}

const modalClick = (index: any) => {
  if (!modalList.value[index].isAvailable) {
    uni.showToast({
      title: '当前项不可选',
      icon: 'none',
    })
    return
  }
  if (modalSelected.value.length == 3 && !modalList.value[index].selected) {
    uni.showToast({
      title: '最多选择三个车型',
      icon: 'none',
    })
    return
  }
  modalList.value[index].selected = !modalList.value[index].selected
  if (modalList.value[index].selected) {
    modalSelected.value.push(modalList.value[index])
  } else {
    for (let i in modalSelected.value) {
      if (modalSelected.value[i].id == modalList.value[index].id) {
        modalSelected.value.splice(Number(i), 1)
      }
    }
  }
  checkCarLengthData()
}
// 更新处理车长数据
const checkCarLengthData = () => {
  let lengthArr = []
  for (let item of modalSelected.value) {
    for (let itm of item.carLength) {
      lengthArr.push(itm.id)
    }
  }
  lengthArr = _.uniq(lengthArr)
  for (let lengthItem of lengthList.value) {
    if (!lengthArr.includes(lengthItem.id)) {
      lengthItem.isAvailable = false
      lengthItem.selected = false
    } else {
      if (!lengthItem.selected) {
        lengthItem.isAvailable = true
        lengthItem.selected = false
      }
    }
    if (lengthArr.length == 0) {
      lengthItem.isAvailable = true
      lengthItem.selected = false
    }
  }
  for (let lengthItem of lengthList.value) {
    for (let i in cardLengthSelected.value) {
      if (
        cardLengthSelected.value[i].id == lengthItem.id &&
        !lengthItem.selected
      ) {
        cardLengthSelected.value.splice(Number(i), 1)
      }
    }
  }
}
watch(cardLengthSelected.value, (val) => {
  if (val.length == 0) {
    for (let item of modalList.value) {
      item.isAvailable = true
    }
  }
})
watch(modalSelected.value, (val) => {
  if (val.length == 0) {
    for (let item of lengthList.value) {
      item.isAvailable = true
    }
  }
})
const submit = () => {
  if (cardLengthSelected.value.length == 0) {
    uni.showToast({
      title: '请选择车长',
      icon: 'none',
    })
    return
  }
  if (modalSelected.value.length == 0) {
    uni.showToast({
      title: '请选择车型',
      icon: 'none',
    })
    return
  }
  let vehicleLength = cardLengthSelected.value
    .map((item) => item.carTypeNames)
    .filter(Boolean)
    .join(',')
  const vehicleLengthId = cardLengthSelected.value
    .map((item) => item.id)
    .filter(Boolean)
    .join(',')
  let vehicleType = modalSelected.value
    .map((item) => item.typename)
    .filter(Boolean)
    .join(',')
  const vehicleTypeId = modalSelected.value
    .map((item) => item.id)
    .filter(Boolean)
    .join(',')
  try {
    // 获取当前页面栈
    const pages = getCurrentPages()
    if (pages.length > 1) {
      // 获取当前页面实例，页面栈最后一个元素为当前页面
      const currentPage = pages[pages.length - 1]
      // 从页面实例获取事件通道
      const eventChannel = currentPage.$vm.getOpenerEventChannel()
      if (eventChannel && typeof eventChannel.emit === 'function') {
        // 发送数据
        eventChannel.emit('acceptDataFromChild', {
          vehicleLength: vehicleLength,
          vehicleLengthId: vehicleLengthId,
          vehicleType: vehicleType,
          vehicleTypeId: vehicleTypeId,
        })
      } else {
        console.warn('事件通道对象不可用或 emit 方法不存在')
      }
    } else {
      console.warn('页面栈中没有上一页，无法获取事件通道')
    }
  } catch (error) {
    console.error('获取事件通道时出错:', error)
  }
  // 返回上一页
  uni.navigateBack()
  /*uni.showToast({
		title: '通过',
		icon: 'none'
	});*/
}
const getModelAll = async () => {
  const result: any = await getModel()
  uni.hideLoading()
  result.result.forEach((item: any) => {
    item.isAvailable = true
    item.selected = false
  })
  modalList.value = result.result
}
const getCarLengthListAll = async () => {
  const result: any = await getCarLengthList()
  uni.hideLoading()
  result.result.forEach((item: any) => {
    item.isAvailable = true
    item.selected = false
  })
  lengthList.value = result.result
}
onShow(() => {
  getModelAll()
  getCarLengthListAll()
})
</script>

<style scoped lang="scss">
.icon-owner-a-fanhuizuojiantou {
  color: #000;
  font-size: $uni-font-size-big;
}
.main-container {
  width: 100%;
  position: relative;
  top: v-bind(headerHeight);
  padding: 0rpx $uni-padding-left-right 0rpx $uni-padding-left-right;
  box-sizing: border-box;
  .card-length {
    display: flex;
    flex-direction: column;
    padding: 20rpx 0px 30rpx 0rpx;
    .label-left {
      margin-bottom: 10rpx;
      .symbol {
        color: red;
        margin-right: 4rpx;
      }
      .label {
        color: #000;
        font-size: $uni-font-size-lg;
        font-weight: bold;
      }
      .text {
        font-size: $uni-font-size-base;
        color: $uni-text-color-placeholder;
      }
    }
    .option-list {
      display: flex;
      flex-wrap: wrap;
      .option-item {
        width: 146rpx;
        height: 58rpx;
        text-align: center;
        line-height: 58rpx;
        background-color: $uni-bg-color-grey;
        color: $uni-text-color-placeholder;
        font-size: $uni-font-size-lg;
        margin-right: 16rpx;
        margin-top: 16rpx;
        border-radius: 10rpx;
        border: 1rpx solid $uni-bg-color-grey;
        &:nth-child(4n) {
          margin-right: 0rpx;
        }
      }
      .option-item-active {
        background-color: #ffe4b5;
        color: #ff8c00;
        border: 1rpx solid #ff8c00;
      }
    }
  }
}
.submit-box {
  width: 100%;
  padding: 20rpx 30rpx 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  position: fixed;
  left: 0;
  bottom: 0;

  .btn {
    width: 100%;
    height: 100rpx;
    text-align: center;
    line-height: 100rpx;
    background-color: #ed782f;
    color: #fff;
    font-size: $uni-font-size-lg;
    border-radius: 20rpx;
  }
}
</style>
