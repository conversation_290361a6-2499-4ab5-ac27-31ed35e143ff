<template>
  <view>
    <view class="address-from-to">
      <view class="from-to">
        <view class="switch-region">
          <view class="dress">装</view>
          <view class="iconfont icon-owner-qiehuan" @tap="switchAddress"></view>
          <view class="unload-text">卸</view>
        </view>
        <view class="from-to-box">
          <view class="from-box" @tap="editFromAddress">
            <view class="empty-adderss" v-if="!formData.shipmentsAddress?.detail?.name">去哪儿装货</view>
            <view class="big-address" v-if="formData.shipmentsAddress?.detail?.name">
              {{ formData.shipmentsAddress?.addressComponent?.province }}
              {{ formData.shipmentsAddress?.addressComponent?.city.length > 0 ? formData.shipmentsAddress?.addressComponent?.city : '' }}
              {{ formData.shipmentsAddress?.addressComponent?.district }}
            </view>
            <view class="small-address" v-if="formData.shipmentsAddress?.detail?.name">{{ formData.shipmentsAddress?.detail?.name }}</view>
          </view>
          <view class="from-box to-box" @tap="editToAddress">
            <view class="empty-adderss" v-if="!formData.placeAddress?.detail?.name">去哪儿卸货</view>
            <view class="big-address" v-if="formData.placeAddress?.detail?.name">
              {{ formData.placeAddress?.addressComponent?.province }}
              {{ formData.placeAddress?.addressComponent?.city.length > 0 ? formData.placeAddress?.addressComponent?.city : '' }}
              {{ formData.placeAddress?.addressComponent?.district }}
            </view>
            <view class="small-address" v-if="formData.placeAddress?.detail?.name">{{ formData.placeAddress?.detail?.name }}</view>
          </view>
        </view>
      </view>
      <view class="summarize">
        <view class="left">一装一卸</view>
        <uni-tooltip placement="top">
          <view class="iconfont icon-owner-dingwei"></view>
          <view class="text">预估总里程约{{ estimatedDistance }}公里</view>
          <view class="iconfont icon-owner-tishi"></view>
          <template #content>根据所选车型车长，智能计算路线，仅供参考</template>
        </uni-tooltip>
      </view>
    </view>
    <view class="cargo-info">
      <view class="info-item">
        <view class="item-left">
          <text class="symbol">*</text>
          <text class="label">货物信息</text>
        </view>
        <view class="item-right" @tap="infoClick(1)">
          <text class="value ellipsis" v-if="formData.cargo">{{ formData.cargo }}</text>
          <text class="value-empty" v-if="!formData.cargo">必填，请输入货物信息</text>
          <text class="iconfont icon-owner-youjiantou"></text>
        </view>
      </view>
      <!--			<view class="info-item">
              <view class="item-left">
                <text class="symbol">*</text>
                <text class="label">用车类型</text>
              </view>
              <view class="item-right">
                <text class="use-car-type">
                  <text class="type" :class="item.used ? 'type-active' : ''" v-for="(item, index) in useCarType" :key="index" @tap="clickCarType(index)">{{ item.text }}</text>
                </text>
              </view>
            </view>-->
      <view class="info-item">
        <view class="item-left">
          <text class="symbol">*</text>
          <text class="label">车长车型</text>
        </view>
        <view class="item-right" @tap="editModal">
          <text class="value ellipsis" v-if="formData.model">{{ formData.model }}</text>
          <text class="value-empty" v-if="!formData.model">必填，请选择车型</text>
          <text class="iconfont icon-owner-youjiantou"></text>
        </view>
      </view>
      <view class="info-item">
        <view class="item-left">
          <text class="symbol">*</text>
          <text class="label">装货时间</text>
        </view>
        <view class="item-right" @tap="editTime">
          <text class="value ellipsis" v-if="formData.time">{{ formData.time }}</text>
          <text class="value-empty" v-if="!formData.time">必填，请选择装货时间</text>
          <text class="iconfont icon-owner-youjiantou"></text>
        </view>
      </view>
      <view class="info-item">
        <view class="item-left">
          <text class="label">备注</text>
        </view>
        <view class="item-right" @tap="editRemarks">
          <text class="value ellipsis" v-if="formData.remarks">{{ formData.remarks }}</text>
          <text class="value-empty" v-if="!formData.remarks">非必填，请填写备注</text>
          <text class="iconfont icon-owner-youjiantou"></text>
        </view>
      </view>
      <view class="deposit-box">
        <view class="deposit-text">订金（司机付）</view>
        <view class="deposit-con">
          <view class="deposit-amount">
            <up-input class="price-input" :maxlength="7" type="number" placeholder="50" border="none" v-model="formData.depositAmount"></up-input>
            <text class="unit">元</text>
          </view>
          <view class="deposit-options">
            <button
              class="option-btn"
              :class="{ 'active': formData.depositRefundType === 'refund' }"
              @click="reback"
            >
              退还
            </button>
            <button
              class="option-btn"
              :class="{ 'active': formData.depositRefundType === 'no_refund' }"
              @click="noReback"
            >
              不退还
            </button>
          </view>
        </view>
        <view class="deposit-tips">
          <text class="tips-text">司机支付订货信息费，订单完成后转入给货主</text>
          <text class="view-process" @click="viewProcess">查看流程</text>
        </view>
      </view>
    </view>
    <view class="price-box">
      <up-radio-group v-model="formData.priceRaido">
        <view class="radio-item-box" :class="formData.priceRaido == 1 ? 'radio-item-box-active' : ''" @tap="priceRadioClick(1)">
          <view class="radio-item">
            <view class="radio-item-left">
              <image src="../../../static/images/yikoujia.png"></image>
              <view class="title-desc">
                <view class="title">一口价</view>
                <view class="desc">免议价，更省心</view>
              </view>
            </view>
            <view class="radio-item-right">
              <view class="price-content" :style="{ background: formData.priceRaido == 1 ? '#fff' : '' }" @tap="priceClick(1)">
                {{ formData.bidAmount ? formData.bidAmount + '元/' + formData.bidType : '请输入价格' }}
              </view>
              <up-radio activeColor="#ee7942" :name="1"></up-radio>
            </view>
          </view>
          <!--					<view class="price-tips-box" v-if="formData.priceRaido == 1">
                      <view class="price-tips">
                        <text class="iconfont icon-owner-tishi1"></text>
                        <text>存在多处名称相似的不同地点，请仔细确认</text>
                      </view>
                    </view>-->
        </view>
        <!--				<view class="radio-item-box" :class="formData.priceRaido == 2 ? 'radio-item-box-active' : ''" @tap="priceRadioClick(2)">
                  <view class="radio-item">
                    <view class="radio-item-left">
                      <image src="../../../static/images/dianhua.png"></image>
                      <view class="title-desc">
                        <view class="title">电话议价</view>
                        <view class="desc">直联司机，价格可议</view>
                      </view>
                    </view>
                    <view class="radio-item-right">
                      <view class="price-content" :style="{ background: formData.priceRaido == 2 ? '#fff' : '' }" @tap="priceClick(2)">
                        {{ formData.bargainingPrice ? formData.bargainingPrice + '元/' + formData.unitText : '请输入价格' }}
                      </view>
                      <up-radio activeColor="#ee7942" :name="2"></up-radio>
                    </view>
                  </view>
                  <view class="price-tips-box" v-if="formData.priceRaido == 2">
                    <view class="price-tips">
                      <text class="iconfont icon-owner-tishi1"></text>
                      <text>存在多处名称相似的不同地点，请仔细确认</text>
                    </view>
                  </view>
                </view>-->
      </up-radio-group>
    </view>
    <!-- <view class="agreement-rule">
      <up-checkbox shape="circle" :labelDisabled="'true'" activeColor="#ee7942" name="agree" usedAlone v-model:checked="formData.aloneChecked">
        <template #label>
          <text class="text" @tap.stop>
            我已阅读并同意
            <text class="highlight">《货物运输协议》</text>
          </text>
        </template>
      </up-checkbox>
    </view> -->
    <view class="check-price-btn">
      <view class="price-submit" @click="handleSubmit">立即发货</view>
    </view>
    <!-- <view class="more">更多服务-测试样式用</view> -->
    <uni-popup ref="timeRef" background-color="#fff" class="global-popup time-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="title-box">
          <text class="btn cancel" @tap="timeCancel">取消</text>
          <text class="title">装货时间</text>
          <text class="btn confirm" @tap="timeConfirm">确定</text>
        </view>
        <view class="choose-tips">
          <text class="iconfont icon-owner-tishi1"></text>
          <text>填写准确装货时间，可防司机误抢哦</text>
        </view>
        <view class="date-list">
          <view class="date-item" :class="item.selected ? 'date-item-active' : ''" v-for="(item, index) in dayList" :key="index" @tap="dateClick(index)">
            <text class="text">{{ item.text }}</text>
            <text class="date">{{ item.date }}</text>
          </view>
          <view class="date-item" :style="{ background: 'transparent', border: 'none' }"></view>
        </view>
        <view class="time-interval">
          <view class="start-time" @tap="handleTime(1)">{{ timeInfo.startTime ? timeInfo.startTime : '开始时间' }}</view>
          <view class="start-time end-time" @tap="handleTime(2)">{{ timeInfo.endTime ? timeInfo.endTime : '结束时间' }}</view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="remarksRef" background-color="#fff" class="global-popup remarks-input-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="title-box">
          <text class="btn cancel" @tap="remarksCancel">取消</text>
          <text class="title">服务要求和备注</text>
          <text class="btn confirm" @tap="remarksConfirm">确定</text>
        </view>
        <view class="textarea-box">
          <up-textarea
              class="custom-textarea"
              height="260rpx"
              v-model="remarksVal"
              placeholder="您可以备注用车需求、装卸方式、特殊要求,如三不超"
              :count="true"
              :maxlength="100"
          ></up-textarea>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="priceRef" background-color="#fff" class="global-popup price-input-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
      <view class="popup-content">
        <view class="title-box">
          <text class="btn cancel" @tap="pirceCancel">取消</text>
          <text class="title">我的出价</text>
          <text class="btn confirm" @tap="priceConfirm">确定</text>
        </view>
        <view class="tip-capacity">
          <image src="../../../static/images/up.png" mode=""></image>
          <text class="main-text">运力充足</text>
          <view class="line"></view>
          <view class="second-text">当前线路司机较多，发货更划算</view>
        </view>
        <view class="count-amount-box">
          <view class="count-amount">
            <up-input class="price-input" :maxlength="7" type="number" placeholder="必填,请输入总价" border="none" v-model="inputPrice" @change="inputPriceChange"></up-input>
            <view class="count-amount-right">
              <text class="text">元/</text>
              <view class="unit-list">
                <view class="unit-item" :class="item.selected ? 'unit-item-active' : ''" v-for="(item, index) in unitList" :key="index" @tap="unitClick(index)">
                  {{ item.text }}
                </view>
              </view>
            </view>
          </view>
          <view class="total-freight">总运费1000元。</view>
        </view>
      </view>
    </uni-popup>
    <up-datetime-picker
        :show="timeInfo.show"
        v-model="timeInfo.currentValue"
        mode="time"
        :minHour="timeInfo.timeMinHour"
        :maxHour="timeInfo.timeMaxHour"
        :minMinute="timeInfo.timeMinMinute"
        :maxMinute="timeInfo.timeMaxMinute"
        @confirm="handleTimeConfirm"
        @cancel="timeInfo.show = false"
    ></up-datetime-picker>

    <!-- 订金流程弹窗 -->
    <uni-popup ref="processPopup" type="center" :mask-click="true">
      <view class="process-dialog">
        <!-- 流程Tab切换 -->
        <view class="process-tabs">
          <view
            class="tab-item"
            :class="{ 'active': activeProcessTab === 'refund' }"
            @click="switchProcessTab('refund')"
          >
            退还流程
          </view>
          <view
            class="tab-item"
            :class="{ 'active': activeProcessTab === 'no_refund' }"
            @click="switchProcessTab('no_refund')"
          >
            不退还流程
          </view>
        </view>

        <!-- 流程步骤列表 -->
        <view class="process-steps">
          <view
            class="step-item"
            v-for="(step, index) in currentProcessSteps"
            :key="index"
          >
            <view class="step-number">{{ index + 1 }}</view>
            <text class="step-text">{{ step }}</text>
          </view>
        </view>

        <!-- 额外说明 -->
        <view class="process-note" v-if="activeProcessTab === 'no_refund'">
          <text class="note-text">若货主未点击"确认完成"，平台将在货物送达后7天内将订金退还给司机</text>
        </view>

        <!-- 确认按钮 -->
        <view class="process-footer">
          <button class="confirm-btn" @click="closeProcessDialog">我知道了</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { onShow } from '@dcloudio/uni-app';
import {nextTick, ref, watch, computed} from 'vue';
import dayjs from 'dayjs';
import { getStoragetItem, clearStorage } from '@/utils/storage';
import { editOrderInfo } from '../../../api/transport';

// clearStorage()

// 滚动到指定锚点
// const scrollToAnchor = (anchor: any) => {
// 	const query = uni.createSelectorQuery().in(this);
// 	query
// 		.select('#' + anchor)
// 		.boundingClientRect((data: any) => {
// 			if (data) {
// 				uni.pageScrollTo({
// 					scrollTop: data.top,
// 					duration: 300 // 动画时长，默认300ms
// 				});
// 			}
// 		})
// 		.exec();
// };
const handleSubmit = async () => {
  // if (!formData.value.aloneChecked) {
  //   uni.showToast({
  //     title: '请阅读并同意货物运输协议',
  //     icon: 'none'
  //   });
  //   return;
  // }
  formData.value.shipmentsAddress = JSON.stringify(formData.value.shipmentsAddress);
  formData.value.placeAddress = JSON.stringify(formData.value.placeAddress);
  formData.value.shippersId = getStoragetItem('userInfo').id;
  const result: any = await editOrderInfo(formData.value);
  uni.hideLoading();
  if (result.code == 700) {
    uni.showToast({
      title: '发货成功',
      icon: 'success',
      duration: 2000
    });
  } else {
    uni.showToast({
      title: result.msg || '发货失败',
      icon: 'none',
      duration: 2000
    });
  }
  console.log(JSON.stringify(result))
};
const tabbarHpx: any = ref('0rpx');
// 装卸地切换
const switchAddress = () => {};

// 编辑装货地址
const editFromAddress = () => {
  uni.navigateTo({
    url: '/pageCom/transport/edit_address?addressType=1'
  });
};
// 编辑卸货地址
const editToAddress = () => {
  uni.navigateTo({
    url: '/pageCom/transport/edit_address?addressType=2'
  });
};
const formData: any = ref({
  loadingAddressName: '', //装货地名称
  loadingLongitude: '', //装货地址经度
  loadingLatitude: '', //装货地址纬度
  unloadingAddressName: '', //卸货地址名称
  unloadingLongitude: '', //卸货地址经度
  unloadingLatitude: '', //卸货地址纬度
  cargoName: '', //货物名称
  packagingMethod: '', //包装方式
  weightUnit: '', //重量单位
  minWeight: 0, //最小重量
  maxWeight: 0, //最大重量
  minVolume: 0, //最小体积
  maxVolume: 0, //最大体积
  requiredVehicles: 1, //需要车辆数
  temperatureControl: 0, //温控范围
  vehicleLength: '', //车长
  occupiedLength: 0, //占用车长
  vehicleType: '', //车型
  bidType: '', //货主出价类型
  bidAmount: 0, //货主出价金额
  depositAmount: 0, //押金金额
  depositRefundType: 'refund', //订金退还类型：refund-退还，no_refund-不退还
  loadingDate: '', //装货日期
  earliestLoadingTime: '', //最早装货时间
  latestLoadingTime: '', //最晚装货时间
  remarks: '', //备注

  cargo: '',
  model: '', //车长类型
  time: '', //日期时间
  timeId: 0, //日期id
  priceRaido: 1, //当前选择价格方式
  price: '', //一口价
  priceUnit: 0, //一口价单位
  bargainingPrice: '', //电话议价
  bargainingPriceUnit: 0, //电话议价单位
  unitText: '', //运输单位
  // aloneChecked: true //协议与规则
});
const useCarType = ref([
  {
    id: 1,
    text: '整车',
    used: true
  },
  {
    id: 2,
    text: '拼车',
    used: false
  }
]);
const clickCarType = (index: any) => {
  for (let item of useCarType.value) {
    item.used = false;
  }
  useCarType.value[index].used = true;
  formData.value.carType
};
const receiveCargoInfo = (params: any) => {
  formData.value.cargo = params.name;
};
defineExpose({
  receiveCargoInfo
});
const infoClick = (num: number) => {
  if (num == 1) {
    uni.navigateTo({
      url: '/pageCom/transport/cargo_info',
      events: {
        acceptDataFromChild: (data) => {
          console.log('收到来自子页面的数据:', JSON.stringify(data));
          formData.value.cargo = data.cargoInfo.name;
          formData.value.cargoName = data.cargoInfo.name;
          formData.value.packagingMethod = data.cargoInfo.packageType;
          formData.value.minWeight = data.cargoInfo.weightStart;
          formData.value.maxWeight = data.cargoInfo.weightEnd;
          formData.value.minVolume = data.cargoInfo.volumeStart;
          formData.value.maxVolume = data.cargoInfo.volumeEnd;
          formData.value.requiredVehicles = data.cargoInfo.cardNum;
          formData.value.temperatureControl = data.cargoInfo.temperature;
        }
      },
    });
  }
};
const editModal = () => {
  uni.navigateTo({
    url: '/pageCom/transport/vehicle_model',
    events: {
      acceptDataFromChild: (data) => {
        console.log('收到来自子页面的数据:', JSON.stringify(data));
        formData.value.vehicleLength = data.vehicleLength;
        formData.value.vehicleLengthId = data.vehicleLengthId;
        formData.value.vehicleType = data.vehicleType;
        formData.value.vehicleTypeId = data.vehicleTypeId;
        formData.value.model = data.vehicleLength + '-' + data.vehicleType;

      }
    },
  });
};
const timeRef = ref(null);
const editTime = () => {
  timeRef.value.open('bottom');
};
const timeCancel = () => {
  timeRef.value.close();
};
const timeConfirm = () => {
  if (!timeInfo.value.startTime || !timeInfo.value.endTime) {
    uni.showToast({
      title: '请选择装货时间',
      icon: 'none'
    });
    return;
  }
  const day = dayList.value.filter((item) => item.selected)[0];
  formData.value.loadingDate = day.date;
  formData.value.earliestLoadingTime = timeInfo.value.startTime;
  formData.value.latestLoadingTime = timeInfo.value.endTime;
  /*  if (day.text == '今明两天均可') {
      formData.value.latestLoadingTime = dayjs().startOf('day').add(1, 'day').format('YYYY-MM-DD')+ ' ' + timeInfo.value.endTime;
    }else {
      formData.value.latestLoadingTime = day.date+ ' ' + timeInfo.value.endTime;
    }*/
  formData.value.time = day.text + ' ' + day.date + ' ' + timeInfo.value.startTime + '-' + timeInfo.value.endTime;
  timeRef.value.close();
};
const dayList = ref([]);
const timeInfo = ref({
  startTime: '',
  endTime: '',
  show: false,
  currentValue: '',
  timeMinHour: 0,
  timeMaxHour: 23,
  timeMinMinute: 0,
  timeMaxMinute: 59
});

const initDayList = () => {
  const days = [];
  const today = dayjs().startOf('day');
  days[0] = {
    date: today.format('YYYY-MM-DD'),
    text: '今天'
  };
  /*	days[1] = {
      date: today.format('YYYY-MM-DD'),
      text: '今明两天均可'
    };*/
  for (let i = 1; i <= 6; i++) {
    const obj = {
      date: today.add(i, 'day').format('YYYY-MM-DD'),
      text: i == 1 ? '明天' : i == 2 ? '后天' : ''
    };
    days.push(obj);
  }
  for (let i in days) {
    let index = Number(JSON.parse(JSON.stringify(i)));
    days[i].id = ++index;
    days[i].selected = false;
    if (Number(i) == 0) {
      days[i].selected = true;
    }
  }
  dayList.value = days;
};
const dateClick = (index: any) => {
  dayList.value.forEach((item, _index) => {
    item.selected = false;
  });
  dayList.value[index].selected = true;
};
let startEnd = 1; //1:开始时间  2:结束时间
const handleTime = (num: any) => {
  startEnd = num;
  if (num == 1) {
    const id = dayList.value.filter((item) => item.selected)[0].id;
    // 如果选择是今天
    if (id == 1) {
      timeInfo.value.timeMinHour = dayjs().hour();
    } else {
      timeInfo.value.timeMinHour = 0;
    }
    setTimeout(() => {
      timeInfo.value.currentValue = timeInfo.value.startTime;
    }, 100);
  } else if (num == 2) {
    if (!timeInfo.value.startTime) {
      uni.showToast({
        title: '请选择开始时间',
        icon: 'none'
      });
      return;
    }
    const startArr = timeInfo.value.startTime.split(':');
    timeInfo.value.timeMinHour = Number(startArr[0]);
    setTimeout(() => {
      timeInfo.value.currentValue = timeInfo.value.endTime;
    }, 100);
  }
  timeInfo.value.show = true;
};
const handleTimeConfirm = (e: any) => {
  if (startEnd == 1) {
    timeInfo.value.startTime = e.value;
    timeInfo.value.endTime = '';
  } else if (startEnd == 2) {
    timeInfo.value.endTime = e.value;
  }
  timeInfo.value.show = false;
};
const remarksRef = ref();
const remarksVal = ref('');
const editRemarks = () => {
  remarksVal.value = formData.value.remarks;
  remarksRef.value.open('bottom');
};
const remarksCancel = () => {
  remarksRef.value.close();
};
const remarksConfirm = () => {
  formData.value.remarks = remarksVal.value;
  remarksRef.value.close();
};
const priceRadioClick = (num: any) => {
  formData.value.priceRaido = num;
};
const priceRef = ref();
let priceTypeNum = 1;
const priceClick = (num: any) => {
  priceTypeNum = num;
  inputPrice.value = '';
  for (let item of unitList.value) {
    item.price = null;
    item.selected = false;
    if (num == 1) {
      if (formData.value.priceUnit == item.id) {
        inputPrice.value = formData.value.price;
        item.price = formData.value.price;
        item.selected = true;
      }
    } else if (num == 2) {
      if (formData.value.bargainingPriceUnit == item.id) {
        inputPrice.value = formData.value.bargainingPrice;
        item.price = formData.value.bargainingPrice;
        item.selected = true;
      }
    }
  }
  const selectUnitPrice = unitList.value.filter((item) => item.selected)[0];
  if (!selectUnitPrice) {
    unitList.value[0].selected = true;
  }
  setTimeout(() => {
    priceRef.value.open('bottom');
  }, 700);
};
const pirceCancel = () => {
  priceRef.value.close();
};
const priceConfirm = () => {
  const selectUnitPrice = unitList.value.filter((item) => item.selected)[0];
  if (!selectUnitPrice.price) {
    uni.showToast({
      title: '请输入价格',
      icon: 'none'
    });
    return;
  }
  if (priceTypeNum == 1) {
    formData.value.bidAmount = selectUnitPrice.price;
    formData.value.priceUnit = selectUnitPrice.id;
  } else if (priceTypeNum == 2) {
    formData.value.bargainingPrice = selectUnitPrice.price;
    formData.value.bargainingPriceUnit = selectUnitPrice.id;
  }
  formData.value.bidType = selectUnitPrice.text;

  priceRef.value.close();
};
const unitList = ref([
  {
    id: 1,
    text: '趟',
    price: null,
    selected: false
  },
  {
    id: 2,
    text: '吨',
    price: null,
    selected: false
  },
  {
    id: 3,
    text: '方',
    price: null,
    selected: false
  }
]);
const inputPrice = ref();
let unitIndex = 0;
const unitClick = (index: any) => {
  unitIndex = index;
  for (let item of unitList.value) {
    item.selected = false;
  }
  unitList.value[index].selected = true;
  inputPrice.value = unitList.value[index].price || '';
};
const inputPriceChange = (value: any) => {
  unitList.value[unitIndex].price = value || null;
};
const initAddressInfo = () => {
  formData.value.shipmentsAddress = getStoragetItem('shipmentsAddress');
  formData.value.placeAddress = getStoragetItem('placeAddress');
  formData.value.loadingAddressName = formData.value.shipmentsAddress.detail.name;
  let location = formData.value.shipmentsAddress.addressComponent.streetNumber.location.split(',');
  formData.value.loadingLongitude = location[0];
  formData.value.loadingLatitude = location[1];
  formData.value.unloadingAddressName = formData.value.placeAddress.detail.name;
  let placeLocation = formData.value.placeAddress.addressComponent.streetNumber.location.split(',');
  formData.value.unloadingLongitude = placeLocation[0];
  formData.value.unloadingLatitude = placeLocation[1];
  console.log('formData.value.shipmentsAddress:', JSON.stringify(formData.value.shipmentsAddress));

  calculateDistance();

};

// 计算距离的函数
const estimatedDistance = ref(null);

// 订金流程弹窗相关
const processPopup = ref(null);
const activeProcessTab = ref('refund');

// 流程步骤数据
const processSteps = {
  refund: [
    '司机(订货时)支付订金到平台',
    '货主点击"确认完成"',
    '平台将订金转入给货主'
  ],
  no_refund: [
    '司机(订货时)支付订金到平台',
    '货主点击"确认完成"',
    '平台将订金还给司机'
  ]
};

// 当前显示的流程步骤
const currentProcessSteps = computed(() => {
  return processSteps[activeProcessTab.value] || [];
});
const calculateDistance = () => {
  const startPoint = formData.value.shipmentsAddress.addressComponent.streetNumber.location;
  const endPoint = formData.value.placeAddress.addressComponent.streetNumber.location;

  if (startPoint && endPoint) {
    const [startLng, startLat] = startPoint.split(',').map(Number);
    const [endLng, endLat] = endPoint.split(',').map(Number);

    const R = 6371; // 地球半径，单位：公里
    const dLat = (endLat - startLat) * (Math.PI / 180);
    const dLng = (endLng - startLng) * (Math.PI / 180);
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(startLat * (Math.PI / 180)) *
        Math.cos(endLat * (Math.PI / 180)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    estimatedDistance.value = distance.toFixed(1);
  }
};

// 点击订金退回
const reback = () => {
  formData.value.depositRefundType = 'refund';
  console.log('选择退还订金');
}

// 点击订金不退回
const noReback = () => {
  formData.value.depositRefundType = 'no_refund';
  console.log('选择不退还订金');
}

// 查看流程
const viewProcess = () => {
  console.log('查看订金流程');
  // 根据当前选择的退还类型设置默认tab
  activeProcessTab.value = formData.value.depositRefundType === 'refund' ? 'refund' : 'no_refund';
  processPopup.value.open();
}

// 切换流程tab
const switchProcessTab = (tab) => {
  activeProcessTab.value = tab;
}

// 关闭流程弹窗
const closeProcessDialog = () => {
  processPopup.value.close();
}
watch(
    [
      () => formData.value.shipmentsAddress?.addressComponent.streetNumber?.location,
      () => formData.value.placeAddress?.addressComponent.streetNumber?.location,
    ],
    calculateDistance
);
onShow(() => {
  // #ifdef H5
  tabbarHpx.value = uni.getSystemInfoSync().windowBottom * 2 + 'rpx';
  // #endif
  initDayList();
  // 处理装卸货地址信息
  initAddressInfo();
});
</script>

<style scoped lang="scss">
.address-from-to {
  background-color: #fff;
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
  padding: 26rpx 20rpx 0rpx 20rpx;
  box-sizing: border-box;

  .from-to {
    display: flex;

    .switch-region {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 20rpx;

      .dress {
        width: 50rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
        background-color: blue;
        border-radius: 10rpx;
        color: #fff;
        font-size: 28rpx;
      }

      .iconfont {
        width: 50rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
      }

      .unload-text {
        width: 50rpx;
        height: 50rpx;
        text-align: center;
        line-height: 50rpx;
        background-color: #ee7942;
        border-radius: 10rpx;
        color: #fff;
        font-size: 28rpx;
      }
    }

    .from-to-box {
      flex: 1;

      .from-box {
        width: 100%;
        padding: 20rpx;
        box-sizing: border-box;
        background-color: $uni-bg-color-grey;
        border-radius: 10rpx;

        .big-address {
          font-size: 32rpx;
          font-weight: bold;
        }

        .small-address {
          font-size: 28rpx;
        }
      }
    }

    .to-box {
      margin-top: 20rpx;
    }
  }

  .summarize {
    width: 100%;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: $uni-text-color;

    :deep(.uni-tooltip) {
      display: flex;
      align-items: center;

      .text {
        margin-left: 6rpx;
        margin-right: 6rpx;
      }

      .uni-tooltip-popup {
        min-width: 300rpx;
        font-size: 24rpx;
      }
    }
  }
}

.cargo-info {
  width: 100%;
  background-color: #fff;
  margin-top: 20rpx;
  border-radius: 10rpx;

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid $uni-text-color-grey;
    &:last-child {
      border-bottom: none;
    }

    .item-left {
      width: 180rpx;

      .symbol {
        color: red;
        margin-right: 4rpx;
      }

      .label {
        font-size: $uni-font-size-lg;
      }
    }

    .item-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .use-car-type {
        .type {
          padding: 10rpx 30rpx;
          background-color: $uni-bg-color-grey;
          color: $uni-text-color-placeholder;
          font-size: $uni-font-size-lg;
          margin-left: 10rpx;
          border-radius: 10rpx;
        }
        .type-active {
          background-color: #ffe4b5;
          color: #ff8c00;
          border: 1rpx solid #ff8c00;
        }
      }
      .value {
        width: 420rpx;
        font-size: $uni-font-size-lg;
        font-weight: bold;
        text-align: right;
      }
      .value-empty {
        font-size: $uni-font-size-lg;
        color: $uni-text-color-grey;
        text-align: right;
      }

      .iconfont {
        color: $uni-text-color-grey;
        font-size: $uni-font-size-base;
        margin-left: 6rpx;
      }
    }
  }
  .deposit-box {
    width: 100%;
    background-color: #fff;
    padding-bottom: 20rpx;
    border-radius:6rpx;
    .deposit-text {
      font-size: 32rpx;
      padding: 15rpx;
    }
    .deposit-con {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      width: 96%;
      margin-left: 2%;

      .deposit-amount {
        display: flex;
        align-items: center;
        background-color: $uni-bg-color-grey;
        border-radius: 10rpx;
        padding: 0 20rpx;
        height: 60rpx;

        :deep(.price-input) {
          flex: none;
          .u-input__content {
            padding: 0;
            box-sizing: border-box;
            width: 120rpx;
            flex: none;
            .u-input__content__field-wrapper {
              .u-input__content__field-wrapper__field {
                .uni-input-wrapper {
                  .uni-input-input {
                    font-size: 32rpx;
                    color: #333;
                    font-weight: bold;
                    text-align: left;
                  }
                }
              }
            }
          }
        }

        .unit {
          font-size: 28rpx;
          color: #333;
          margin-left: 8rpx;
        }
      }

      .deposit-options {
        display: flex;
        gap: 12rpx;

        .option-btn {
          padding: 8rpx 24rpx;
          height: 60rpx;
          line-height: 44rpx;
          background-color: #fff;
          border: 2rpx solid #ddd;
          border-radius: 8rpx;
          color: #666;
          font-size: 28rpx;
          transition: all 0.3s ease;

          &.active {
            background-color: #e84138;
            border-color: #e84138;
            color: #fff;
          }

          &:active {
            opacity: 0.8;
          }
        }
      }
    }

    .deposit-tips {
      margin: 20rpx 2% 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .tips-text {
        font-size: 24rpx;
        color: #999;
        flex: 1;
      }

      .view-process {
        font-size: 24rpx;
        color: #e84138;
        text-decoration: underline;
        margin-left: 16rpx;

        &:active {
          opacity: 0.7;
        }
      }
    }
  }
}
.check-price-btn {
  padding: 30rpx 0rpx;
  box-sizing: border-box;
  position: sticky;
  left: 0rpx;
  /* 底部距离 = 基础距离100rpx + 底部安全区域 */
  bottom: calc(90rpx + env(safe-area-inset-bottom));
  z-index: 1;
  background-color: $uni-bg-page;
  margin-top: 20rpx;

  .price-submit {
    width: 100%;
    height: 90rpx;
    text-align: center;
    line-height: 90rpx;
    background-color: #ff8c00;
    font-size: $uni-font-size-big;
    color: #fff;
    font-weight: bold;
    border-radius: 40rpx;
  }
}
.more {
  background-color: blue;
  padding: 40rpx 0rpx;
}
:deep(.time-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: v-bind(tabbarHpx);
    .popup-content {
      height: 700rpx;
      .title-box {
        border-bottom: none;
      }
      .choose-tips {
        display: flex;
        align-items: center;
        padding: 10rpx 20rpx;
        box-sizing: border-box;
        width: 100%;
        background-color: #ffe4b5;
        font-size: $uni-font-size-base;
        color: #ff8c00;
        border-radius: 10rpx;
        .iconfont {
          margin-right: 8rpx;
        }
      }
      .date-list {
        padding: 0rpx 30rpx;
        box-sizing: border-box;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        .date-item {
          width: 200rpx;
          height: 90rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          background-color: $uni-bg-color-grey;
          color: $uni-text-color-placeholder;
          font-size: $uni-font-size-base;
          margin-right: 10rpx;
          border-radius: 10rpx;
          margin-top: 10rpx;
          border: 1rpx solid $uni-bg-color-grey;
          &:nth-child(3n) {
            margin-right: 0rpx;
          }
        }
        .date-item-active {
          background-color: #ffe4b5;
          color: #ff8c00;
          border: 1rpx solid #ff8c00;
        }
      }
      .time-interval {
        display: flex;
        justify-content: center;
        margin-top: 30rpx;
        .start-time {
          width: 230rpx;
          height: 80rpx;
          text-align: center;
          line-height: 80rpx;
          background-color: $uni-bg-color-grey;
          color: $uni-text-color-placeholder;
          font-size: $uni-font-size-lg;
        }
        .end-time {
          margin-left: 40rpx;
        }
      }
    }
  }
}
:deep(.remarks-input-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: v-bind(tabbarHpx);
    .popup-content {
      height: 500rpx;
      .title-box {
        border-bottom: none;
      }
      .textarea-box {
        width: 100%;
        padding: 0 30rpx;
        box-sizing: border-box;
        margin-top: 20rpx;
        .custom-textarea {
          background-color: $uni-bg-color-grey;
          .u-textarea__field {
            .uni-textarea-wrapper {
              .uni-textarea-textarea {
                color: #000;
                font-size: $uni-font-size-lg;
                font-weight: bold;
              }
            }
          }
          .u-textarea__count {
            background-color: $uni-bg-color-grey !important;
          }
        }
      }
    }
  }
}
.price-box {
  width: 100%;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  .radio-item-box {
    width: 100%;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid $uni-text-color-grey;
    &:last-child {
      border-bottom: none;
    }
    .radio-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .radio-item-left {
        display: flex;
        align-items: center;
        image {
          width: 60rpx;
          height: 60rpx;
          margin-right: 16rpx;
        }
        .title-desc {
          display: flex;
          flex-direction: column;
          .title {
            font-size: $uni-font-size-lg;
            color: #000;
            font-weight: bold;
          }
          .desc {
            font-size: $uni-font-size-sm;
            color: $uni-text-color-placeholder;
          }
        }
      }
      .radio-item-right {
        display: flex;
        align-items: center;
        .price-content {
          width: 180rpx;
          height: 80rpx;
          text-align: right;
          line-height: 80rpx;
          margin-right: 20rpx;
          background-color: $uni-bg-color-grey;
          padding: 0rpx 20rpx;
          border-radius: 10rpx;
          font-size: $uni-font-size-base-30;
          color: $uni-text-color-placeholder;
        }
      }
    }
    .price-tips-box {
      margin-top: 20rpx;
      .price-tips {
        display: flex;
        align-items: center;
        padding: 10rpx 20rpx;
        box-sizing: border-box;
        width: 100%;
        background-color: #ffe4b5;
        font-size: $uni-font-size-base;
        color: #ff8c00;
        border-radius: 10rpx;
        border: 1rpx solid #ff8c00;

        .iconfont {
          margin-right: 8rpx;
        }
      }
    }
  }
  .radio-item-box-active {
    background-color: #ffe4b5;
    .radio-item-left {
      .title {
        color: #ff8c00;
      }
      .desc {
        color: #ff8c00;
      }
    }
  }
}
:deep(.price-input-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: v-bind(tabbarHpx);
    .popup-content {
      height: 700rpx;
      .title-box {
        border-bottom: none;
      }
      .tip-capacity {
        display: flex;
        align-items: center;
        background-color: #ffe4b5;
        padding: 10rpx 20rpx;
        box-sizing: border-box;
        image {
          width: 40rpx;
          height: 40rpx;
          margin-right: 8rpx;
        }
        .main-text {
          font-size: $uni-font-size-base;
          color: #ff8c00;
          font-weight: bold;
        }
        .line {
          width: 2rpx;
          height: 20rpx;
          background-color: #ff8c00;
          margin: 0 20rpx;
        }
        .second-text {
          font-size: $uni-font-size-sm;
          color: #f9ae3d;
        }
      }
      .count-amount-box {
        width: 100%;
        padding: 0rpx 30rpx;
        box-sizing: border-box;
        margin-top: 20rpx;
        .count-amount {
          width: 100%;
          height: 90rpx;
          background-color: $uni-bg-color-grey;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 10rpx;
          padding-right: 20rpx;
          box-sizing: border-box;
          .price-input {
            flex: none;
            .u-input__content {
              padding-left: 20rpx;
              box-sizing: border-box;
              width: 300rpx;
              flex: none;
              .u-input__content__field-wrapper {
                .u-input__content__field-wrapper__field {
                  .uni-input-wrapper {
                    .uni-input-input {
                      font-size: $uni-font-size-big;
                      color: #333;
                      font-weight: bold;
                    }
                  }
                }
              }
            }
          }
          .count-amount-right {
            display: flex;
            align-items: center;
            .text {
              font-size: $uni-font-size-base;
              color: #333;
            }
            .unit-list {
              display: flex;
              align-items: center;
              margin-left: 20rpx;
              .unit-item {
                width: 90rpx;
                height: 60rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid #fff;
                font-size: $uni-font-size-base;
                background-color: #fff;
                border-radius: 10rpx;
                margin-right: 10rpx;
                color: #333;
                &:last-child {
                  margin-right: 0rpx;
                }
              }
              .unit-item-active {
                color: #ff8c00;
                border-color: #ff8c00;
              }
            }
          }
        }
        .total-freight {
          font-size: $uni-font-size-base-30;
          color: #ff8c00;
          margin-top: 20rpx;
        }
      }
    }
  }
}
.agreement-rule {
  margin-top: 30rpx;
  .u-checkbox {
    display: flex;
    align-items: flex-start;
    .u-checkbox__icon-wrap {
      width: 36rpx;
      height: 36rpx;
    }
    .text {
      flex: 1;
      font-size: $uni-font-size-sm;
      .highlight {
        color: #ff8c00;
      }
    }
  }
}

// 订金流程弹窗样式
.process-dialog {
  width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;

  .process-tabs {
    display: flex;
    border-bottom: 1px solid #EBEEF5;

    .tab-item {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #666;
      position: relative;

      &.active {
        color: #e84138;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 4rpx;
          background-color: #e84138;
          border-radius: 2rpx;
        }
      }

      &:active {
        opacity: 0.7;
      }
    }
  }

  .process-steps {
    padding: 40rpx 30rpx 20rpx;

    .step-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .step-number {
        width: 40rpx;
        height: 40rpx;
        background-color: #e84138;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 500;
        margin-right: 20rpx;
        flex-shrink: 0;
        margin-top: 4rpx;
      }

      .step-text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        padding-top: 8rpx;
      }
    }
  }

  .process-note {
    padding: 0 30rpx 20rpx;

    .note-text {
      font-size: 24rpx;
      color: #999;
      line-height: 1.5;
      background-color: #FFF5F5;
      padding: 20rpx;
      border-radius: 8rpx;
      border-left: 4rpx solid #e84138;
    }
  }

  .process-footer {
    padding: 20rpx 30rpx 30rpx;

    .confirm-btn {
      width: 100%;
      height: 80rpx;
      background-color: #e84138;
      color: #fff;
      border: none;
      border-radius: 8rpx;
      font-size: 32rpx;
      font-weight: 500;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
