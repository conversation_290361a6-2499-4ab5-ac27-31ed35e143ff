<template>
  <view class="history">
    <view class="order-list">
      <scroll-view
        :scroll-y="true"
        refresher-background="#f3f3f3"
        :refresher-enabled="isLoadingTop"
        :refresher-triggered="trigger"
        @refresherrefresh="refresherrefresh"
        show-scrollbar="false"
      >
        <view
          class="order-item"
          v-for="item in allOrders"
          :key="item"
          @tap="goHistoryDetail(item)"
        >
          <view class="status-tag">{{
            checkOrderStatus(item.orderStatus)
          }}</view>
          <view class="address">
            <text>{{ formatAddressRegion(item.loadingAddress) }}</text>
            <view class="iconfont icon-owner-a-xiangyouji<PERSON><PERSON>"></view>
            <text>{{ formatAddressRegion(item.dischargeAddress) }}</text>
          </view>
          <view class="info">
            <!--
                        <view class="tag">一口价</view>
            -->
            <text>{{ formatCargoInfo(item) }}</text>
          </view>
          <view class="handle-box">
            <view class="time">{{ formatDateTime(item.orderTime) }}</view>
            <view class="btn-region">
              <!--
                            <view class="btn-span">存为常发</view>
              -->
              <!-- <view class="btn-span">再来一单</view> -->
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    <uni-load-more iconType="auto" :status="loadStatus"></uni-load-more>
  </view>
</template>

<script setup lang="ts">
import { onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { computed, onMounted, ref } from 'vue'
import { orderInfoPage } from '../../../api/transport'
import UniLoadMore from '../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'
import { getStoragetItem } from '@/utils/storage'
import { formatDateTime } from '@/utils/common'

// 格式化地址显示 - 只显示省市区
const formatAddressRegion = (addressData: any) => {
  if (!addressData) return '未知'

  // 如果是字符串，直接返回
  if (typeof addressData === 'string') {
    return addressData || '未知'
  }

  // 如果是对象，解析地址信息
  let province = ''
  let city = ''
  let district = ''

  // 尝试不同的数据结构
  if (addressData.addressComponent) {
    province = addressData.addressComponent.province || ''
    city = addressData.addressComponent.city || ''
    district = addressData.addressComponent.district || ''
  } else if (addressData.province || addressData.city || addressData.district) {
    province = addressData.province || ''
    city = addressData.city || ''
    district = addressData.district || ''
  } else {
    // 如果都没有，返回原始数据的字符串形式
    return String(addressData) || '未知'
  }

  // 直辖市判断：如果省份和城市相同，只显示城市
  const municipalities = ['北京市', '上海市', '天津市', '重庆市']
  const isDirectMunicipality = municipalities.includes(province) || province === city

  if (isDirectMunicipality) {
    // 直辖市格式：{城市}{区域}
    return `${city}${district}`.replace(/市$/, '') // 去掉城市末尾的"市"字
  } else {
    // 普通城市格式：{省份}{城市}{区域}
    return `${province}${city}${district}`
  }
}

// 格式化货物信息显示
const formatCargoInfo = (item: any) => {
  if (!item) return ''

  let infoArray = []

  // 1. 车长信息
  if (item.vehicleLength) {
    infoArray.push(`${item.vehicleLength}米`)
  }

  // 2. 车型信息
  if (item.vehicleType) {
    infoArray.push(item.vehicleType)
  }

  // 3. 重量范围处理
  const minWeight = item.minWeight
  const maxWeight = item.maxWeight

  if (minWeight && maxWeight && minWeight !== maxWeight) {
    // 有起始和结束，显示区间
    infoArray.push(`${minWeight}-${maxWeight}吨`)
  } else if (minWeight && minWeight > 0) {
    // 只有起始值
    infoArray.push(`${minWeight}吨`)
  } else if (maxWeight && maxWeight > 0) {
    // 只有结束值
    infoArray.push(`${maxWeight}吨`)
  }

  // 4. 体积范围处理
  const minVolume = item.minVolume
  const maxVolume = item.maxVolume

  if (minVolume && maxVolume && minVolume !== maxVolume) {
    // 有起始和结束，显示区间
    infoArray.push(`${minVolume}-${maxVolume}方`)
  } else if (minVolume && minVolume > 0) {
    // 只有起始值
    infoArray.push(`${minVolume}方`)
  } else if (maxVolume && maxVolume > 0) {
    // 只有结束值
    infoArray.push(`${maxVolume}方`)
  }

  // 5. 货物名称
  if (item.cargoName) {
    infoArray.push(item.cargoName)
  }

  // 6. 运费信息
  if (item.bidAmount) {
    const priceInfo = `¥${item.bidAmount}${item.bidType ? '/' + item.bidType : ''}`
    infoArray.push(priceInfo)
  }

  // 用空格连接所有信息
  return infoArray.join(' ')
}

// 是否开启下来刷新
const isLoadingTop = ref(true)
// 刷新状态
const trigger = ref(true)
// 下拉刷新被触发
const refresherrefresh = (e: any) => {
  trigger.value = true
  currentPage.value = 1
  try {
    fetchOrders()
  } catch (error) {
    console.error('刷新订单列表失败:', error)
  } finally {
    trigger.value = false
  }
}
onPageScroll((e) => {
  if (e.scrollTop == 0) {
    isLoadingTop.value = true
  } else if (e.scrollTop > 0) {
    isLoadingTop.value = false
  }
})

// more、loading、no-more
const loadStatus = ref('loading')

// 当前页码
const currentPage = ref(1)
// 每页数量
const pageSize = ref(10)
// 所有订单列表
const allOrders = ref<any[]>([])
// 计算属性，筛选出已完成订单
const completedOrders = computed(() =>
  allOrders.value.filter((order) => order.status === 1)
)

// 获取订单列表
const fetchOrders = async () => {
  try {
    const response: any = await orderInfoPage({
      page: currentPage.value,
      limit: pageSize.value,
      shippersId: getStoragetItem('userInfo').id,
      orderStatus: '1,2,3,4,5,6,7,8,9,10,98,99', //已完成
    })
    uni.hideLoading()
    if (response.code === 700) {
      if (currentPage.value === 1) {
        allOrders.value = response.result || []
      } else {
        allOrders.value = [...allOrders.value, ...(response.result || [])]
      }
      if (!response.result || response.result.length < pageSize.value) {
        loadStatus.value = 'no-more'
      } else {
        loadStatus.value = 'more'
      }
    } else {
      console.error('获取订单列表失败，错误码:', response.code)
      loadStatus.value = 'more'
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    loadStatus.value = 'more'
  }
}
onReachBottom(async () => {
  if (loadStatus.value === 'more') {
    currentPage.value++
    await fetchOrders()
  }
})
// 详情页
const goHistoryDetail = (item: any) => {
  if (
    item.orderStatus == 10 ||
    item.orderStatus == 98 ||
    item.orderStatus == 99
  ) {
    uni.navigateTo({
      url: '/pageCom/transport/history_detail',
      success: (res) => {
        // 使用 eventChannel 发送数据
        res.eventChannel.emit('orderId', { id: item.id })
      },
    })
    return
  } else {
    uni.navigateTo({
      url: '/pageCom/orders/completed',
      success: (res) => {
        // 使用 eventChannel 发送数据
        res.eventChannel.emit('orderId', { id: item.id })
      },
    })
  }
}

// 存为常发
const saveAsRegular = (item: any) => {
  // 实现存为常发逻辑
  console.log('存为常发:', item)
}

// 再来一单
const orderAgain = (item: any) => {
  // 实现再来一单逻辑
  console.log('再来一单:', item)
}

// 检查订单状态
const checkOrderStatus = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '已接单',
    2: '改价待确认',
    3: '改价已确认',
    4: '改价已拒绝',
    5: '到达装货地',
    6: '装货中',
    7: '运输中',
    8: '到达卸货地',
    9: '已完成待确认',
    10: '已完成',
    99: '已完成',
    98: '已取消',
  }
  return statusMap[status] || '未知状态'
}

onMounted(async () => {
  await fetchOrders()
})
</script>

<style scoped lang="scss">
.history {
  padding-bottom: 60rpx;
}
.order-list {
  width: 100%;
  .order-item {
    width: 100%;
    padding: 0rpx 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    position: relative;
    margin-top: 20rpx;
    border-radius: 10rpx;
    .status-tag {
      background-color: #eeeeee;
      color: $uni-text-color-grey;
      position: absolute;
      right: 0;
      top: 0;
      padding: 2rpx 20rpx;
      box-sizing: border-box;
      border-bottom-left-radius: 10rpx;
      border-top-right-radius: 10rpx;
      font-size: $uni-font-size-sm;
    }
    .address {
      display: flex;
      align-items: center;
      padding-top: 42rpx;
      text {
        font-size: 32rpx;
        font-weight: 600;
      }
      .iconfont {
        font-weight: 700;
        margin: 0 20rpx;
      }
    }
    .info {
      display: flex;
      align-items: center;
      padding: 20rpx 0rpx 30rpx 0rpx;
      border-bottom: 2rpx solid $uni-text-color-grey;
      .tag {
        padding: 0rpx 4rpx;
        box-sizing: border-box;
        color: $uni-text-color-light;
        border: 2rpx solid $uni-text-color-light;
        border-radius: 6rpx;
      }
      text {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
      }
    }
    .handle-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0rpx 32rpx 0rpx;
      .time {
        font-size: $uni-font-size-base;
        color: $uni-text-color-grey;
      }
      .btn-region {
        display: flex;
        .btn-span {
          padding: 10rpx 24rpx;
          box-sizing: border-box;
          margin-left: 20rpx;
          border: 2rpx solid $uni-border-color;
          border-radius: 10rpx;
          margin-left: 20rpx;
        }
      }
    }
  }
}
</style>
