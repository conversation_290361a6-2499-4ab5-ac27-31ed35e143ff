<template>
  <view class="invoice">
    <view class="order-list">
      <scroll-view
        :scroll-y="true"
        refresher-background="#f3f3f3"
        :refresher-enabled="isLoadingTop"
        :refresher-triggered="trigger"
        @refresherrefresh="refresherrefresh"
        show-scrollbar="false"
      >
        <view
          class="order-item"
          v-for="item in orderList"
          :key="item.id"
          @tap="goPendingDetail(item)"
        >
          <view class="position-price">
            <view class="position">
              <text class="start-position">{{
                formatAddress(item.loadingAddress)
              }}</text>
              <view class="iconfont icon-owner-a-xiangyoujiantou"></view>
              <text class="start-position">{{
                formatAddress(item.dischargeAddress)
              }}</text>
            </view>
            <view class="price">
              <text class="small">¥</text>
              <text class="big">{{ item.bidAmount || 0 }}</text>
              <text class="small">/{{ item.bidType || '' }}</text>
            </view>
          </view>
          <view class="goods-info">
            <text class="name-info">{{ formatCargoInfo(item) }}</text>
            <text class="time">{{ formatCreateTime(item.createTime) }}</text>
          </view>
          <!--          <view class="price-tag">
                      <text class="tag">一口价</text>
                      <text class="tag">电话议价</text>
                    </view>-->
          <!--          <view class="message">
                      <view class="message-left">
                        <image src="../../../static/images/xiaofeiji.png"></image>
                        <text class="name">{{ item.userName || '未知用户' }}</text>
                        <text class="text">{{ item.message || '暂无消息' }}</text>
                      </view>
                      <up-icon name="arrow-right"></up-icon>
                    </view>-->
          <view class="btn-region">
            <view class="btn-span" @click.stop="editOrder(item)">改价</view>
            <view class="btn-span" @click.stop="deleteOrder(item)">取消订单</view>
          </view>
        </view>
      </scroll-view>
    </view>
    <uni-load-more iconType="auto" :status="loadStatus"></uni-load-more>
    <view class="order-tips">新发货源24小时后自动删除</view>

    <!-- 删除确认弹窗 -->
    <cu-confirm-dialog
      ref="confirmDialog"
      :title="deleteDialogData.title"
      :content="deleteDialogData.content"
      :important-tips="deleteDialogData.importantTips"
      cancel-text="取消"
      confirm-text="删除"
      @cancel="handleDeleteCancel"
      @confirm="handleDeleteConfirm"
    />

    <!-- 价格编辑弹窗 -->
    <cu-price-edit-dialog
      ref="priceEditDialog"
      :show-deposit="true"
      :initial-price="editOrderData.price"
      :initial-deposit="editOrderData.deposit"
      :initial-unit-text="editOrderData.unitText"
      @cancel="handleEditCancel"
      @confirm="handleEditConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { onReachBottom, onPageScroll, onShow } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue'
import { orderInfoPage, deleteOrderInfo } from '../../../api/transport'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { getStoragetItem } from '@/utils/storage'
import CuPriceEditDialog from '../../../components/cu-price-edit-dialog/cu-price-edit-dialog.vue'
import { editOrderInfo } from '@/api/transport'

// 格式化地址显示
const formatAddress = (addressData: any) => {
  if (!addressData) return '未知'

  // 如果是字符串，直接返回
  if (typeof addressData === 'string') {
    return addressData || '未知'
  }

  // 如果是对象，解析地址信息
  let province = ''
  let city = ''
  let district = ''

  // 尝试不同的数据结构
  if (addressData.addressComponent) {
    province = addressData.addressComponent.province || ''
    city = addressData.addressComponent.city || ''
    district = addressData.addressComponent.district || ''
  } else if (addressData.province || addressData.city || addressData.district) {
    province = addressData.province || ''
    city = addressData.city || ''
    district = addressData.district || ''
  } else {
    // 如果都没有，返回原始数据的字符串形式
    return String(addressData) || '未知'
  }

  // 直辖市判断：如果省份和城市相同，只显示城市
  const municipalities = ['北京市', '上海市', '天津市', '重庆市']
  const isDirectMunicipality = municipalities.includes(province) || province === city

  if (isDirectMunicipality) {
    // 直辖市格式：{城市}{区域}
    return `${city}${district}`.replace(/市$/, '') // 去掉城市末尾的"市"字
  } else {
    // 普通城市格式：{省份}{城市}{区域}
    return `${province}${city}${district}`
  }
}

// 格式化货物信息显示
const formatCargoInfo = (item: any) => {
  if (!item) return '暂无信息'

  let infoArray = []

  // 1. 货物名称
  if (item.cargoName) {
    infoArray.push(item.cargoName)
  }

  // 2. 重量范围处理
  const minWeight = item.minWeight
  const maxWeight = item.maxWeight

  if (minWeight && maxWeight && minWeight !== maxWeight) {
    // 有起始和结束，显示区间
    infoArray.push(`${minWeight}-${maxWeight}吨`)
  } else if (minWeight && minWeight > 0) {
    // 只有起始值
    infoArray.push(`${minWeight}吨`)
  } else if (maxWeight && maxWeight > 0) {
    // 只有结束值
    infoArray.push(`${maxWeight}吨`)
  }
  // 都没有或都为0，不显示重量信息

  // 3. 体积范围处理
  const minVolume = item.minVolume
  const maxVolume = item.maxVolume

  if (minVolume && maxVolume && minVolume !== maxVolume) {
    // 有起始和结束，显示区间
    infoArray.push(`${minVolume}-${maxVolume}方`)
  } else if (minVolume && minVolume > 0) {
    // 只有起始值
    infoArray.push(`${minVolume}方`)
  } else if (maxVolume && maxVolume > 0) {
    // 只有结束值
    infoArray.push(`${maxVolume}方`)
  }
  // 都没有或都为0，不显示体积信息

  // 4. 车长信息
  if (item.vehicleLength) {
    infoArray.push(`${item.vehicleLength}`)
  }

  // 5. 车型信息
  if (item.vehicleType) {
    infoArray.push(item.vehicleType)
  }

  // 用空格连接所有信息，如果没有任何信息则返回默认文本
  return infoArray.length > 0 ? infoArray.join(' ') : '暂无信息'
}

// 订单列表
const orderList = ref<any[]>([])
// 当前页码
const currentPage = ref(1)
// 每页数量
const pageSize = ref(10)
// 是否开启下拉刷新
const isLoadingTop = ref(true)
// 刷新状态
const trigger = ref(false)
// 页面是否已初始化
const isPageInitialized = ref(false)
// 是否需要刷新列表（从详情页返回时设置为true）
const needRefreshList = ref(false)

// 确认弹窗引用
const confirmDialog = ref(null)
// 删除弹窗数据
const deleteDialogData = ref({
  title: '',
  content: '',
  importantTips: '',
})
// 当前要删除的订单ID
const currentDeleteOrderId = ref('')

// 价格编辑弹窗引用
const priceEditDialog = ref(null)
// 编辑订单数据
const editOrderData = ref({
  orderId: '',
  price: '',
  deposit: '',
  unitText: '趟'
})

// 获取订单列表
const fetchOrderList = async (page: number) => {
  try {
    const response: any = await orderInfoPage({
      page,
      limit: pageSize.value,
      shippersId: getStoragetItem('userInfo').id,
      orderStatus: 0, //待接单
    })
    uni.hideLoading()
    if (response.code === 700) {
      if (page === 1) {
        orderList.value = response.result || []
      } else {
        orderList.value = [...orderList.value, ...(response.result || [])]
      }
      if (!response.result || response.result.length < pageSize.value) {
        loadStatus.value = 'no-more'
      } else {
        loadStatus.value = 'more'
      }
    } else {
      console.error('获取订单列表失败')
      loadStatus.value = 'more'
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    loadStatus.value = 'more'
  }
}
// 格式化创建时间的函数
const formatCreateTime = (createTime: string | undefined) => {
  if (!createTime) {
    return '刚刚'
  }
  const diffMinutes = dayjs().diff(dayjs(createTime), 'minute')

  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes} 分钟前`
  } else if (diffMinutes < 1440) {
    const diffHours = Math.floor(diffMinutes / 60)
    return `${diffHours} 小时前`
  } else if (diffMinutes < 10080) {
    const diffDays = Math.floor(diffMinutes / 1440)
    return `${diffDays} 天前`
  } else {
    return dayjs(createTime).format('MM-DD')
  }
}
// 下拉刷新被触发
const refresherrefresh = async () => {
  currentPage.value = 1
  trigger.value = true
  await fetchOrderList(currentPage.value)
  trigger.value = false
}
// 滚动到底部加载更多
const loadMore = async () => {
  if (loadStatus.value === 'no-more') return
  loadStatus.value = 'loading'
  currentPage.value++
  await fetchOrderList(currentPage.value)
}
onPageScroll((e) => {
  if (e.scrollTop == 0) {
    isLoadingTop.value = true
  } else if (e.scrollTop > 0) {
    isLoadingTop.value = false
  }
})

// more、loading、no-more
const loadStatus = ref('loading')

onReachBottom(() => {
  loadMore()
})

// 页面显示时的处理
onShow(() => {
  // 如果页面已经初始化过且需要刷新，说明是从其他页面返回的
  if (isPageInitialized.value && needRefreshList.value) {
    console.log('从详情页返回，刷新列表数据')
    currentPage.value = 1
    fetchOrderList(currentPage.value)
    needRefreshList.value = false // 重置刷新标志
  }
})

// 页面加载时获取第一页数据
onMounted(() => {
  fetchOrderList(currentPage.value)
  isPageInitialized.value = true
})

// 详情页
const goPendingDetail = (item: any) => {
  // 设置需要刷新标志，当从详情页返回时会刷新列表
  needRefreshList.value = true

  uni.navigateTo({
    url: '/pageCom/transport/pending_order_detail',
    success: (res) => {
      // 使用 eventChannel 发送数据
      res.eventChannel.emit('orderId', { id: item.id })
    },
  })
}

// 编辑订单
const editOrder = (item: any) => {
  // 设置编辑数据
  editOrderData.value = {
    orderId: item.id,
    price: item.bidAmount || '',
    deposit: item.depositAmount || '',
    unitText: item.bidType || '趟'
  }
  console.log('编辑数据:', editOrderData.value)

  // 打开编辑弹窗
  priceEditDialog.value?.open()
}

// 处理编辑取消
const handleEditCancel = () => {
  console.log('用户取消编辑')
}

// 处理编辑确认
const handleEditConfirm = async (data: any) => {
  try {
    console.log('编辑确认数据:', data)

    // 显示加载提示
    uni.showLoading({
      title: '保存中...',
      mask: true
    })

    // 调用编辑订单接口
    const response = await editOrderInfo({
      id: editOrderData.value.orderId,
      bidAmount: data.price,
      depositAmount: data.deposit,
      bidType: data.unitText,  // 使用中文单位文本
      // priceUnit: data.unitId   // 同时传递单位ID（如果后端需要）
    })

    uni.hideLoading()

    uni.showToast({
      title: '修改成功',
      icon: 'success',
      duration: 1500
    })

    // 刷新列表数据
    currentPage.value = 1
    await fetchOrderList(currentPage.value)

  } catch (error) {
    uni.hideLoading()
    console.error('编辑订单失败:', error)
    uni.showToast({
      title: '修改失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

// 删除订单
const deleteOrder = (item: any) => {
  // 设置弹窗数据
  deleteDialogData.value = {
    title: '确认取消订单',
    content: `确定要取消这个订单吗？`,
    importantTips: `${formatAddress(item.loadingAddress)} → ${formatAddress(item.dischargeAddress)}`,
  }

  // 保存要删除的订单ID
  currentDeleteOrderId.value = item.id

  // 打开确认弹窗
  confirmDialog.value?.open()
}

// 处理删除取消
const handleDeleteCancel = () => {
  // 清空当前删除的订单ID
  currentDeleteOrderId.value = ''
  console.log('用户取消删除')
}

// 处理删除确认
const handleDeleteConfirm = async () => {
  if (currentDeleteOrderId.value) {
    await handleDeleteOrder(currentDeleteOrderId.value)
    currentDeleteOrderId.value = ''
  }
}

// 执行删除订单操作
const handleDeleteOrder = async (orderId: string) => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '删除中...',
      mask: true,
    })

    // 调用删除接口
    const response: any = await deleteOrderInfo(orderId)

    uni.hideLoading()

    if (response.code === 700) {
      // 删除成功
      uni.showToast({
        title: '删除成功',
        icon: 'success',
        duration: 1500,
      })

      // 刷新列表数据
      currentPage.value = 1
      await fetchOrderList(currentPage.value)
    } else {
      // 删除失败
      uni.showToast({
        title: response.msg || '删除失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('删除订单失败:', error)
    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}
</script>

<style scoped lang="scss">
.invoice {
  min-height: 0;
  width: 100%;
  height: 100%;
}
.order-list {
  width: 100%;
  height: 100%;
  // background-color: #fff;
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
  // padding: 0rpx 32rpx;
  // box-sizing: border-box;

  .order-item {
    padding: 32rpx 32rpx;
    box-sizing: border-box;
    // border-bottom: 2rpx solid $uni-border-color;
    margin-top: 20rpx;
    background-color: #fff;
    &:last-child {
      border-bottom: none;
    }
    .position-price {
      width: 100%;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      .position {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .start-position {
          font-size: 32rpx;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200rpx;
        }
        .iconfont {
          font-weight: 700;
          margin: 0 20rpx;
        }
      }
      .price {
        width: 200rpx;
        color: #ff8c00;
        text-align: right;
        .small {
          font-size: 28rpx;
        }
        .big {
          font-size: 36rpx;
          font-weight: 700;
        }
      }
    }
    .goods-info {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-top: 8rpx;
      .name-info {
        flex: 1;
        font-size: $uni-font-size-base;
        color: $uni-text-color;
        line-height: 1.4;
        word-break: break-all;
        margin-right: 20rpx;
      }
      .time {
        width: 150rpx;
        font-size: $uni-font-size-base;
        color: $uni-text-color-grey;
        text-align: right;
      }
    }
    .price-tag {
      display: flex;
      align-items: center;
      margin-top: 8rpx;
      .tag {
        padding: 2rpx 6rpx;
        box-sizing: border-box;
        border: 2rpx solid #ff8c00;
        color: #ff8c00;
        font-size: $uni-font-size-sm;
        border-radius: 2rpx;
        margin-right: 12rpx;
      }
    }
    .message {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10rpx;
      .message-left {
        flex: 1;
        display: flex;
        align-items: center;
        image {
          width: 40rpx;
          height: 40rpx;
          margin-right: 8rpx;
        }
        text {
          font-size: $uni-font-size-base;
          color: $uni-text-color-placeholder;
        }
        .name {
          color: #ff8c00;
        }
      }
      :deep(.u-icon--right) {
        width: 30rpx;
      }
    }
    .btn-region {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-top: 14rpx;
      .btn-span {
        padding: 10rpx 24rpx;
        box-sizing: border-box;
        margin-left: 20rpx;
        border: 2rpx solid $uni-border-color;
        border-radius: 10rpx;
      }
    }
  }
}
.order-tips {
  text-align: center;
  margin-top: 60rpx;
  font-size: $uni-font-size-base-30;
  color: $uni-text-color-placeholder;
  padding-bottom: 60rpx;
}
</style>
