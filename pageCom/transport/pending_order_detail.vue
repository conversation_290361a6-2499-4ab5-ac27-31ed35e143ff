<template>
  <view class="pending-order-detail">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
      <template #leftBtn>
        <text class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
      </template>
      <template #title>{{ '' }}</template>
      <template #rightBtn>{{ '' }}</template>
    </cu-header>
    <cu-occupation></cu-occupation>
    <view class="main-container" :style="{ paddingBottom: safeAreaInsetsBottom + 200 + 'rpx' }">
      <!--			<view class="forthwith-info">
              <text class="name">王师傅(6.8米/高栏)</text>
              <text class="text">刚刚浏览了您的货源</text>
            </view>-->
      <view class="base-info-box">
        <view class="base-info">
          <view class="date-time">{{ formatDate(orderDetail?.loadingDate) }} {{ orderDetail?.earliestLoadingTime }}-{{ orderDetail?.latestLoadingTime }}</view>
          <view class="loading-info loading-info-line">
            <view class="loading-info-left">
              <view class="icon-text">装</view>
              <view class="loading-info-left-address">
                <view class="big-name">{{ formatAddressDetail(orderDetail?.loadingAddress).region }}</view>
                <view class="small-name" v-if="formatAddressDetail(orderDetail?.loadingAddress).detail">
                  {{ formatAddressDetail(orderDetail?.loadingAddress).detail }}
                </view>
              </view>
            </view>
            <view class="loading-info-right">
              <!-- <text>添加联系人</text> -->
            </view>
          </view>
          <view class="loading-info unloading-info">
            <view class="loading-info-left">
              <view class="icon-text">卸</view>
              <view class="loading-info-left-address">
                <view class="big-name">{{ formatAddressDetail(orderDetail?.dischargeAddress).region }}</view>
                <view class="small-name" v-if="formatAddressDetail(orderDetail?.dischargeAddress).detail">
                  {{ formatAddressDetail(orderDetail?.dischargeAddress).detail }}
                </view>
              </view>
            </view>
            <view class="loading-info-right">
              <!-- <text>添加联系人</text> -->
            </view>
          </view>
          <view class="mileage">
            <text>一装一卸，预估运输里程{{ estimatedDistance }}km</text>
            <uni-tooltip placement="top">
              <view class="iconfont icon-owner-tishi"></view>
              <template #content>根据所选车型车长，智能计算路线，仅供参考</template>
            </uni-tooltip>
          </view>
          <cu-cell>
            <template #label>车辆</template>
            <template #showText>{{ orderDetail?.vehicleLength }} {{ orderDetail?.vehicleType }}</template>
          </cu-cell>
          <cu-cell>
            <template #label>货物</template>
            <template #showText>{{ formatCargoInfo(orderDetail) }}</template>
          </cu-cell>
          <cu-cell>
            <template #label>备注</template>
            <template #showText>{{ orderDetail?.remarks }}</template>
          </cu-cell>
        </view>
      </view>
      <view class="pay-info-box">
        <view class="pay-info">
          <view class="title">支付信息</view>
          <cu-cell>
            <template #label>总运费</template>
            <template #showText>{{ orderDetail?.bidAmount }}元/{{ orderDetail?.bidType }}</template>
          </cu-cell>
          <cu-cell>
            <template #label>订金</template>
            <template #showText>{{ orderDetail?.depositAmount }}元（{{ orderDetail.depositRefundType == 'refund' ? '退还' : '不退还' }}）</template>
            <!-- <template #handleText>查看流程</template> -->
          </cu-cell>
          <!-- <cu-cell>
            <template #label>担保说明</template>
            <template #showText>订金支付到平台用于订货订金，货主确认收货后由平台退还给司机</template>
          </cu-cell> -->
          <cu-cell>
            <template #label>司机净得</template>
            <template #showText>{{ orderDetail?.bidAmount }}元</template>
          </cu-cell>
        </view>
      </view>
      <view class="secure-tip-box">
        <view class="secure-tip">
          为了保障货物安全及跟车人员（如有）人身安全，请您务必核实实际到场装货的司机及车辆是否与订单显示一致。如不一致，您应当拒绝装货并立即告知平台客服。
        </view>
      </view>
    </view>
    <view class="btn-region" :style="{ bottom: safeAreaInsetsBottom + 'rpx' }">
      <view class="btn-span" @click="editOrder">改价</view>
      <view class="btn-span" @click="cancelOrder">取消订单</view>
      <!-- <view class="btn-span" @click="toDepositApply">扣订金</view> -->
    </view>
    <cu-secure></cu-secure>

    <!-- 订金流程弹窗 -->
    <cu-payment-dialog ref="paymentDialog" :deposit-info="depositDialogData" @close="handlePaymentDialogClose" />

    <!-- 取消订单确认弹窗 -->
    <cu-confirm-dialog ref="confirmDialog" :title="deleteDialogData.title" :content="deleteDialogData.content" :important-tips="deleteDialogData.importantTips" cancel-text="取消" confirm-text="确认取消" @cancel="handleDeleteCancel" @confirm="handleDeleteConfirm" />

    <!-- 价格编辑弹窗 -->
    <cu-price-edit-dialog ref="priceEditDialog" :show-deposit="true" :initial-price="editOrderData.price" :initial-deposit="editOrderData.deposit" :initial-unit-text="editOrderData.unitText" @cancel="handleEditCancel" @confirm="handleEditConfirm" />
  </view>
</template>

<script setup lang="ts">

import dayjs from "dayjs";
import { formatDate } from '@/utils/common';
import CuPaymentDialog from '../../components/cu-payment-dialog/cu-payment-dialog.vue';
import CuConfirmDialog from '../../components/cu-confirm-dialog/cu-confirm-dialog.vue';
import CuPriceEditDialog from '../../components/cu-price-edit-dialog/cu-price-edit-dialog.vue';

const { safeAreaInsets } = uni.getSystemInfoSync();
const safeAreaInsetsBottom = safeAreaInsets.bottom * 2;
const heightHeader = safeAreaInsets.top * 2 + 80 + 'rpx';
import { onMounted, ref } from 'vue';
// 导入订单详情查询接口
import { editOrderInfo, getOrderInfoById, deleteOrderInfo } from '../../api/transport';
import { union } from 'lodash';

// 格式化地址显示 - 分离省市区和详细地址
const formatAddressDetail = (addressData: any) => {
  if (!addressData) {
    return {
      region: '未知',
      detail: ''
    }
  }

  // 如果是字符串，尝试解析或直接返回
  if (typeof addressData === 'string') {
    return {
      region: addressData || '未知',
      detail: ''
    }
  }

  // 如果是对象，解析地址信息
  let province = ''
  let city = ''
  let district = ''
  let detail = ''

  // 尝试不同的数据结构
  if (addressData.addressComponent) {
    province = addressData.addressComponent.province || ''
    city = addressData.addressComponent.city || ''
    district = addressData.addressComponent.district || ''
    detail = addressData.addressComponent.street || addressData.addressComponent.streetNumber || ''
  } else if (addressData.province || addressData.city || addressData.district) {
    province = addressData.province || ''
    city = addressData.city || ''
    district = addressData.district || ''
    detail = addressData.detail || addressData.street || addressData.address || ''
  } else {
    // 如果都没有，返回原始数据
    return {
      region: String(addressData) || '未知',
      detail: ''
    }
  }

  // 直辖市判断：如果省份和城市相同，只显示城市
  const municipalities = ['北京市', '上海市', '天津市', '重庆市']
  const isDirectMunicipality = municipalities.includes(province) || province === city

  let region = ''
  if (isDirectMunicipality) {
    // 直辖市格式：{城市}{区域}
    region = `${city}${district}`.replace(/市$/, '') // 去掉城市末尾的"市"字
  } else {
    // 普通城市格式：{省份}{城市}{区域}
    region = `${province}${city}${district}`
  }

  return {
    region: region || '未知',
    detail: detail || ''
  }
}

// 定义响应式变量存储订单详情
const orderDetail = ref(null);

// 支付弹窗引用
const paymentDialog = ref(null);

// 订金弹窗数据
const depositDialogData = ref({
  amount: '0',
  timeline: []
});

// 确认弹窗引用
const confirmDialog = ref(null);

// 删除弹窗数据
const deleteDialogData = ref({
  title: '',
  content: '',
  importantTips: ''
});

// 当前要删除的订单ID
const currentDeleteOrderId = ref('');

// 价格编辑弹窗引用
const priceEditDialog = ref(null);

// 编辑订单数据
const editOrderData = ref({
  orderId: '',
  price: '',
  deposit: '',
  unitText: '趟'
});

// 改价
const updatePrice = async () => {
  try {
    // 调用接口获取订单详情
    const result: any = await editOrderInfo({ "id": orderDetail.value.id, "notBidAmount": addPrice.value, "notDepositAmount": deposit.value });
    uni.hideLoading();
    if (result.code == 700) {
      uni.showToast({
        title: '改价成功',
        icon: 'success',
        duration: 2000
      });
    } else {
      uni.showToast({
        title: result.msg || '改价失败',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    uni.showToast({
      title: '改价失败',
      icon: 'none',
      duration: 2000
    });
  }
};

// 获取订单详情的函数
const fetchOrderDetail = async (id: string) => {
  try {
    // 调用接口获取订单详情
    const response: any = await getOrderInfoById(id);
    uni.hideLoading();
    if (response.code === 700) {
      orderDetail.value = response.result;
      calculateDistance();
      console.log(orderDetail.value);
    } else {
      console.error('获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
  }
};

// 格式化货物信息显示
const formatCargoInfo = (item: any) => {
  if (!item) return '暂无信息'

  let infoArray = []

  // 1. 货物名称
  if (item.cargoName) {
    infoArray.push(item.cargoName)
  }

  // 2. 重量范围处理
  const minWeight = item.minWeight
  const maxWeight = item.maxWeight

  if (minWeight && maxWeight && minWeight !== maxWeight) {
    // 有起始和结束，显示区间
    infoArray.push(`${minWeight}-${maxWeight}吨`)
  } else if (minWeight && minWeight > 0) {
    // 只有起始值
    infoArray.push(`${minWeight}吨`)
  } else if (maxWeight && maxWeight > 0) {
    // 只有结束值
    infoArray.push(`${maxWeight}吨`)
  }
  // 都没有或都为0，不显示重量信息

  // 3. 体积范围处理
  const minVolume = item.minVolume
  const maxVolume = item.maxVolume

  if (minVolume && maxVolume && minVolume !== maxVolume) {
    // 有起始和结束，显示区间
    infoArray.push(`${minVolume}-${maxVolume}方`)
  } else if (minVolume && minVolume > 0) {
    // 只有起始值
    infoArray.push(`${minVolume}方`)
  } else if (maxVolume && maxVolume > 0) {
    // 只有结束值
    infoArray.push(`${maxVolume}方`)
  }
  // 都没有或都为0，不显示体积信息

  // 用空格连接所有信息，如果没有任何信息则返回默认文本
  return infoArray.length > 0 ? infoArray.join(' ') : '暂无信息'
}

onMounted(() => {
  const pages = getCurrentPages();
  if (pages.length > 1) {
    const currentPage = pages[pages.length - 1];
    // 从页面实例获取事件通道
    const eventChannel = currentPage.$vm.getOpenerEventChannel();
    eventChannel.on('orderId', (data) => {
      const id = data.id;
      if (id) {
        // 若获取到 id，调用获取订单详情函数
        fetchOrderDetail(id);
      } else {
        console.error('未获取到订单 ID');
      }
    });
  }
});

// 计算距离的函数
const estimatedDistance = ref(null);
const calculateDistance = () => {
  const startLng = orderDetail.value.loadingLongitude;
  const startLat = orderDetail.value.loadingLatitude;
  const endLng = orderDetail.value.unloadingLongitude;
  const endLat = orderDetail.value.unloadingLatitude;
  const R = 6371; // 地球半径，单位：公里
  const dLat = (endLat - startLat) * (Math.PI / 180);
  const dLng = (endLng - startLng) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(startLat * (Math.PI / 180)) *
    Math.cos(endLat * (Math.PI / 180)) *
    Math.sin(dLng / 2) *
    Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  estimatedDistance.value = distance.toFixed(1);
};

// 显示订金流程弹窗
const showDepositFlow = () => {
  // 构建订金流程数据
  // depositDialogData.value = {
  //   amount: orderDetail.value?.depositAmount || '0',
  //   timeline: [
  //     { title: '成功支付给货主', time: '2025.03.14 12:22' },
  //     { title: '支付处理中', time: '2025.03.14 12:22' },
  //     { title: `已到达订金自动流转时间，发起给货主¥${orderDetail.value?.depositAmount || '0'}`, time: '2025.03.14 12:22' },
  //     { title: '您已支付订金，平台监管中', time: '2025.03.12 13:14' }
  //   ]
  // }
  depositDialogData.value.amount = orderDetail.value.depositAmount
  depositDialogData.value.timeline = orderDetail.value.depositProcesses

  // 打开弹窗
  paymentDialog.value?.open('deposit')
}

// 处理支付弹窗关闭
const handlePaymentDialogClose = () => {
  console.log('订金流程弹窗关闭')
}

// 编辑订单
const editOrder = () => {
  // 设置编辑数据
  editOrderData.value = {
    orderId: orderDetail.value?.id || '',
    price: orderDetail.value?.bidAmount || '',
    deposit: orderDetail.value?.depositAmount || '',
    unitText: orderDetail.value?.bidType || '趟'
  }
  console.log('编辑数据:', editOrderData.value)

  // 打开编辑弹窗
  priceEditDialog.value?.open()
}

// 处理编辑取消
const handleEditCancel = () => {
  console.log('用户取消编辑')
}

// 处理编辑确认
const handleEditConfirm = async (data: any) => {
  try {
    console.log('编辑确认数据:', data)

    // 显示加载提示
    uni.showLoading({
      title: '保存中...',
      mask: true
    })

    // 调用编辑订单接口
    const response = await editOrderInfo({
      id: editOrderData.value.orderId,
      bidAmount: data.price,
      depositAmount: data.deposit,
      bidType: data.unitText,  // 使用中文单位文本
      // priceUnit: data.unitId   // 同时传递单位ID（如果后端需要）
    })

    uni.hideLoading()

    uni.showToast({
      title: '修改成功',
      icon: 'success',
      duration: 1500
    })

    // 刷新订单详情数据
    setTimeout(async () => {
      await fetchOrderDetail(orderDetail.value?.id)
    }, 1500)

  } catch (error) {
    uni.hideLoading()
    console.error('编辑订单失败:', error)
    uni.showToast({
      title: '修改失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

// 取消订单
const cancelOrder = () => {
  // 设置弹窗数据
  deleteDialogData.value = {
    title: '确认取消订单',
    content: `确定要取消这个订单吗？`,
    importantTips: `${formatAddressDetail(orderDetail.value?.loadingAddress).region} → ${formatAddressDetail(orderDetail.value?.dischargeAddress).region}`
  }

  // 保存要删除的订单ID
  currentDeleteOrderId.value = orderDetail.value?.id

  // 打开确认弹窗
  confirmDialog.value?.open()
}

// 处理删除取消
const handleDeleteCancel = () => {
  // 清空当前删除的订单ID
  currentDeleteOrderId.value = ''
  console.log('用户取消删除')
}

// 处理删除确认
const handleDeleteConfirm = async () => {
  if (currentDeleteOrderId.value) {
    await handleDeleteOrder(currentDeleteOrderId.value)
    currentDeleteOrderId.value = ''
  }
}

// 执行删除订单操作
const handleDeleteOrder = async (orderId: string) => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '取消中...',
      mask: true
    })

    // 调用删除接口
    const response: any = await deleteOrderInfo(orderId)

    uni.hideLoading()

    if (response.code === 700) {
      // 删除成功
      uni.showToast({
        title: '取消成功',
        icon: 'success',
        duration: 1500
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      // 删除失败
      uni.showToast({
        title: response.msg || '取消失败',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('取消订单失败:', error)
    uni.showToast({
      title: '取消失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

// 跳转扣订金退订金页面
const toDepositApply = () => {
  const depositStatus = orderDetail.value?.depositStatus;
  const orderId = orderDetail.value?.id;

  const url = `/pageCom/depositApply/depositApply?depositStatus=${depositStatus}&orderId=${orderId}`;
  uni.redirectTo({
    url: url
  })
}
</script>

<style scoped lang="scss">
:deep(.cu-header-box) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background-image: linear-gradient(#ee9209, #f3f3f3);
  background-size: 100% 400rpx;
  z-index: 3;

  .header-container {
    background-color: transparent;
  }

  .cu-header {
    background-color: transparent;
  }
}

.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 400rpx;
  background-image: linear-gradient(#ee9209, #f3f3f3);
  z-index: 1;
}

.main-container {
  position: relative;
  z-index: 1;
}

.forthwith-info {
  width: 100%;
  padding: 0rpx 32rpx;
  box-sizing: border-box;

  text {
    font-size: $uni-font-size-base-30;
  }

  .name {
    color: $uni-text-color-light;
  }

  .text {
    color: $uni-text-color;
  }
}

.base-info-box {
  padding: 0rpx 32rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .base-info {
    padding: 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 8rpx;

    .date-time {
      font-size: $uni-font-size-base;
      color: $uni-text-color-grey;
      margin-bottom: 24rpx;
    }

    .loading-info-line {
      position: relative;

      &:before {
        position: absolute;
        left: 20rpx;
        top: 40rpx;
        content: '';
        width: 2rpx;
        height: 100%;
        border-left: 2rpx dashed $uni-text-color-grey;
      }
    }

    .loading-info {
      display: flex;
      justify-content: space-between;

      .loading-info-left {
        display: flex;
        align-items: flex-start;
        flex: 1;

        .icon-text {
          width: 40rpx;
          height: 40rpx;
          text-align: center;
          line-height: 40rpx;
          color: #fff;
          background-color: blue;
          font-size: $uni-font-size-base;
          border-radius: 10rpx;
          margin-right: 14rpx;
        }

        .loading-info-left-address {
          flex: 1;

          .big-name {
            font-size: $uni-font-size-base-30;
            color: $uni-text-color;
          }

          .small-name {
            font-size: $uni-font-size-base;
            color: $uni-text-color-grey;
            margin-top: 8rpx;
          }
        }
      }

      .loading-info-right {
        width: 180rpx;
        text-align: right;

        text {
          font-size: 26rpx;
          color: $uni-text-color-grey;
          padding: 2rpx 8rpx;
          border: 2rpx solid $uni-text-color-grey;
          border-radius: 8rpx;
        }
      }
    }

    .unloading-info {
      margin-top: 40rpx;

      .loading-info-left {
        .icon-text {
          background-color: #ee7942;
        }
      }
    }

    .mileage {
      font-size: $uni-font-size-base;
      color: $uni-text-color-grey;
      margin-top: 20rpx;
      margin-bottom: 20rpx;
      padding-bottom: 30rpx;
      border-bottom: 2rpx solid $uni-text-color-grey;

      :deep(.uni-tooltip) {
        margin-left: 10rpx;

        .uni-tooltip-popup {
          min-width: 300rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}

.pay-info-box {
  padding: 0rpx 32rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .pay-info {
    padding: 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 8rpx;

    .title {
      font-size: $uni-font-size-base-30;
      color: $uni-text-color;
      line-height: $uni-font-size-base-30;
      font-weight: 700;
      margin-bottom: 20rpx;
    }
  }
}

.secure-tip-box {
  padding: 0rpx 32rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .secure-tip {
    padding: 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: $uni-font-size-sm;
    color: $uni-text-color-grey;
  }
}

.btn-region {
  width: 100%;
  padding: 10rpx 30rpx 10rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 3;
  display: flex;
  justify-content: flex-end;

  .btn-span {
    padding: 10rpx 24rpx;
    box-sizing: border-box;
    margin-left: 20rpx;
    border: 2rpx solid $uni-border-color;
    border-radius: 10rpx;
  }
}

:deep(.price-add-popup) {
  .uni-popup__wrapper {
    width: 100%;
    position: absolute;
    bottom: 0;

    // bottom: v-bind(tabbarHpx);
    .popup-content {
      height: 500rpx;
      background-color: #fff;

      .confirm-btn {
        width: 90%;
        margin-left: 5%;
        position: absolute;
        bottom: 20rpx;

        .btn {
          width: 100%;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          background-color: red;
          color: #fff;
          border-radius: 5rpx;
        }
      }

      .count-amount-box {
        width: 100%;
        padding: 0rpx 30rpx;
        box-sizing: border-box;
        margin-top: 20rpx;

        .count-amount {
          width: 100%;
          height: 90rpx;
          margin-bottom: 20rpx;
          background-color: $uni-bg-color-grey;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 10rpx;
          padding-right: 20rpx;
          box-sizing: border-box;

          .price {
            padding-left: 10rpx;
            width: 60rpx;
          }

          .price-input {

            // flex: none;
            .u-input__content {
              padding-left: 20rpx;
              box-sizing: border-box;
              width: 300rpx;
              flex: none;

              .u-input__content__field-wrapper {
                .u-input__content__field-wrapper__field {
                  .uni-input-wrapper {
                    .uni-input-input {
                      font-size: $uni-font-size-big;
                      color: #333;
                      font-weight: bold;
                    }
                  }
                }
              }
            }
          }

          .count-amount-right {
            display: flex;
            align-items: center;

            .text {
              font-size: $uni-font-size-base;
              color: #333;
            }

            .unit-list {
              display: flex;
              align-items: center;
              margin-left: 20rpx;

              .unit-item {
                width: 90rpx;
                height: 60rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid #fff;
                font-size: $uni-font-size-base;
                background-color: #fff;
                border-radius: 10rpx;
                margin-right: 10rpx;
                color: #333;

                &:last-child {
                  margin-right: 0rpx;
                }
              }

              .unit-item-active {
                color: #ff8c00;
                border-color: #ff8c00;
              }
            }
          }
        }

        .total-freight {
          font-size: $uni-font-size-base-30;
          color: #ff8c00;
          margin-top: 20rpx;
        }
      }
    }
  }
}
</style>
