<template>
  <view>
    <cu-header>
      <template #title>{{
        addressType == 1 ? '装货地址' : '卸货地址'
      }}</template>
      <template #rightBtn> {{ '' }} </template>
    </cu-header>
    <view
      class="input-text-box"
      id="inputTextBox"
      :class="!isShowInfo ? 'input-text-box-active' : ''"
    >
      <view class="input-text">
        <view class="input-region">
          <uni-easyinput
            type="textarea"
            :focus="isFocus"
            :inputBorder="false"
            v-model="addressValue"
            placeholder="请输入装货详细地址"
            @focus="inputFocus"
            @input="addressInput"
          ></uni-easyinput>
          <view class="sub-address" v-if="isShowInfo">
            {{
              selectedAddressShow?.detail?.address
                ? selectedAddressShow.addressComponent.province +
                  selectedAddressShow.addressComponent.city +
                  selectedAddressShow.addressComponent.district +
                  selectedAddressShow.detail.address
                : '例：南京市秦淮区总统府南门'
            }}
          </view>
          <view class="clear-btn" @tap="clear" v-if="addressValue.length > 0"
            >清空</view
          >
          <view class="paste-btn" v-if="addressValue.length == 0" @tap="paste"
            >粘贴地址</view
          >
        </view>
        <view class="tools" v-if="isShowInfo">
          <view class="contacts" @tap="contactsClick">
            <view
              class="iconfont icon-owner-tianjialianxiren"
              :class="
                selectedAddressShow?.contact?.name ||
                selectedAddressShow?.contact?.phone
                  ? 'contact-light'
                  : ''
              "
            ></view>
            <text
              v-if="
                selectedAddressShow?.contact?.name ||
                selectedAddressShow?.contact?.phone
              "
              class="contact-light"
            >
              {{ selectedAddressShow?.contact?.name }}
              {{ selectedAddressShow?.contact?.phone }}
            </text>
            <text v-else>联系人</text>
          </view>
        </view>
        <view class="choose-address" v-if="!isShowInfo">
          <view class="choose-title">请选择详细的装货地址</view>
          <view class="choose-tips" v-if="searchAddressData.length > 1">
            <text class="iconfont icon-owner-tishi1"></text>
            <text>存在多处名称相似的不同地点，请仔细确认</text>
          </view>
        </view>
      </view>
    </view>
    <view
      class="search-address-list"
      v-if="!isShowInfo"
      :style="{
        marginTop: inputTextBoxHeight + 'rpx',
        paddingBottom: addressPadding + 'rpx',
      }"
    >
      <view
        class="empty-type"
        v-if="!searchLoading && searchAddressData.length == 0"
      >
        <image class="empty-img" src="../../static/images/empty.png"></image>
      </view>
      <view class="address-list">
        <view class="loading-box" v-if="searchLoading">
          <image
            class="loading-car"
            src="../../static/images/loading_car.gif"
          ></image>
        </view>
        <view
          class="item"
          v-for="(item, index) of searchAddressData.pois"
          :key="index"
          @tap="searchAddressClick(item)"
        >
          <view class="iconfont icon-owner-dingwei1"></view>
          <view class="item-name">
            <view class="big-name">
              <rich-text :nodes="highlightText(item.name)"></rich-text>
            </view>
            <view class="small-name"
              >{{ searchAddressData.addressComponent.province }}-{{
                searchAddressData.addressComponent.city.length > 0
                  ? searchAddressData.addressComponent.city + '-'
                  : ''
              }}{{ searchAddressData.addressComponent.district }}
              {{ item.address.length > 0 ? item.address : '' }}</view
            >
          </view>
        </view>
      </view>
    </view>
    <view class="recommend-address-title" v-if="isShowInfo">推荐地址</view>
    <view class="recommend-list-box" v-if="isShowInfo">
      <view class="recommend-list">
        <view class="item" @tap="currentLocationClick">
          <view class="iconfont icon-owner-dingwei1"></view>
          <view>
            <text class="big-name">{{
              currentLocation?.pois ? currentLocation?.pois[0]?.name : ''
            }}</text>
            <text class="tag">当前定位</text>
            <view class="small-name">
              {{ currentLocation?.addressComponent?.province }}-{{
                currentLocation?.addressComponent?.city.length > 0
                  ? currentLocation?.addressComponent?.city + '-'
                  : ''
              }}{{ currentLocation?.addressComponent?.district }}
              {{
                currentLocation?.pois ? currentLocation?.pois[0]?.address : ''
              }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view
      class="map-btn-box"
      v-if="!isShowInfo"
      id="mapBtnBox"
      :style="{ bottom: safeAreaInsetsBottom + 'rpx' }"
    >
      <view class="map-btn">
        <text>地图选点</text>
        <text class="iconfont icon-owner-youjiantou"></text>
      </view>
    </view>
    <view
      class="submit-box"
      :style="{ bottom: safeAreaInsetsBottom + 'rpx' }"
      v-if="isShowInfo"
      @click="confirmBtnClick"
    >
      <view class="btn">确定</view>
    </view>
    <cu-secure></cu-secure>
    <uni-popup
      ref="contactRef"
      background-color="#fff"
      class="global-popup contact-popup"
      borderRadius="20rpx 20rpx 0rpx 0rpx"
      :is-mask-click="false"
    >
      <view class="popup-content">
        <view class="title-box">
          <text class="btn cancel" @tap="contactCancel">取消</text>
          <text class="title"
            >{{ addressType == 1 ? '装货' : '卸货' }}联系人</text
          >
          <text class="btn confirm" @tap="contactConfirm">确定</text>
        </view>
        <view class="content-box">
          <view class="item">
            <view class="label">联系人姓名</view>
            <uni-easyinput
              class="custom-easyinput"
              :inputBorder="false"
              v-model="contackInfo.name"
              placeholder="请输入联系人姓名"
            ></uni-easyinput>
          </view>
          <view class="item">
            <view class="label">联系人电话</view>
            <uni-easyinput
              class="custom-easyinput"
              type="number"
              :inputBorder="false"
              v-model="contackInfo.phone"
              placeholder="请输入联系人电话"
            ></uni-easyinput>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import { gConfig } from '@/config.ts'
import { debounce } from 'lodash'
import { setStoragetItem, getStoragetItem } from '@/utils/storage'

const { safeAreaInsets } = uni.getSystemInfoSync()
const addressType = ref(1) // 1:装货地址   2:卸货地址
const addressValue = ref('')
const headerHeight = safeAreaInsets.top * 2 + 80 + 'rpx'
// 清空
const clear = () => {
  addressValue.value = ''
  isShowInfo.value = true
  searchAddressData.value = []
  selectedAddressShow.value = {}
  resetContack()
}
// 粘贴
const paste = () => {
  uni.getClipboardData({
    success: function (res) {
      addressValue.value = res.data
    },
  })
}
// 联系人
const contactRef = ref()
let contackInfo: any = reactive({
  name: '',
  phone: '',
})
const contactsClick = () => {
  contactRef.value.open('bottom')
}
// 联系人取消
const contactCancel = () => {
  contactRef.value.close()
}
// 联系人确认
const contactConfirm = () => {
  if (!contackInfo.name || !contackInfo.phone) {
    uni.showToast({
      title: '请输入联系人信息',
      icon: 'none',
    })
    return
  }
  submitAddressHandle(3) //保存用户
  contactRef.value.close()
}
// 获取焦点
const inputFocus = () => {
  isShowInfo.value = false
  setTimeout(() => {
    const query = uni.createSelectorQuery().in(this)
    query
      .select('#inputTextBox')
      .boundingClientRect((data: any) => {
        if (data) {
          inputTextBoxHeight.value =
            data.height * 2 + safeAreaInsets.top * 2 + 120
        }
      })
      .exec()
    query
      .select('#mapBtnBox')
      .boundingClientRect((data: any) => {
        if (data) {
          addressPadding.value = data.height * 2 + safeAreaInsets.bottom * 2
        }
      })
      .exec()
  }, 100)
  addressInput(addressValue.value)
}
const isShowInfo = ref(true)
const addressInput = debounce((val: string) => {
  if (val) {
    isShowInfo.value = false
    getPoiList(val)
  } else {
    isShowInfo.value = true
    searchAddressData.value = []
    selectedAddressShow.value = {}
    resetContack()
  }
}, 400)
// 清空恢复联系人数据
const resetContack = () => {
  let localAddressObj: any = {}
  if (addressType.value == 1) {
    localAddressObj = getStoragetItem('shipmentsAddress')
  } else if (addressType.value == 2) {
    localAddressObj = getStoragetItem('placeAddress')
  }
  if (localAddressObj) {
    selectedAddressShow.value.contact = localAddressObj.contact
    contackInfo = selectedAddressShow.value.contact || {}
  }
}
const safeAreaInsetsBottom = safeAreaInsets.bottom * 2
const inputTextBoxHeight = ref(0)
const addressPadding = ref(0)
const searchAddressData = ref([])
const searchLoading = ref(true)
const getPoiList = (
  keywords: any,
  city: any = null,
  latitude: any = null,
  longitude: any = null,
  radius: any = null
) => {
  searchAddressData.value = []
  searchLoading.value = true
  const data: any = {
    key: gConfig.gdApiKey,
  }
  // 关键词，比如搜索餐厅
  if (keywords) {
    data.keywords = keywords
  }
  // 搜索的城市（可选）
  if (city) {
    data.city = city
  }
  // 中心点坐标（经度,纬度）
  if (latitude) {
    data.location = `${longitude},${latitude}`
  }
  // 搜索半径，单位米
  if (radius) {
    data.radius = radius
  }
  uni.request({
    url: 'https://restapi.amap.com/v3/place/text', // 高德地图POI搜索API
    method: 'GET',
    data: data,
    success: (res: any) => {
      if (res.data.status === '1') {
        const location = [
          res.data.pois[0].location.split(',')[0],
          res.data.pois[0].location.split(',')[1],
        ]
        getAddress(location[0], location[1], 3)
        // searchAddressData.value = res.data.pois;
      } else {
        uni.showToast({
          title: '搜索失败',
          icon: 'none',
        })
      }
      searchLoading.value = false
    },
    fail: (err) => {
      uni.showToast({
        title: '请求失败',
        icon: 'none',
      })
      console.error(err)
    },
  })
}
const highlightText = (text: any) => {
  const highlighted = text.replace(
    new RegExp(addressValue.value, 'gi'),
    (match: any) => `<span class="highlight">${match}</span>`
  )
  return highlighted
}
// 搜索地址点击
const selectedAddress: any = ref({}) //点击地址列表后选中的地址的返回值
const selectedAddressShow: any = ref({}) //回显，页面中搜索项中展示信息
const addressId = ref('') //点击记录地址的具体id
const searchAddressClick = (item: any) => {
  const location = [item.location.split(',')[0], item.location.split(',')[1]]
  addressId.value = item.id
  getAddress(location[0], location[1], 2)
}
// 当前位置信息
const currentLocation: any = ref({})
const currentLocationClick = () => {
  submitAddressHandle(1)
}
// 获取地址  type =1 给推荐地址赋值  type=2  给选中地址赋值  type=3  输入关键字获取第一个坐标根据坐标拿到所有点
const getAddress = (longitude: any, latitude: any, type: any = 1) => {
  // 反向地理编码 API 请求
  uni.request({
    url: `https://restapi.amap.com/v3/geocode/regeo`,
    method: 'GET',
    data: {
      key: gConfig.gdApiKey,
      location: `${longitude},${latitude}`, // 经纬度
      // radius: 1000, // 搜索半径
      extensions: 'all', // 获取更多的地址信息
    },
    success: (res: any) => {
      if (res.data.status === '1') {
        if (type == 1) {
          currentLocation.value = res.data.regeocode
        } else if (type == 2) {
          selectedAddress.value = res.data.regeocode
          submitAddressHandle(2)
        } else if (type == 3) {
          searchAddressData.value = res.data.regeocode
        }
      } else {
        uni.showToast({
          title: '地址获取失败',
          icon: 'none',
        })
      }
    },
    fail: (err) => {
      uni.showToast({
        title: '请求失败',
        icon: 'none',
      })
      console.error(err)
    },
  })
}
// 处理最终选中地址  num:1 点击推荐地址默认拿第一个详细地址   num:2 点击搜索后地址，拿点击地址的对应id详情地址  num3:输入地址保存后，不返回上一页
const submitAddressHandle = (num: any) => {
  let obj: any = { ...selectedAddressShow.value }
  if (num == 2) {
    let detail: any = {}
    for (let item of selectedAddress.value.pois) {
      if (item.id == addressId.value) {
        detail = item
      }
    }
    obj.addressComponent = selectedAddress.value.addressComponent
    obj.detail = detail
  } else if (num == 1) {
    obj.addressComponent = currentLocation.value.addressComponent
    obj.detail = currentLocation.value.pois[0]
  } else if (num == 3) {
    obj.contact = contackInfo
  }
  if (addressType.value == 1) {
    // 缓存发货地址
    if (setStoragetItem('shipmentsAddress', obj)) {
      selectedAddressShow.value = obj
      if (num != 3) {
        addressValue.value = selectedAddressShow.value.detail.name
        uni.navigateBack()
      }
    }
  } else if (addressType.value == 2) {
    // 缓存收货地址
    if (setStoragetItem('placeAddress', obj)) {
      selectedAddressShow.value = obj
      if (num != 3) {
        addressValue.value = selectedAddressShow.value.detail.name
        uni.navigateBack()
      }
    }
  }
}

// 获取当前定位
const initLocationInfo = () => {
  uni.getLocation({
    type: 'gcj02', //app直接获取地理位置信息要使用gcj02
    geocode: true, //必须要将geocode配置为true
    success: function (res: any) {
      getAddress(res.longitude, res.latitude)
    },
    fail: function (error) {
      console.error('获取位置失败：', error)
    },
  })
}
// 确定按钮
const isFocus = ref(false)
const confirmBtnClick = () => {
  if (!addressValue.value) {
    isFocus.value = false
    setTimeout(function () {
      uni.showToast({
        title: '请输入详细地址',
        icon: 'none',
      })
      isFocus.value = true
    }, 10)
  }
}
const initAddress = () => {
  let localAddressObj: any = {}
  if (addressType.value == 1) {
    localAddressObj = getStoragetItem('shipmentsAddress')
  } else if (addressType.value == 2) {
    localAddressObj = getStoragetItem('placeAddress')
  }
  if (localAddressObj) {
    selectedAddressShow.value = localAddressObj
    addressValue.value = selectedAddressShow.value?.detail?.name || ''
    contackInfo = selectedAddressShow.value.contact || {}
  }
}
onLoad((options) => {
  initLocationInfo()
  // 反选地址
  addressType.value = options.addressType
  initAddress()
})
</script>

<style scoped lang="scss">
.input-text-box-active {
  width: 100%;
  position: fixed;
  top: v-bind(headerHeight);
  margin-top: 0rpx !important;
  z-index: 1;
  background-color: $uni-bg-color-grey;
}

.map-btn-box {
  width: 100%;
  padding: 0rpx 20rpx 0rpx 20rpx;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 0;

  .map-btn {
    padding: 20rpx 0rpx 20rpx 0rpx;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: $uni-font-size-base;
    color: $uni-text-color;
    background-color: $uni-bg-color-grey;

    .iconfont {
      font-size: $uni-font-size-sm;
      color: $uni-text-color;
      margin-left: 6rpx;
    }
  }
}

.input-text-box {
  margin-top: v-bind(headerHeight);
  padding: 20rpx $uni-padding-left-right 0rpx $uni-padding-left-right;
  box-sizing: border-box;

  .input-text {
    background-color: $uni-bg-color;
    padding: $uni-padding-left-right;
    border-radius: 10rpx;

    .input-region {
      background-color: $uni-bg-color-grey;
      padding: 0rpx $uni-padding-left-right 100rpx $uni-padding-left-right;
      box-sizing: border-box;
      position: relative;
      border-radius: 20rpx;

      :deep(.uni-easyinput) {
        .is-textarea {
          background-color: $uni-bg-color-grey !important;

          .uni-easyinput__content-textarea {
            min-height: 60rpx;
            height: auto;
            max-height: 200rpx;

            .uni-textarea-wrapper {
              .uni-textarea-placeholder {
                font-size: $uni-font-size-lg;
                font-weight: bold;
                color: #ea9518;
              }

              .uni-textarea-textarea {
                font-size: $uni-font-size-lg;
                font-weight: bold;
                color: #000;
              }
            }
          }
        }
      }

      .sub-address {
        font-size: $uni-font-size-base;
        color: $uni-text-color;
      }

      .clear-btn {
        font-size: $uni-font-size-base;
        color: $uni-text-color;
        border: 1px solid $uni-border-color;
        border-radius: 30rpx;
        position: absolute;
        bottom: 20rpx;
        right: 20rpx;
        padding: 6rpx 20rpx;
        word-spacing: $uni-spacing-row-sm;
      }

      .paste-btn {
        word-spacing: $uni-spacing-row-sm;
        background-color: #ea9518;
        font-size: $uni-font-size-base;
        color: #fff;
        border: 1px solid #ea9518;
        border-radius: 30rpx;
        position: absolute;
        bottom: 20rpx;
        right: 20rpx;
        padding: 6rpx 20rpx;
      }
    }

    .tools {
      display: flex;
      margin-top: 10rpx;

      .contacts {
        display: flex;
        align-items: center;

        .iconfont {
          font-size: $uni-font-size-base;
          margin-right: 6rpx;
        }

        text {
          font-size: $uni-font-size-base;
        }
        .contact-light {
          color: #ea9518;
        }
      }
    }

    .choose-address {
      margin-top: 30rpx;

      .choose-title {
        font-size: $uni-font-size-base;
        color: $uni-text-color;
      }

      .choose-tips {
        display: flex;
        align-items: center;
        padding: 10rpx 20rpx;
        box-sizing: border-box;
        width: 100%;
        background-color: #ffe4b5;
        font-size: $uni-font-size-base;
        color: #ff8c00;
        border-radius: 10rpx;
        margin-top: 18rpx;

        .iconfont {
          margin-right: 8rpx;
        }
      }
    }
  }
}

.search-address-list {
  width: 100%;
  padding: 0rpx 20rpx;
  box-sizing: border-box;

  .empty-type {
    display: flex;
    justify-content: center;
    .empty-img {
      width: 400rpx;
      height: 400rpx;
    }
  }
  .address-list {
    background-color: #fff;
    padding: 0rpx 20rpx;
    .loading-box {
      display: flex;
      justify-content: center;
      .loading-car {
        width: 300rpx;
        height: 300rpx;
      }
    }

    .item {
      display: flex;
      align-items: center;
      padding: 20rpx 0rpx;
      border-bottom: 2rpx solid $uni-border-color;

      &:last-child {
        border-bottom: 0rpx;
      }

      .item-name {
        .big-name {
          font-size: $uni-font-size-lg;
          color: #000;
          font-weight: bold;
          :deep(.highlight) {
            color: #ff6347;
            font-weight: bold;
          }
        }

        .small-name {
          font-size: $uni-font-size-sm;
          color: $uni-text-color-grey;
          margin-top: 4rpx;
        }
      }

      .iconfont {
        font-size: 40rpx;
        color: #999;
        margin-right: 16rpx;
      }
    }
  }
}

.recommend-address-title {
  font-size: $uni-font-size-lg;
  font-weight: bold;
  padding: $uni-padding-left-right;
}

.recommend-list-box {
  padding: 0px $uni-padding-left-right 180rpx $uni-padding-left-right;
  box-sizing: border-box;

  .recommend-list {
    border-radius: 20rpx;

    .item {
      background-color: $uni-bg-color;
      padding: 26rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid $uni-text-color-disable;
      display: flex;
      align-items: center;
      &:last-child {
        border-bottom: none;
      }

      .iconfont {
        font-size: 40rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .big-name {
        width: 100%;
        font-size: $uni-font-size-lg;
        color: #000;
        font-weight: bold;
      }

      .tag {
        padding: 4rpx 8rpx;
        box-sizing: border-box;
        font-size: $uni-font-size-sm;
        border: 1rpx solid #ea9518;
        border-radius: 6rpx;
        color: #ea9518;
        margin-left: 10rpx;
        vertical-align: 4rpx;
      }

      .small-name {
        margin-top: 4rpx;
        font-size: $uni-font-size-sm;
        color: $uni-text-color-grey;
      }
    }
  }
}

.submit-box {
  width: 100%;
  padding: 20rpx 30rpx 20rpx 30rpx;
  box-sizing: border-box;
  background-color: $uni-bg-color;
  position: fixed;
  left: 0;
  bottom: 0;

  .btn {
    width: 100%;
    height: 100rpx;
    text-align: center;
    line-height: 100rpx;
    background-color: #ed782f;
    color: #fff;
    font-size: $uni-font-size-lg;
    border-radius: 20rpx;
  }
}
:deep(.contact-popup) {
  .uni-popup__wrapper {
    width: 100%;
    .popup-content {
      height: 500rpx;
      .content-box {
        width: 100%;
        padding: 0rpx 32rpx;
        box-sizing: border-box;
        .item {
          margin-top: 30rpx;
          padding-bottom: 10rpx;
          .label {
            font-size: $uni-font-size-base;
            color: #000;
          }
          .custom-easyinput {
            margin-top: 16rpx;
            .uni-easyinput__content {
              background-color: $uni-bg-color-grey !important;
              border-radius: $uni-border-radius-base;
            }
          }
        }
      }
    }
  }
}
</style>
