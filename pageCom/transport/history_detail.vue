<template>
	<view class="history-detail">
		<view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn>
				<text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
			</template>
			<template #title>{{ '' }}</template>
			<template #rightBtn>{{ '' }}</template>
		</cu-header>
		<cu-occupation></cu-occupation>
		<view class="main-container" :style="{ paddingBottom: safeAreaInsetsBottom + 20 + 'rpx' }">
			<view class="order-info">
				<view class="status-text">已成交</view>
				<view class="status-tip">货源已成交，欢迎再次下单</view>
			</view>
			<view class="load-upload">
				<view class="title-info">
					<view class="big">装卸信息</view>
					<view class="small">一装一卸</view>
				</view>
				<view class="time">{{ orderDetail.loadingDate }}{{ orderDetail.earliestLoadingTime }}</view>
				<view class="loading-info loading-info-line">
					<view class="loading-info-left">
						<view class="icon-text">装</view>
						<view class="loading-info-left-address">
							<view class="big-name">{{ orderDetail.loadingAddressName }}</view>
<!--
							<view class="small-name">北京市 西城区</view>
-->
						</view>
					</view>
				</view>
				<view class="loading-info unloading-info">
					<view class="loading-info-left">
						<view class="icon-text">卸</view>
						<view class="loading-info-left-address">
							<view class="big-name">{{ orderDetail.unloadingAddressName }}</view>
<!--
							<view class="small-name">北京市 西城区</view>
-->
						</view>
					</view>
				</view>
				<view class="mileage">
					<view class="mileage-text">
						<text class="text">预估运输里程</text>
						<text class="num">{{ estimatedDistance }}km</text>
						<uni-tooltip placement="top">
							<view class="iconfont icon-owner-tishi"></view>
							<template #content>根据所选车型车长，智能计算路线，仅供参考</template>
						</uni-tooltip>
					</view>
					<view class="mileage-line">
						<view class="iconfont icon-owner-dingwei"></view>
						<text>查看线路</text>
					</view>
				</view>
			</view>
			<view class="car-goods-info">
				<view class="title-info">
					<view class="big">车货信息</view>
				</view>
				<view class="cell-row-box">
					<cu-cell-two>
						<template #label>车辆</template>
						<template #value>{{ orderDetail.vehicleLength }}米 {{ orderDetail.vehicleType }}</template>
					</cu-cell-two>
					<cu-cell-two>
						<template #label>货物</template>
						<template #value>{{ orderDetail.cargoName }}，{{ orderDetail.maxWeight }}吨，{{ orderDetail.maxVolume }}方，{{ orderDetail.packagingMethod }}</template>
					</cu-cell-two>
					<cu-cell-two>
						<template #label>备注</template>
						<template #value>{{ orderDetail.remarks }}</template>
					</cu-cell-two>
<!--					<cu-cell-two>
						<template #label>跟车情况</template>
						<template #value>不跟车</template>
					</cu-cell-two>-->
				</view>
			</view>
			<view class="pay-info">
				<view class="title-info">
					<view class="big">支付信息</view>
				</view>
				<view class="cell-row-box">
					<cu-cell-two>
						<template #label>总运费</template>
						<template #value>
							<view class="price-info">
								<view class="price">{{ orderDetail.bidAmount }}元/趟</view>
							</view>
						</template>
					</cu-cell-two>
					<cu-cell-two>
						<template #label>订金</template>
						<template #value>
							<view class="price-info">
								<view class="price">{{ orderDetail.depositAmount }}元（退还）</view>
								<view class="detail">
									查看订单金额
									<view class="iconfont icon-owner-youjiantou"></view>
								</view>
							</view>
						</template>
					</cu-cell-two>
<!--					<cu-cell-two>
						<template #label>订金对则</template>
						<template #value>订金支付到平台用于订货订金，货主确认收货后由平台退还给司机</template>
					</cu-cell-two>
					<cu-cell-two>
						<template #label>总运费</template>
						<template #value>
							<view class="price-info">
								<view class="price">{{ orderDetail.depositAmount }}元</view>
							</view>
						</template>
					</cu-cell-two>-->
				</view>
			</view>
		</view>
		<cu-secure></cu-secure>
	</view>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getOrderInfoById} from '../../api/transport';
import CuOccupation from "../../components/cu-occupation/cu-occupation.vue";
import CuHeader from "../../components/cu-header/cu-header.vue";
import CuCellTwo from "../../components/cu-cell-two/cu-cell-two.vue";

const { safeAreaInsets } = uni.getSystemInfoSync();
const safeAreaInsetsBottom = safeAreaInsets.bottom * 2;
const heightHeader = safeAreaInsets.top * 2 + 80 + 'rpx';
// 定义响应式变量存储订单详情
const orderDetail = ref(null);

// 获取订单详情的函数
const fetchOrderDetail = async (id: string) => {
  try {
    // 调用接口获取订单详情
    const response: any = await getOrderInfoById(id);
    uni.hideLoading();
    if (response.code === 700) {
      orderDetail.value = response.result;
      calculateDistance();
      console.log(orderDetail.value);
    } else {
      console.error('获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
  }
};
onMounted(() => {
  const pages = getCurrentPages();
  if (pages.length > 1) {
    const currentPage = pages[pages.length - 1];
    // 从页面实例获取事件通道
    const eventChannel = currentPage.$vm.getOpenerEventChannel();
    eventChannel.on('orderId', (data) => {
      const id = data.id;
      if (id) {
        // 若获取到 id，调用获取订单详情函数
        fetchOrderDetail(id);
      } else {
        console.error('未获取到订单 ID');
      }
    });
  }
});
// 计算距离的函数
const estimatedDistance = ref(null);
const calculateDistance = () => {
  const startLng = orderDetail.value.loadingLongitude;
  const startLat = orderDetail.value.loadingLatitude;
  const endLng = orderDetail.value.unloadingLongitude;
  const endLat = orderDetail.value.unloadingLatitude;
  const R = 6371; // 地球半径，单位：公里
  const dLat = (endLat - startLat) * (Math.PI / 180);
  const dLng = (endLng - startLng) * (Math.PI / 180);
  const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(startLat * (Math.PI / 180)) *
      Math.cos(endLat * (Math.PI / 180)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  estimatedDistance.value = distance.toFixed(1);
};
</script>

<style scoped lang="scss">
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 400rpx;
	background-image: linear-gradient(#ee9209, #f3f3f3);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(#ee9209, #f3f3f3);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
		.left-btn > .iconfont {
			color: #fff;
			font-weight: 600;
		}
	}
}
.main-container {
	position: relative;
	z-index: 1;
	padding: 0rpx 32rpx;
	box-sizing: border-box;
}
.order-info {
	padding: 0rpx 32rpx;
	box-sizing: border-box;
	color: #fff;
	margin-top: 10rpx;
	.status-text {
		font-size: 32rpx;
	}
	.status-tip {
		font-size: 20rpx;
		margin-top: 8rpx;
	}
}
.load-upload {
	background-color: #fff;
	border-radius: 10rpx;
	margin-top: 50rpx;
	.title-info {
		padding: 28rpx 32rpx 0rpx 32rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		.big {
			font-size: $uni-font-size-base-30;
			line-height: $uni-font-size-base-30;
			color: $uni-text-color;
		}
		.small {
			font-size: 22rpx;
			color: $uni-text-color-grey;
			margin-left: 20rpx;
		}
	}
	.time {
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		font-size: $uni-font-size-sm;
		color: $uni-text-color-grey;
		margin-top: 28rpx;
		margin-bottom: 18rpx;
	}
	.loading-info-line {
		position: relative;
		&:before {
			position: absolute;
			left: 52rpx;
			top: 40rpx;
			content: '';
			width: 2rpx;
			height: 100%;
			border-left: 2rpx dashed $uni-text-color-grey;
		}
	}
	.loading-info {
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		.loading-info-left {
			display: flex;
			align-items: flex-start;
			flex: 1;
			.icon-text {
				width: 40rpx;
				height: 40rpx;
				text-align: center;
				line-height: 40rpx;
				color: #fff;
				background-color: blue;
				font-size: $uni-font-size-base;
				border-radius: 10rpx;
				margin-right: 14rpx;
			}
			.loading-info-left-address {
				flex: 1;
				.big-name {
					font-size: $uni-font-size-base-30;
					color: $uni-text-color;
				}
				.small-name {
					font-size: $uni-font-size-base;
					color: $uni-text-color-grey;
					margin-top: 8rpx;
				}
			}
		}
	}
	.unloading-info {
		margin-top: 40rpx;
		margin-bottom: 28rpx;
		.loading-info-left {
			.icon-text {
				background-color: #ee7942;
			}
		}
	}
	.mileage {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 20rpx 32rpx 24rpx 32rpx;
		box-sizing: border-box;
		border-top: 2rpx solid $uni-bg-color-grey;
		.mileage-text {
			display: flex;
			align-items: center;
			.text {
				font-size: 22rpx;
				color: $uni-text-color-grey;
			}
			.num {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
				margin-left: 6rpx;
			}
			:deep(.uni-tooltip) {
				margin-left: 4rpx;
				.iconfont {
					color: $uni-text-color-grey;
					font-size: 28rpx;
					margin-top: 6rpx;
				}
				.uni-tooltip-popup {
					min-width: 300rpx;
					font-size: 24rpx;
				}
			}
		}
		.mileage-line {
			display: flex;
			align-items: center;
			.iconfont {
				color: #ee9209;
			}
			text {
				font-size: $uni-font-size-sm;
				color: $uni-text-color;
			}
		}
	}
}
.car-goods-info {
	background-color: #fff;
	border-radius: 10rpx;
	margin-top: 20rpx;
	.title-info {
		padding: 28rpx 32rpx 0rpx 32rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		.big {
			font-size: $uni-font-size-base-30;
			line-height: $uni-font-size-base-30;
			color: $uni-text-color;
		}
	}
	.cell-row-box {
		padding: 32rpx 32rpx;
		box-sizing: border-box;
	}
}
.pay-info {
	background-color: #fff;
	border-radius: 10rpx;
	margin-top: 20rpx;
	.title-info {
		padding: 28rpx 32rpx 0rpx 32rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		.big {
			font-size: $uni-font-size-base-30;
			line-height: $uni-font-size-base-30;
			color: $uni-text-color;
		}
	}
	.cell-row-box {
		padding: 32rpx 32rpx;
		box-sizing: border-box;
		:deep(.cell-row) {
			.cell-value > .price-info {
				display: flex;
				align-items: center;
				.price {
					color: #ee9209;
				}
				.detail {
					display: flex;
					align-items: center;
					color: #4778d6;
					.iconfont {
						font-size: 22rpx;
					}
				}
			}
		}
	}
}
</style>
