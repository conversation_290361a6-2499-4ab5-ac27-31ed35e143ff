<template>
  <view class="order-info">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text></template>
			<template #title>
        <text>订单明细</text>
      </template>
			<template #rightBtn>
				{{ '' }}
			</template>
		</cu-header>
    <view class="main-container">
      <view class="content">
        <view class="text-con">
          <view>订金 <span class="text-color">(可退)</span></view>
          <view>¥ {{ money.toFixed(2) }}</view>
        </view>
        <view class="list-con">
          <view class="list-item">
            <view>订单编号</view>
            <view class="text-color">{{ orderCode || '暂无订单编号' }}</view>
          </view>
          <view class="list-item">
            <view>下单时间</view>
            <view class="text-color">{{ orderTime || '暂无时间信息' }}</view>
          </view>
          <view class="list-item">
            <view>货物信息</view>
            <view class="text-color">{{ goodsInfo || '暂无货物信息' }}</view>
          </view>
          <view class="list-item">
            <view>备注信息</view>
            <view class="text-color">{{ remark || '无' }}</view>
          </view>
          <view class="list-item">
            <view>车辆信息</view>
            <view class="text-color">{{ carInfo || '暂无车辆信息' }}</view>
          </view>
        </view>
      </view>
      
    </view>
  </view>
</template>
<script setup lang="ts">
import {onMounted, ref, reactive} from 'vue';
import { formatDateTime } from '@/utils/common';
const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';

// 响应式数据定义
const money = ref(0)
const orderCode = ref('')
const orderTime = ref('')
const goodsInfo = ref('')
const remark = ref('')
const carInfo = ref('')

// 接收订单数据的函数
const handleOrderData = (data: any) => {
  console.log('接收到订单数据:', data)

  // 更新订金金额
  money.value = data.depositAmount || 0

  // 更新订单编号
  orderCode.value = data.orderId || ''

  // 格式化并更新订单时间
  if (data.orderTime) {
    orderTime.value = formatDateTime(data.orderTime)
  } else if (data.loadingDate && data.earliestLoadingTime) {
    // 如果没有orderTime，使用装货日期和时间
    orderTime.value = `${data.loadingDate} ${data.earliestLoadingTime}`
  }

  // 更新货物信息
  goodsInfo.value = data.cargoName || '暂无货物信息'

  // 更新备注信息
  remark.value = data.remark || '无'

  // 格式化车辆信息
  if (data.vehicleLength && data.vehicleType) {
    carInfo.value = `${data.vehicleLength} ${data.vehicleType}`
  } else if (data.vehicleType) {
    carInfo.value = data.vehicleType
  } else {
    carInfo.value = '暂无车辆信息'
  }

  // 如果有完整的订单详情，可以获取更多信息
  if (data.orderDetail) {
    const detail = data.orderDetail
    // 可以根据需要添加更多字段的处理
    console.log('完整订单详情:', detail)
  }
}



onMounted(() => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    const currentPage = pages[pages.length - 1]
    // 从页面实例获取事件通道
    const eventChannel = currentPage.$vm.getOpenerEventChannel()
    eventChannel.on('orderInfo', (data) => {
      console.log('接收到订单信息:', data)
      handleOrderData(data)
    })
  }
})

</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
  .team-name {
    display: block;
    font-size: 24rpx;
    color: #e58814;
    text-align: center;
  }
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding-top: v-bind(heightHeader);
  .content {
    width: 94%;
    padding: 0 3%;
  }
  .text-con {
    background-color: #fff;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    border-radius: 10rpx;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    .text-color {
      color: #ccc;
    }
  }
  .list-con {
    background-color: #fff;
    padding: 20rpx;
    margin-top: 20rpx;
    border-radius: 10rpx;
    .list-item {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      height: 72rpx;
      line-height: 72rpx;
      .text-color {
        color: #ccc;
        width: 60%;
        text-align: right;
        white-space: nowrap;
        overflow: hidden;         /* 隐藏溢出的文本 */
        text-overflow: ellipsis; 
      }
    }
  }
}
</style>