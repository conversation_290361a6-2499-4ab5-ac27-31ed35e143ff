<template>
  <view class="completed">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
      <template #leftBtn>
        <text class="iconfont icon-owner-a-fanhuizu<PERSON>ji<PERSON><PERSON>"></text>
      </template>
      <template #title>{{ '' }}</template>
      <template #rightBtn>{{ '' }}</template>
    </cu-header>
    <cu-occupation></cu-occupation>
    <view class="main-container" :style="{ paddingBottom: safeAreaInsetsBottom + 140 + 'rpx' }">
      <view class="order-status">
        <view class="status-text">{{
          getOrderStatusText(orderDetail.orderStatus)
          }}</view>
        <view class="status-handle">
          <view class="handle-item" @click="callBtn">
            <view class="iconfont icon-owner-dianhua1"></view>
            <text>打电话</text>
          </view>
          <!--					<view class="handle-item">
						<view class="iconfont icon-owner-pingjia"></view>
						<text>评价司机</text>
					</view>-->
          <view class="handle-item" @click="oneMoreBtn">
            <view class="iconfont icon-owner-zailaiyidan"></view>
            <text>再来一单</text>
          </view>
          <!--					<view class="handle-item">
						<view class="iconfont icon-owner-shanchu"></view>
						<text>删除订单</text>
					</view>-->
        </view>
      </view>
      <view class="amount-status">
        <view class="status-item" @click="toDeposit">
          <view class="status-item-left">
            <text class="label">订金</text>
            <view class="value">
              <text>¥{{ orderDetail.depositAmount }}退还处理成功，预计</text>
              <text class="light">2024.09.29</text>
              <text>退还到您的账户余额</text>
            </view>
          </view>
          <view class="iconfont icon-owner-youjiantou"></view>
        </view>
      </view>
      <view class="trajectory card-box">
        <view class="trajectory-info card-header">
          <view class="info-left">
            <text class="big">运输轨迹</text>
            <text class="small">预估{{ estimatedDistance }}公里</text>
          </view>
          <view class="info-right" @tap="viewTrajectory">
            <text>查看轨迹</text>
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>
        <view class="map-view">
          <map id="trajectoryMap" class="trajectory-map" :latitude="mapCenter.latitude" :longitude="mapCenter.longitude" :scale="mapScale" :markers="mapMarkers" :polyline="mapPolyline" :show-location="false" :enable-zoom="true" :enable-scroll="true" :enable-rotate="false" :enable-overlooking="false" :enable-3D="false"></map>
        </view>
      </view>
      <view class="order-info card-box">
        <view class="card-header" @click="toOrderInfo">
          <view class="info-left">
            <view class="big">订单信息</view>
          </view>
          <view class="info-right">
            <text>查看更多</text>
            <view class="iconfont icon-owner-youjiantou"></view>
          </view>
        </view>
        <view class="time">{{ formatDate(orderDetail.loadingDate) }}
          {{ orderDetail.earliestLoadingTime }}</view>
        <view class="loading-info loading-info-line">
          <view class="loading-info-left">
            <view class="icon-text">装</view>
            <view class="loading-info-left-address">
              <view class="big-name">{{ orderDetail.loadingAddressName }}</view>
              <view class="small-name">{{ orderDetail.loadingAddress?.province }}
                {{ orderDetail.loadingAddress?.county }}</view>
            </view>
          </view>
        </view>
        <view class="loading-info unloading-info">
          <view class="loading-info-left">
            <view class="icon-text">卸</view>
            <view class="loading-info-left-address">
              <view class="big-name">{{
                orderDetail.unloadingAddressName
                }}</view>
              <view class="small-name">{{ orderDetail.dischargeAddress?.province }}
                {{ orderDetail.dischargeAddress?.county }}</view>
            </view>
          </view>
        </view>
        <view class="order-cargo ellipsis">{{ orderDetail.cargoName }}</view>
      </view>
      <view class="driver-car card-box">
        <view class="driver-left">
          <image src="@/static/images/head.png" class="head"></image>
          <view class="driver-left-right">
            <view class="name">{{ orderDetail.driverName }}</view>
            <view class="phone">{{ orderDetail.driverInfo?.mobile }} |
              {{ orderDetail.driverInfo?.licensePlateNumber }}</view>
          </view>
        </view>
        <view class="driver-right" @click="callBtn">
          <view class="icon-box">
            <view class="iconfont icon-owner-dianhua1"></view>
          </view>
        </view>
      </view>
      <view class="type-class card-box" v-if="orderDetail.orderStatus > 6">
        <view class="type-left">订单图片</view>
        <view class="type-right" @click="toOrderPic">
          <text>装卸货图片、回单照片</text>
          <view class="iconfont icon-owner-youjiantou"></view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" :style="{ paddingBottom: safeAreaInsetsBottom + 'rpx' }">
      <!-- 更多操作展开区域 -->
      <view class="more-actions-panel" v-show="isMoreActionsExpanded">
        <view class="more-actions-buttons">
          <button class="more-action-btn" v-if="checkUpdateOrderStatus(7)" @click="applyDeductDeposit">
            <text class="more-btn-text">申请扣订金</text>
          </button>
          <button class="more-action-btn" v-if="checkUpdateOrderStatus(8)" @click="agreeRefund">
            <text class="more-btn-text">同意退还</text>
          </button>
          <button class="more-action-btn" v-if="checkUpdateOrderStatus(9)" @click="rejectRefund">
            <text class="more-btn-text">拒绝退还</text>
          </button>
          <button class="more-action-btn" v-if="checkUpdateOrderStatus(10)" @click="applyArbitration">
            <text class="more-btn-text">申请仲裁</text>
          </button>
          <button class="more-action-btn" v-if="checkUpdateOrderStatus(11)" @click="withdrawDeductDeposit">
            <text class="more-btn-text">撤回扣订金</text>
          </button>
        </view>
      </view>

      <view class="action-buttons">
        <!-- 更多按钮 -->
        <button class="action-btn secondary more-btn" v-if="checkUpdateOrderStatus(6)" @click="toggleMoreActions">
          <view class="btn-content">
            <text class="btn-text">{{
              isMoreActionsExpanded ? '收起' : '更多'
              }}</text>
          </view>
        </button>

        <button class="action-btn secondary" v-if="checkUpdateOrderStatus(1)" @click="openPriceEditDialog">
          <view class="btn-content">
            <text class="btn-text">改价</text>
          </view>
        </button>
        <button class="action-btn secondary" v-if="checkUpdateOrderStatus(2)" @click="cancelPriceEdit">
          <view class="btn-content">
            <text class="btn-text">撤销改价</text>
          </view>
        </button>
        <button class="action-btn secondary" v-if="checkUpdateOrderStatus(3)" @click="cancelOrder">
          <view class="btn-content">
            <text class="btn-text">取消订单</text>
          </view>
        </button>
        <button class="action-btn primary" v-if="checkUpdateOrderStatus(4)" @click="confirmReceipt">
          <view class="btn-content">
            <text class="btn-text">确认收货</text>
          </view>
        </button>
        <button class="action-btn primary" v-if="checkUpdateOrderStatus(5)" @click="toPayFreight">
          <view class="btn-content">
            <text class="btn-text">支付运费</text>
          </view>
        </button>
      </view>
    </view>

    <!-- 订金流程弹窗 -->
    <cu-payment-dialog ref="paymentDialog" :deposit-info="depositDialogData" @close="handlePaymentDialogClose" />

    <!-- 改价弹窗 -->
    <cu-price-edit-dialog ref="priceEditDialog" :show-deposit="false" :initial-price="editOrderData.price" :initial-unit-text="editOrderData.unitText" @cancel="handlePriceEditCancel" @confirm="handlePriceEditConfirm" />

    <!-- 撤销改价确认弹窗 -->
    <cu-confirm-dialog ref="cancelPriceEditDialog" :title="cancelPriceEditDialogData.title" :content="cancelPriceEditDialogData.content" :cancel-text="cancelPriceEditDialogData.cancelText" :confirm-text="cancelPriceEditDialogData.confirmText" @cancel="handleCancelPriceEditCancel" @confirm="handleCancelPriceEditConfirm" />

    <!-- 确认收货确认弹窗 -->
    <cu-confirm-dialog ref="confirmReceiptDialog" :title="confirmReceiptDialogData.title" :content="confirmReceiptDialogData.content" :cancel-text="confirmReceiptDialogData.cancelText" :confirm-text="confirmReceiptDialogData.confirmText" @cancel="handleConfirmReceiptCancel" @confirm="handleConfirmReceiptConfirm" />

    <!-- 支付运费确认弹窗 -->
    <cu-confirm-dialog ref="payFreightDialog" :title="payFreightDialogData.title" :content="payFreightDialogData.content" :cancel-text="payFreightDialogData.cancelText" :confirm-text="payFreightDialogData.confirmText" @cancel="handlePayFreightCancel" @confirm="handlePayFreightConfirm" />

    <!-- 取消订单确认弹窗 -->
    <cu-confirm-dialog ref="cancelOrderDialog" :title="cancelOrderDialogData.title" :content="cancelOrderDialogData.content" :important-tips="cancelOrderDialogData.importantTips" cancel-text="取消" confirm-text="确认取消" @cancel="handleCancelOrderCancel" @confirm="handleCancelOrderConfirm" />

    <!-- 申请扣订金弹窗 -->
    <cu-apply-deposit-dialog ref="applyDeductDepositDialog" type="owner" @cancel="handleApplyDeductDepositCancel" @confirm="handleApplyDeductDepositConfirm" @upload-success="handleApplyDepositUploadSuccess" @upload-fail="handleApplyDepositUploadFail" />

    <!-- 同意退还确认弹窗 -->
    <cu-confirm-dialog ref="agreeRefundDialog" :title="agreeRefundDialogData.title" :content="agreeRefundDialogData.content" :cancel-text="agreeRefundDialogData.cancelText" :confirm-text="agreeRefundDialogData.confirmText" @cancel="handleAgreeRefundCancel" @confirm="handleAgreeRefundConfirm" />

    <!-- 拒绝退还确认弹窗 -->
    <cu-confirm-dialog ref="rejectRefundDialog" :title="rejectRefundDialogData.title" :content="rejectRefundDialogData.content" :cancel-text="rejectRefundDialogData.cancelText" :confirm-text="rejectRefundDialogData.confirmText" @cancel="handleRejectRefundCancel" @confirm="handleRejectRefundConfirm" />

    <!-- 申请仲裁确认弹窗 -->
    <cu-confirm-dialog ref="applyArbitrationDialog" :title="applyArbitrationDialogData.title" :content="applyArbitrationDialogData.content" :cancel-text="applyArbitrationDialogData.cancelText" :confirm-text="applyArbitrationDialogData.confirmText" @cancel="handleApplyArbitrationCancel" @confirm="handleApplyArbitrationConfirm" />

    <!-- 撤回扣订金确认弹窗 -->
    <cu-confirm-dialog ref="withdrawDeductDepositDialog" :title="withdrawDeductDepositDialogData.title" :content="withdrawDeductDepositDialogData.content" :cancel-text="withdrawDeductDepositDialogData.cancelText" :confirm-text="withdrawDeductDepositDialogData.confirmText" @cancel="handleWithdrawDeductDepositCancel" @confirm="handleWithdrawDeductDepositConfirm" />

    <cu-secure></cu-secure>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getOrderInfoById,
  editOrderInfo,
  deleteOrderInfo,
  editOrderStatus
} from '../../api/transport'
import { makeCall } from '@/utils/phone'
import { formatDate } from '@/utils/common'
import CuPaymentDialog from '../../components/cu-payment-dialog/cu-payment-dialog.vue'
import CuPriceEditDialog from '../../components/cu-price-edit-dialog/cu-price-edit-dialog.vue'
import CuConfirmDialog from '../../components/cu-confirm-dialog/cu-confirm-dialog.vue'
import CuApplyDepositDialog from '../../components/cu-apply-deposit-dialog/cu-apply-deposit-dialog.vue'

const systemInfo: any = uni.getSystemInfoSync()
const safeAreaInsetsBottom = systemInfo.safeAreaInsets.bottom * 2
const heightHeaderBg = systemInfo.safeAreaInsets.top * 2 + 70 + 100 + 'rpx'
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx'
const orderDetail = ref({})

// 地图相关数据
const mapCenter = ref({
  latitude: 39.9042,
  longitude: 116.4074,
})
const mapMarkers = ref([])
const mapPolyline = ref([])
const mapScale = ref(10)

// 支付弹窗引用
const paymentDialog = ref(null)

// 订金弹窗数据
const depositDialogData = ref({
  amount: '0',
  timeline: [],
})

// 改价弹窗引用
const priceEditDialog = ref(null)

// 改价数据
const editOrderData = ref({
  orderId: '',
  price: '',
  unitText: '趟',
})

// 撤销改价确认弹窗引用
const cancelPriceEditDialog = ref(null)

// 撤销改价弹窗数据
const cancelPriceEditDialogData = ref({
  title: '撤销改价',
  content: '确定要撤销改价吗？撤销后订单状态将变为已接单。',
  cancelText: '取消',
  confirmText: '确定撤销',
})

// 确认收货确认弹窗引用
const confirmReceiptDialog = ref(null)

// 确认收货弹窗数据
const confirmReceiptDialogData = ref({
  title: '确认收货',
  content: '确定要确认收货吗？确认后订单将完成。',
  cancelText: '取消',
  confirmText: '确认收货',
})

// 支付运费确认弹窗引用
const payFreightDialog = ref(null)

// 支付运费弹窗数据
const payFreightDialogData = ref({
  title: '支付运费报备',
  content:
    '此功能仅用于报备，请确认您已完成线下支付。点击确认后将记录您的支付报备信息。',
  cancelText: '取消',
  confirmText: '确认报备',
})

// 取消订单确认弹窗引用
const cancelOrderDialog = ref(null)

// 取消订单弹窗数据
const cancelOrderDialogData = ref({
  title: '',
  content: '',
  importantTips: '',
})

// 当前要取消的订单ID
const currentCancelOrderId = ref('')

// 更多按钮展开状态
const isMoreActionsExpanded = ref(false)

// 申请退订金弹窗引用
const applyDeductDepositDialog = ref(null)

// 同意退还确认弹窗引用和数据
const agreeRefundDialog = ref(null)
const agreeRefundDialogData = ref({
  title: '同意退还',
  content: '确定要同意退还订金吗？',
  cancelText: '取消',
  confirmText: '确认同意',
})

// 拒绝退还确认弹窗引用和数据
const rejectRefundDialog = ref(null)
const rejectRefundDialogData = ref({
  title: '拒绝退还',
  content: '确定要拒绝退还订金吗？',
  cancelText: '取消',
  confirmText: '确认拒绝',
})

// 申请仲裁确认弹窗引用和数据
const applyArbitrationDialog = ref(null)
const applyArbitrationDialogData = ref({
  title: '申请仲裁',
  content: '确定要申请仲裁吗？',
  cancelText: '取消',
  confirmText: '确认申请',
})

// 撤回扣订金确认弹窗引用和数据
const withdrawDeductDepositDialog = ref(null)
const withdrawDeductDepositDialogData = ref({
  title: '撤回扣订金',
  content: '确定要撤回扣订金申请吗？',
  cancelText: '取消',
  confirmText: '确认撤回',
})

// 支付运费报备
const toPayFreight = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  console.log('打开支付运费报备确认弹窗')

  // 打开支付运费确认弹窗
  payFreightDialog.value?.open()
}

// 处理支付运费取消
const handlePayFreightCancel = () => {
  console.log('用户取消支付运费报备')
}

// 处理支付运费确认
const handlePayFreightConfirm = () => {
  try {
    console.log('确认支付运费报备，订单ID:', orderDetail.value.id)

    // 显示报备成功提示
    uni.showToast({
      title: '支付运费报备成功',
      icon: 'success',
      duration: 2000,
    })

    // 这里不调用真正的支付接口，仅作为报备记录
    // 如果后续需要记录报备状态，可以在这里添加相关API调用
  } catch (error) {
    console.error('支付运费报备失败:', error)
    uni.showToast({
      title: '报备失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 取消订单
const cancelOrder = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  // 设置弹窗数据
  cancelOrderDialogData.value = {
    title: '确认取消订单',
    content: '确定要取消这个订单吗？',
    importantTips: `${orderDetail.value?.loadingAddressName || '未知'} → ${orderDetail.value?.unloadingAddressName || '未知'
      }`,
  }

  // 保存要取消的订单ID
  currentCancelOrderId.value = orderDetail.value?.id

  console.log('打开取消订单确认弹窗')

  // 打开确认弹窗
  cancelOrderDialog.value?.open()
}

// 处理取消订单取消
const handleCancelOrderCancel = () => {
  // 清空当前取消的订单ID
  currentCancelOrderId.value = ''
  console.log('用户取消取消订单')
}

// 处理取消订单确认
const handleCancelOrderConfirm = async () => {
  if (currentCancelOrderId.value) {
    await handleDeleteOrder(currentCancelOrderId.value)
    currentCancelOrderId.value = ''
  }
}

// 执行取消订单操作
const handleDeleteOrder = async (orderId: string) => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '取消中...',
      mask: true,
    })

    // 调用删除接口
    const response: any = await deleteOrderInfo(orderId)

    uni.hideLoading()

    if (response.code === 700) {
      // 取消成功
      uni.showToast({
        title: '取消成功',
        icon: 'success',
        duration: 1500,
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      // 取消失败
      uni.showToast({
        title: response.msg || '取消失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('取消订单失败:', error)
    uni.showToast({
      title: '取消失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 切换更多操作展开状态
const toggleMoreActions = () => {
  isMoreActionsExpanded.value = !isMoreActionsExpanded.value
}

// 申请退订金
const applyDeductDeposit = () => {
  console.log('打开申请退订金弹窗')
  applyDeductDepositDialog.value?.open()
}

// 同意退还
const agreeRefund = () => {
  console.log('打开同意退还确认弹窗')
  agreeRefundDialog.value?.open()
}

// 拒绝退还
const rejectRefund = () => {
  console.log('打开拒绝退还确认弹窗')
  rejectRefundDialog.value?.open()
}

// 申请仲裁
const applyArbitration = () => {
  console.log('打开申请仲裁确认弹窗')
  applyArbitrationDialog.value?.open()
}

// 撤回扣订金
const withdrawDeductDeposit = () => {
  console.log('打开撤回扣订金确认弹窗')
  withdrawDeductDepositDialog.value?.open()
}

// 申请退订金 - 取消
const handleApplyDeductDepositCancel = () => {
  console.log('用户取消申请退订金')
  isMoreActionsExpanded.value = false
}

// 申请退订金 - 确认
const handleApplyDeductDepositConfirm = async (formData) => {
  console.log('用户确认申请退订金，表单数据:', formData)

  const result: any = await editOrderStatus({
    id: orderDetail.value.id,
    depositOperate: 1,
    amountRemarks: formData.amountRemarks || '',
    amountImages: formData.amountImages || ''
  })
  if (result.code === 700) {
    uni.showToast({
      title: '申请退订金成功',
      icon: 'success',
      duration: 2000,
    })
    handleApplyDeductDepositCancel()
    setTimeout(async () => {
      await fetchOrderDetail(orderDetail.value.id)
    }, 1500)
  } else {
    uni.showToast({
      title: result.msg || '申请退订金失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 申请退订金图片上传成功
const handleApplyDepositUploadSuccess = (result) => {
  console.log('申请退订金图片上传成功:', result)
}

// 申请退订金图片上传失败
const handleApplyDepositUploadFail = (error) => {
  console.log('申请退订金图片上传失败:', error)
  uni.showToast({
    title: '图片上传失败',
    icon: 'none'
  })
}

// 同意退还 - 取消
const handleAgreeRefundCancel = () => {
  console.log('用户取消同意退还')
  agreeRefundDialog.value?.close()
  isMoreActionsExpanded.value = false
}

// 同意退还 - 确认
const handleAgreeRefundConfirm = async () => {
  console.log('用户确认同意退还')
  const result: any = await editOrderInfo({
    id: orderDetail.value.id,
    depositStatus: 3,
    orderStatus: 99,
  })
  if (result.code === 700) {
    uni.showToast({
      title: '同意退还成功',
      icon: 'success',
      duration: 2000,
    })
    handleAgreeRefundCancel()
    setTimeout(async () => {
      await fetchOrderDetail(orderDetail.value.id)
    }, 1500)
  } else {
    uni.showToast({
      title: result.msg || '同意退还失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 拒绝退还 - 取消
const handleRejectRefundCancel = () => {
  console.log('用户取消拒绝退还')
  rejectRefundDialog.value?.close()
  isMoreActionsExpanded.value = false
}

// 拒绝退还 - 确认
const handleRejectRefundConfirm = async () => {
  console.log('用户确认拒绝退还')
  const result: any = await editOrderInfo({
    id: orderDetail.value.id,
    depositStatus: 4,
  })
  if (result.code === 700) {
    uni.showToast({
      title: '拒绝退还成功',
      icon: 'success',
      duration: 2000,
    })
    handleRejectRefundCancel()
    setTimeout(async () => {
      await fetchOrderDetail(orderDetail.value.id)
    }, 1500)
  } else {
    uni.showToast({
      title: result.msg || '拒绝退还失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 申请仲裁 - 取消
const handleApplyArbitrationCancel = () => {
  console.log('用户取消申请仲裁')
  applyArbitrationDialog.value?.close()
  isMoreActionsExpanded.value = false
}

// 申请仲裁 - 确认
const handleApplyArbitrationConfirm = async () => {
  console.log('用户确认申请仲裁')
  const result: any = await editOrderInfo({
    id: orderDetail.value.id,
    depositStatus: 5,
  })
  if (result.code === 700) {
    uni.showToast({
      title: '申请仲裁成功',
      icon: 'success',
      duration: 2000,
    })
    handleApplyArbitrationCancel()
    setTimeout(async () => {
      await fetchOrderDetail(orderDetail.value.id)
    }, 1500)
  } else {
    uni.showToast({
      title: result.msg || '申请仲裁失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 撤回扣订金 - 取消
const handleWithdrawDeductDepositCancel = () => {
  console.log('用户取消撤回扣订金')
  withdrawDeductDepositDialog.value?.close()
  isMoreActionsExpanded.value = false
}

// 撤回扣订金 - 确认
const handleWithdrawDeductDepositConfirm = async () => {
  console.log('用户确认撤回扣订金')
  const result: any = await editOrderInfo({
    id: orderDetail.value.id,
    depositStatus: 1,
  })
  if (result.code === 700) {
    uni.showToast({
      title: '撤回扣订金成功',
      icon: 'success',
      duration: 2000,
    })
    handleWithdrawDeductDepositCancel()
    setTimeout(async () => {
      await fetchOrderDetail(orderDetail.value.id)
    }, 1500)
  } else {
    uni.showToast({
      title: result.msg || '撤回扣订金失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 存储订单ID，用于在onShow中重新获取数据
const currentOrderId = ref('')

onMounted(() => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    const currentPage = pages[pages.length - 1]
    // 从页面实例获取事件通道
    const eventChannel = currentPage.$vm.getOpenerEventChannel()
    eventChannel.on('orderId', (data) => {
      const id = data.id
      if (id) {
        // 存储订单ID
        currentOrderId.value = id
        // 若获取到 id，调用获取订单详情函数
        fetchOrderDetail(id)
      } else {
        console.error('未获取到订单 ID')
      }
    })
  }
})
// 点击打电话
const callBtn = () => {
  makeCall(orderDetail.value.driverInfo.mobile)
}

// 点击再来一单
const oneMoreBtn = () => {
  uni.reLaunch({
    url: '/pages/index/index',
  })
}

// 点击查看订金流程
const toDeposit = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  // 构建订金流程数据
  depositDialogData.value.amount = orderDetail.value.depositAmount || '0'
  depositDialogData.value.timeline = orderDetail.value.depositProcesses || []

  console.log('订金流程数据:', depositDialogData.value)

  // 打开订金流程弹窗
  paymentDialog.value?.open('deposit')
}

// 处理支付弹窗关闭
const handlePaymentDialogClose = () => {
  console.log('订金流程弹窗关闭')
}

// 打开改价弹窗
const openPriceEditDialog = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  // 设置改价数据
  editOrderData.value = {
    orderId: orderDetail.value.id,
    price: orderDetail.value.bidAmount || '',
    unitText: orderDetail.value.bidType || '趟',
  }

  console.log('打开改价弹窗，数据:', editOrderData.value)

  // 打开改价弹窗
  priceEditDialog.value?.open()
}

// 撤销改价
const cancelPriceEdit = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  console.log('打开撤销改价确认弹窗')

  // 打开撤销改价确认弹窗
  cancelPriceEditDialog.value?.open()
}

// 处理撤销改价取消
const handleCancelPriceEditCancel = () => {
  console.log('用户取消撤销改价')
}

// 处理撤销改价确认
const handleCancelPriceEditConfirm = async () => {
  try {
    console.log('确认撤销改价，订单ID:', orderDetail.value.id)

    // 显示加载提示
    uni.showLoading({
      title: '撤销中...',
      mask: true,
    })

    // 调用撤销改价接口
    const response = await editOrderInfo({
      id: orderDetail.value.id,
      orderStatus: 1, // 将订单状态改为已接单
    })

    uni.hideLoading()

    if (response.code === 700) {
      uni.showToast({
        title: '撤销改价成功',
        icon: 'success',
        duration: 1500,
      })

      // 刷新订单详情数据
      setTimeout(async () => {
        await fetchOrderDetail(orderDetail.value.id)
      }, 1500)
    } else {
      uni.showToast({
        title: response.msg || '撤销改价失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('撤销改价失败:', error)
    uni.showToast({
      title: '撤销改价失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 处理改价取消
const handlePriceEditCancel = () => {
  console.log('用户取消改价')
}

// 处理改价确认
const handlePriceEditConfirm = async (data: any) => {
  try {
    console.log('改价确认数据:', data)

    // 显示加载提示
    uni.showLoading({
      title: '改价中...',
      mask: true,
    })

    // 调用改价接口
    const response = await editOrderInfo({
      id: editOrderData.value.orderId,
      notBidAmount: data.price,
      bidType: data.unitText,
      orderStatus: 2, // 改价待确认
    })

    uni.hideLoading()

    if (response.code === 700) {
      uni.showToast({
        title: '改价成功',
        icon: 'success',
        duration: 1500,
      })

      // 刷新订单详情数据
      setTimeout(async () => {
        await fetchOrderDetail(editOrderData.value.orderId)
      }, 1500)
    } else {
      uni.showToast({
        title: response.msg || '改价失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('改价失败:', error)
    uni.showToast({
      title: '改价失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 确认收货
const confirmReceipt = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  console.log('打开确认收货确认弹窗')

  // 打开确认收货确认弹窗
  confirmReceiptDialog.value?.open()
}

// 处理确认收货取消
const handleConfirmReceiptCancel = () => {
  console.log('用户取消确认收货')
}

// 处理确认收货确认
const handleConfirmReceiptConfirm = async () => {
  try {
    console.log('确认收货，订单ID:', orderDetail.value.id)

    // 显示加载提示
    uni.showLoading({
      title: '确认中...',
      mask: true,
    })

    // 调用确认收货接口
    const response = await editOrderInfo({
      id: orderDetail.value.id,
      orderStatus: 10, // 已完成
    })

    uni.hideLoading()

    if (response.code === 700) {
      uni.showToast({
        title: '确认收货成功',
        icon: 'success',
        duration: 1500,
      })

      // 刷新订单详情数据
      setTimeout(async () => {
        await fetchOrderDetail(orderDetail.value.id)
      }, 1500)
    } else {
      uni.showToast({
        title: response.msg || '确认收货失败',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('确认收货失败:', error)
    uni.showToast({
      title: '确认收货失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 点击跳转订单明细页面
const toOrderInfo = () => {
  uni.navigateTo({
    url: '/pageCom/orders/orderInfo',
    success: (res) => {
      // 使用 eventChannel 发送数据
      res.eventChannel.emit('orderInfo', {
        orderId: orderDetail.value.id,
        orderTime: orderDetail.value.orderTime,
        cargoName: orderDetail.value.cargoName,
        vehicleLength: orderDetail.value.vehicleLength,
        vehicleType: orderDetail.value.vehicleType,
        remark: orderDetail.value.remark,
        depositAmount: orderDetail.value.depositAmount,
        loadingDate: orderDetail.value.loadingDate,
        earliestLoadingTime: orderDetail.value.earliestLoadingTime,
        orderDetail: orderDetail.value, // 传递完整的订单详情
      })
    },
  })
}

// 点击跳转订单图片页面
const toOrderPic = () => {
  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  uni.navigateTo({
    url: '/pageComDriver/orders/order_pic',
    success: (res) => {
      // 使用 eventChannel 发送数据
      res.eventChannel.emit('orderInfo', {
        orderId: orderDetail.value.id,
        orderStatus: orderDetail.value.orderStatus,
        loadingImages: orderDetail.value.loadingImages || '',
        unloadingImages: orderDetail.value.unloadingImages || '',
        deliveryProofImages: orderDetail.value.deliveryProofImages || '',
        viewMode: 'owner', // 标识为货主查看模式
      })
    },
  })
}

// 获取订单详情的函数
const fetchOrderDetail = async (id: string) => {
  try {
    // 调用接口获取订单详情
    const response: any = await getOrderInfoById(id)
    uni.hideLoading()
    if (response.code === 700) {
      orderDetail.value = response.result
      calculateDistance()
      // 初始化地图数据
      initMapData()
      console.log(orderDetail.value)
    } else {
      console.error('获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
  }
}
const getOrderStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '已接单',
    2: '改价待确认',
    3: '改价已确认',
    4: '改价已拒绝',
    5: '到达装货地',
    6: '装货中',
    7: '运输中',
    8: '到达卸货地',
    9: '已完成待确认',
    10: '已完成',
    99: '已完成',
    98: '已取消',
  }
  return statusMap[status] || '未知状态'
}
// 计算距离的函数
const estimatedDistance = ref(null)
const calculateDistance = () => {
  const startLng = orderDetail.value.loadingLongitude
  const startLat = orderDetail.value.loadingLatitude
  const endLng = orderDetail.value.unloadingLongitude
  const endLat = orderDetail.value.unloadingLatitude
  const R = 6371 // 地球半径，单位：公里
  const dLat = (endLat - startLat) * (Math.PI / 180)
  const dLng = (endLng - startLng) * (Math.PI / 180)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(startLat * (Math.PI / 180)) *
    Math.cos(endLat * (Math.PI / 180)) *
    Math.sin(dLng / 2) *
    Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c
  estimatedDistance.value = distance.toFixed(1)
}

// 初始化地图数据
const initMapData = () => {
  if (!orderDetail.value) return
  return //先不调用地图了，不够用
  const startLat = orderDetail.value.loadingLatitude
  const startLng = orderDetail.value.loadingLongitude
  const endLat = orderDetail.value.unloadingLatitude
  const endLng = orderDetail.value.unloadingLongitude

  // 检查是否有经纬度数据
  if (!startLat || !startLng || !endLat || !endLng) {
    console.log('缺少经纬度数据，无法初始化地图')
    return
  }

  // 计算地图中心点（起点和终点的中点）
  mapCenter.value = {
    latitude: (parseFloat(startLat) + parseFloat(endLat)) / 2,
    longitude: (parseFloat(startLng) + parseFloat(endLng)) / 2,
  }

  // 创建标记点
  mapMarkers.value = [
    {
      id: 1,
      latitude: parseFloat(startLat),
      longitude: parseFloat(startLng),
      iconPath: '/static/images/start_dot.png',
      width: 32,
      height: 32,
      callout: {
        content: orderDetail.value.loadingAddressName || '装货地',
        color: '#000000',
        fontSize: 12,
        borderRadius: 4,
        bgColor: '#ffffff',
        padding: 8,
        display: 'ALWAYS',
      },
    },
    {
      id: 2,
      latitude: parseFloat(endLat),
      longitude: parseFloat(endLng),
      iconPath: '/static/images/end_dot.png',
      width: 32,
      height: 32,
      callout: {
        content: orderDetail.value.unloadingAddressName || '卸货地',
        color: '#000000',
        fontSize: 12,
        borderRadius: 4,
        bgColor: '#ffffff',
        padding: 8,
        display: 'ALWAYS',
      },
    },
  ]

  // 创建简单的直线路线
  mapPolyline.value = [
    {
      points: [
        {
          latitude: parseFloat(startLat),
          longitude: parseFloat(startLng),
        },
        {
          latitude: parseFloat(endLat),
          longitude: parseFloat(endLng),
        },
      ],
      color: '#0066FF',
      width: 6,
      dottedLine: false,
      arrowLine: true,
      borderColor: '#0044CC',
      borderWidth: 1,
    },
  ]

  // 根据距离调整地图缩放级别
  const distance = parseFloat(estimatedDistance.value || 0)
  if (distance > 1000) {
    mapScale.value = 6
  } else if (distance > 500) {
    mapScale.value = 8
  } else if (distance > 100) {
    mapScale.value = 10
  } else {
    mapScale.value = 12
  }

  console.log('地图数据初始化完成', {
    center: mapCenter.value,
    markers: mapMarkers.value,
    polyline: mapPolyline.value,
    scale: mapScale.value,
  })
}

// 查看轨迹，直接跳转高德地图
const viewTrajectory = () => {
  console.log('查看轨迹', orderDetail.value)

  if (!orderDetail.value) {
    uni.showToast({
      title: '订单信息加载中，请稍后重试',
      icon: 'none',
    })
    return
  }

  // 获取起点和终点信息
  const startLat = orderDetail.value.loadingLatitude
  const startLon = orderDetail.value.loadingLongitude
  const startName = orderDetail.value.loadingAddressName || '装货地'
  const endLat = orderDetail.value.unloadingLatitude
  const endLon = orderDetail.value.unloadingLongitude
  const endName = orderDetail.value.unloadingAddressName || '卸货地'

  // 检查是否有经纬度数据
  if (!startLat || !startLon || !endLat || !endLon) {
    uni.showToast({
      title: '暂无路线定位信息',
      icon: 'none',
    })
    return
  }

  // 构建高德地图URL Scheme
  const amapUrl = `amapuri://route/plan/?slat=${startLat}&slon=${startLon}&sname=${encodeURIComponent(
    startName
  )}&dlat=${endLat}&dlon=${endLon}&dname=${encodeURIComponent(
    endName
  )}&dev=0&t=0`

  console.log('高德地图URL:', amapUrl)

  // 直接跳转到高德地图
  // #ifdef APP-PLUS
  plus.runtime.openURL(amapUrl, (error) => {
    console.error('打开高德地图失败:', error)
    // 如果打开失败，尝试打开应用商店下载高德地图
    uni.showModal({
      title: '提示',
      content: '未检测到高德地图APP，是否前往下载？',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 跳转到应用商店
          const downloadUrl =
            uni.getSystemInfoSync().platform === 'ios'
              ? 'https://apps.apple.com/cn/app/id461703208'
              : 'https://www.amap.com/'
          plus.runtime.openURL(downloadUrl)
        }
      },
    })
  })
  // #endif

  // #ifdef H5
  window.location.href = amapUrl
  // #endif

  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
  // 小程序环境下复制链接到剪贴板
  uni.setClipboardData({
    data: amapUrl,
    success: () => {
      uni.showToast({
        title: '导航链接已复制，请在高德地图中打开',
        icon: 'none',
        duration: 3000,
      })
    },
  })
  // #endif
}
// 底部按钮校验  1:改价  2:撤销改价  3:取消订单  4:确认收货  5:支付运费  6:更多按钮  7:申请退订金  8:同意扣除 9:拒绝扣除 10:申请仲裁 11:撤回退订金
const checkUpdateOrderStatus = (num: number) => {
  if (num == 1) {
    // 到达装货地之前都可以改价 司机确认改价货主拒绝改价前不允许再次改价
    return (
      orderDetail.value.orderStatus <= 4 && orderDetail.value.orderStatus != 2
    )
  } else if (num == 2) {
    // 改价待确认时允许撤销改价
    return orderDetail.value.orderStatus == 2
  } else if (num == 3) {
    // 到达装货地之前都可以取消订单
    return orderDetail.value.orderStatus <= 4
  } else if (num == 4) {
    // 已完成待确认时允许确认收货
    return orderDetail.value.orderStatus == 9
  } else if (num == 5) {
    // 已完成已确认时允许支付运费
    return orderDetail.value.orderStatus == 10
  } else if (num == 6) {
    // 更多按钮显示状态
    return (
      orderDetail.value.depositStatus == 1 ||
      orderDetail.value.depositStatus == 2 ||
      orderDetail.value.depositStatus == 4 ||
      orderDetail.value.depositStatus == 6 ||
      orderDetail.value.depositStatus == 8
    )
  } else if (num == 7) {
    // 申请退订金状态
    return (
      orderDetail.value.depositStatus == 1 ||
      orderDetail.value.depositStatus == 4 ||
      orderDetail.value.depositStatus == 8
    )
  } else if (num == 8) {
    // 同意扣除按钮
    return orderDetail.value.depositStatus == 2
  } else if (num == 9) {
    // 拒绝扣除按钮
    return orderDetail.value.depositStatus == 2
  } else if (num == 10) {
    // 申请仲裁按钮
    return orderDetail.value.depositStatus == 2
  } else if (num == 11) {
    // 撤回退订金
    return orderDetail.value.depositStatus == 6
  }
}

onShow(() => {
  // 如果有订单ID，则获取订单详情
  if (currentOrderId.value) {
    console.log('页面显示，获取订单详情:', currentOrderId.value)
    fetchOrderDetail(currentOrderId.value)
  }
})
</script>

<style lang="scss" scoped>
.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeaderBg);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

:deep(.cu-header-box) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  background-size: 100% v-bind(heightHeader);
  z-index: 3;

  .header-container {
    background-color: transparent;
  }

  .cu-header {
    background-color: transparent;

    .left-btn>.iconfont {
      font-weight: 600;
    }
  }
}

.main-container {
  position: relative;
  z-index: 1;
  padding: 20rpx 32rpx 0rpx 32rpx;
  box-sizing: border-box;
}

.order-status {
  background-color: #fff;
  padding: 0rpx 32rpx 0rpx 32rpx;
  box-sizing: border-box;
  border-radius: 20rpx;

  .status-text {
    font-size: $uni-font-size-big;
    color: $uni-text-color;
    font-weight: 700;
    border-bottom: 2rpx solid $uni-text-color-grey;
    padding: 30rpx 0rpx 32rpx 0rpx;
  }

  .status-handle {
    display: flex;
    justify-content: space-around;
    padding: 30rpx 0rpx 28rpx 0rpx;

    .handle-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 44rpx;
      }

      text {
        font-size: $uni-font-size-base;
      }
    }
  }
}

.amount-status {
  background-color: #fff;
  padding: 32rpx 32rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  margin-top: 20rpx;

  .status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 36rpx;

    &:first-child {
      margin-top: 0rpx;
    }

    .status-item-left {
      display: flex;
      align-items: center;
      width: calc(100vw - 188rpx);

      .label {
        padding: 4rpx 10rpx;
        box-sizing: border-box;
        font-size: $uni-font-size-base;
        color: $uni-text-color;
        line-height: $uni-font-size-base;
        border: 2rpx solid $uni-text-color;
        border-radius: 6rpx;
      }

      .value {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 20rpx;

        text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: $uni-font-size-base-30;
          color: $uni-text-color;
        }

        .light {
          color: #ee9209;
        }
      }
    }

    .iconfont {
      width: 60rpx;
      font-size: 32rpx;
      color: $uni-text-color-grey;
      text-align: right;
    }
  }
}

.card-box {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-sizing: border-box;
  margin-top: 20rpx;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info-left {
      display: flex;
      align-items: center;

      .big {
        font-size: $uni-font-size-big;
        color: $uni-text-color;
        font-weight: 700;
      }

      .small {
        font-size: $uni-font-size-base-30;
        color: $uni-text-color-grey;
        margin-left: 20rpx;
      }
    }

    .info-right {
      display: flex;
      align-items: center;

      text {
        font-size: $uni-font-size-base-30;
        line-height: $uni-font-size-base-30;
        color: #5c9bd1;
      }

      .iconfont {
        font-size: 28rpx;
        color: #5c9bd1;
        margin-top: 2rpx;
        margin-left: 4rpx;
      }
    }
  }
}

.trajectory {
  .map-view {
    width: 100%;
    height: 260rpx;
    margin-top: 16rpx;
    border-radius: 20rpx;
    overflow: hidden;

    .trajectory-map {
      width: 100%;
      height: 100%;
      border-radius: 20rpx;
    }
  }
}

.order-info {
  .time {
    padding: 0rpx 32rpx;
    box-sizing: border-box;
    font-size: $uni-font-size-sm;
    color: $uni-text-color-grey;
    margin-top: 18rpx;
    margin-bottom: 18rpx;
  }

  .loading-info-line {
    position: relative;

    &:before {
      position: absolute;
      left: 52rpx;
      top: 40rpx;
      content: '';
      width: 2rpx;
      height: 100%;
      border-left: 2rpx dashed $uni-text-color-grey;
    }
  }

  .loading-info {
    padding: 0rpx 32rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .loading-info-left {
      display: flex;
      align-items: flex-start;
      flex: 1;

      .icon-text {
        width: 40rpx;
        height: 40rpx;
        text-align: center;
        line-height: 40rpx;
        color: #fff;
        background-color: blue;
        font-size: $uni-font-size-base;
        border-radius: 10rpx;
        margin-right: 14rpx;
      }

      .loading-info-left-address {
        flex: 1;

        .big-name {
          font-size: $uni-font-size-base-30;
          color: $uni-text-color;
        }

        .small-name {
          font-size: $uni-font-size-base;
          color: $uni-text-color-grey;
          margin-top: 8rpx;
        }
      }
    }
  }

  .unloading-info {
    margin-top: 40rpx;

    .loading-info-left {
      .icon-text {
        background-color: #ee7942;
      }
    }
  }

  .order-cargo {
    width: 100%;
    padding: 20rpx;
    box-sizing: border-box;
    background-color: #f9f9f9;
    border-radius: 10rpx;
    font-size: $uni-font-size-base;
    line-height: $uni-font-size-base;
    color: #707271;
    margin-top: 20rpx;
  }
}

.price-info {
  .card-header {
    margin-bottom: 10rpx;
  }

  .detail-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14rpx 0rpx;

    .label {
      width: 200rpx;
      font-size: $uni-font-size-base;
      line-height: $uni-font-size-base;
      color: $uni-text-color;
    }

    .value {
      flex: 1;
      font-size: $uni-font-size-base-30;
      line-height: $uni-font-size-base-30;
      color: $uni-text-color;
      font-weight: 600;
      text-align: right;
    }
  }
}

.driver-car {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .driver-left {
    display: flex;
    align-items: center;

    .head {
      width: 80rpx;
      height: 80rpx;
      border-radius: 20rpx;
      margin-right: 18rpx;
    }

    .driver-left-right {
      display: flex;
      flex-direction: column;

      .name {
        font-size: $uni-font-size-base-30;
      }

      .phone {
        font-size: $uni-font-size-base;
        color: $uni-text-color-grey;
      }
    }
  }

  .driver-right {
    .icon-box {
      width: 70rpx;
      height: 70rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      border: 2rpx solid #333;

      .iconfont {
        font-size: 40rpx;
        font-weight: 500;
      }
    }
  }
}

.type-class {
  display: flex;
  justify-content: space-between;

  .type-left {
    font-size: $uni-font-size-big;
    color: $uni-text-color;
    font-weight: 700;
  }

  .type-right {
    display: flex;
    align-items: center;

    text {
      font-size: $uni-font-size-base;
      color: $uni-text-color-grey;
    }

    .iconfont {
      font-size: 28rpx;
      color: $uni-text-color-grey;
      margin-top: 2rpx;
      margin-left: 4rpx;
    }
  }
}

// 底部操作按钮样式
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 9;

  // 更多操作展开区域
  .more-actions-panel {
    border-bottom: 1rpx solid #f0f0f0;
    background-color: #fafafa;

    .more-actions-buttons {
      display: flex;
      flex-wrap: wrap;
      padding: 20rpx 32rpx;
      gap: 12rpx 16rpx; // 垂直间距12rpx，水平间距16rpx
      justify-content: center; // 居中对齐

      .more-action-btn {
        width: 200rpx; // 固定宽度，确保按钮大小一致
        height: 72rpx;
        background-color: #fff;
        border: 1rpx solid #e0e0e0;
        border-radius: 36rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0; // 防止按钮被压缩

        .more-btn-text {
          font-size: 26rpx;
          color: #333;
          white-space: nowrap; // 防止文字换行
        }

        &:active {
          background-color: #f5f5f5;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    padding: 20rpx 32rpx;
    gap: 16rpx;

    .action-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      border: none;
      font-size: 28rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;

        .iconfont {
          font-size: 32rpx;
        }

        .btn-text {
          font-size: 28rpx;
          line-height: 1;
        }
      }

      &.primary {
        background-color: #e84138;
        color: #fff;

        &:active {
          background-color: #d73027;
          transform: scale(0.98);
        }
      }

      &.secondary {
        background-color: #f8f9fa;
        color: #666;
        border: 1rpx solid #e0e0e0;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
