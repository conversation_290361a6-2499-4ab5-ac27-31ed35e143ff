<template>
	<view class="cancel-detail">
		<view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn>
				<text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text>
			</template>
			<template #title>{{ '' }}</template>
			<template #rightBtn>{{ '' }}</template>
		</cu-header>
		<view class="main-container" :style="{ paddingBottom: safeAreaInsetsBottom + 20 + 'rpx' }">
			<view class="cancel-reason-box">
				<view class="cancel-title">
					<text>已取消</text>
					<view class="iconfont icon-owner-youjiant<PERSON>"></view>
				</view>
				<view class="cancel-reason">订单取消原因为货主原因：司机时间有变，协商取消</view>
			</view>
			<view class="route-info">
				<view class="route-title">货物路线信息</view>
				<view class="loading-info loading-info-line">
					<view class="loading-info-left">
						<view class="icon-text">装</view>
						<view class="loading-info-left-address">
							<view class="big-name">展览路街道展览路街道展览路街道展览路街道展览路街道展览路街道</view>
							<view class="small-name">北京市 西城区</view>
							<view class="time">05-23 00:00-24:00</view>
						</view>
					</view>
				</view>
				<view class="loading-info unloading-info">
					<view class="loading-info-left">
						<view class="icon-text">卸</view>
						<view class="loading-info-left-address">
							<view class="big-name">展览路街道展览路街道展览路街道展览路街道展览路街道展览路街道</view>
							<view class="small-name">北京市 西城区</view>
							<view class="time">05-23 00:00-24:00</view>
						</view>
					</view>
				</view>
				<view class="goods-detail">
					<view class="goods-detail-left">
						<view class="goods-detail-item">其他化工塑料</view>
						<view class="goods-detail-item">32.4吨</view>
						<view class="goods-detail-item">33方</view>
						<view class="goods-detail-item">吨包</view>
					</view>
					<view class="goods-detail-right">
						<text>货物详情</text>
						<view class="iconfont icon-owner-youjiantou"></view>
					</view>
				</view>
			</view>
			<view class="driver-info">
				<view class="driver-left">
					<view class="driver-head">
						<image src="@/static/images/head.png" mode="center"></image>
					</view>
					<view class="driver-detail">
						<view class="name">杜师傅</view>
						<view class="phone">135****5143 | 冀B*****B</view>
					</view>
				</view>
				<view class="driver-phone">
					<view class="iconfont icon-owner-dianhua1"></view>
				</view>
			</view>
			<view class="order-pic">
				<view class="order-pic-left">
					<view class="big-label">订单图片</view>
					<view class="small-label">装卸货图片、回单照片</view>
				</view>
				<view class="order-pic-right" @tap="showAll">
					<text>查看全部</text>
					<view class="iconfont icon-owner-youjiantou"></view>
				</view>
			</view>
			<view class="tips">
				为保障订单顺利成交及跟车人员（如有）人身安全，请您在实际装货前务必核实并承诺实际货物信息、司机、车辆与订单展示一致。如不一致，您应当拒绝交易，否则后果自负！
			</view>
			<view class="other-info">
				<view class="orther-title">其他信息</view>
				<view class="cell-item">
					<view class="cell-label">订单编号</view>
					<view class="cell-value">
						<view class="cell-value-text">1234343343234534</view>
						<view class="line"></view>
						<view class="cell-value-handle">复制</view>
					</view>
				</view>
				<view class="cell-item">
					<view class="cell-label">订单编号</view>
					<view class="cell-value">
						<view class="cell-value-text">1234343343234534</view>
						<view class="line"></view>
						<view class="cell-value-handle">复制</view>
					</view>
				</view>
				<view class="cell-item">
					<view class="cell-label">订单编号</view>
					<view class="cell-value">
						<view class="cell-value-text">1234343343234534</view>
						<view class="line"></view>
						<view class="cell-value-handle">复制</view>
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="orderRecordRef" background-color="#fff" class="global-popup order-record-popup" borderRadius="20rpx 20rpx 0rpx 0rpx" :is-mask-click="false">
			<view class="popup-content">
				<view class="title-box">
					<text class="btn cancel"></text>
					<text class="title">订单记录</text>
					<text class="btn confirm" @tap="closeRecord">确定</text>
				</view>
				<view class="record">
					<view class="record-item">
						<view class="status-name status-active">订单取消</view>
						<view class="status-time">2020-05-23 21:04:33</view>
					</view>
					<view class="record-item">
						<view class="status-name">订单生成</view>
						<view class="status-time">2020-05-23 21:04:33</view>
					</view>
					<view class="record-item">
						<view class="status-name">赵林发起协议</view>
						<view class="status-time">2020-05-23 21:04:33</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<cu-secure></cu-secure>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const { safeAreaInsets } = uni.getSystemInfoSync();
const safeAreaInsetsBottom = safeAreaInsets.bottom * 2;
const heightHeader = safeAreaInsets.top * 2 + 80 + 'rpx';

const orderRecordRef = ref();
// 查看全部
const showAll = () => {
	orderRecordRef.value.open('bottom');
};
const closeRecord = () => {
	orderRecordRef.value.close();
};
</script>

<style scoped lang="scss">
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: #373b4c;
	background-size: 100% 400rpx;
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: #373b4c;
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
		.left-btn > .iconfont {
			color: #fff;
			font-weight: 600;
		}
	}
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding: 0rpx 0rpx 0rpx 0rpx;
	box-sizing: border-box;
	padding-top: v-bind(heightHeader);
}
.cancel-reason-box {
	background-color: #373b4c;
	padding-top: 40rpx;
	padding-bottom: 40rpx;
	.cancel-title {
		display: flex;
		align-items: center;
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		text {
			font-size: 50rpx;
			color: #fff;
		}
		.iconfont {
			font-size: 30rpx;
			color: #fff;
			margin-left: 5rpx;
		}
	}
	.cancel-reason {
		font-size: $uni-font-size-base;
		color: #ed9642;
		margin-top: 20rpx;
		padding: 0rpx 32rpx;
		box-sizing: border-box;
	}
}
.route-info {
	background-color: #fff;
	.route-title {
		padding: 20rpx 32rpx 20rpx 32rpx;
		font-size: $uni-font-size-big;
		color: $uni-text-color;
		font-weight: 700;
	}
	.loading-info-line {
		position: relative;
		&:before {
			position: absolute;
			left: 52rpx;
			top: 40rpx;
			content: '';
			width: 2rpx;
			height: 100%;
			border-left: 2rpx dashed $uni-text-color-grey;
		}
	}
	.loading-info {
		padding: 0rpx 32rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		.loading-info-left {
			display: flex;
			align-items: flex-start;
			flex: 1;
			.icon-text {
				width: 40rpx;
				height: 40rpx;
				text-align: center;
				line-height: 40rpx;
				color: #fff;
				background-color: blue;
				font-size: $uni-font-size-base;
				border-radius: 10rpx;
				margin-right: 14rpx;
			}
			.loading-info-left-address {
				flex: 1;
				.big-name {
					font-size: $uni-font-size-base-30;
					color: $uni-text-color;
				}
				.small-name {
					font-size: $uni-font-size-base;
					color: $uni-text-color-grey;
					margin-top: 8rpx;
				}
				.time {
					font-size: $uni-font-size-base;
					color: $uni-text-color-grey;
					margin-top: 8rpx;
				}
			}
		}
	}
	.unloading-info {
		margin-top: 40rpx;
		.loading-info-left {
			.icon-text {
				background-color: #ee7942;
			}
		}
	}
	.goods-detail {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 32rpx;
		box-sizing: border-box;
		.goods-detail-left {
			display: flex;
			.goods-detail-item {
				padding: 4rpx 6rpx;
				background-color: $uni-bg-color-grey;
				font-size: $uni-font-size-base;
				line-height: $uni-font-size-base;
				color: $uni-text-color-grey;
				margin-left: 8rpx;
				border-radius: 4rpx;
				&:first-child {
					margin-left: 0rpx;
				}
			}
		}
		.goods-detail-right {
			display: flex;
			align-items: center;
			text {
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
			}
			.iconfont {
				color: $uni-text-color-grey;
				font-size: 26rpx;
				margin-left: 10rpx;
			}
		}
	}
}
.driver-info {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
	background-color: #fff;
	padding: 16rpx 32rpx;
	box-sizing: border-box;
	.driver-left {
		display: flex;
		align-items: center;
		.driver-head {
			width: 70rpx;
			height: 70rpx;
			border-radius: 4rpx;
			margin-right: 10rpx;
			image {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}
		}
		.driver-detail {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			color: $uni-text-color-grey;
			.name {
				font-size: $uni-font-size-sm;
			}
			.phone {
				font-size: $uni-font-size-sm;
			}
		}
	}
	.driver-phone {
		width: 50rpx;
		height: 50rpx;
		border: 2rpx solid orangered;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		.iconfont {
			color: orangered;
			font-size: 24rpx;
		}
	}
}
.order-pic {
	width: 100%;
	background-color: #fff;
	display: flex;
	justify-content: space-between;
	margin-top: 20rpx;
	padding: 20rpx 32rpx;
	box-sizing: border-box;
	.order-pic-left {
		display: flex;
		align-items: center;
		.big-label {
			font-size: $uni-font-size-lg;
			line-height: $uni-font-size-lg;
			color: $uni-text-color;
		}
		.small-label {
			font-size: $uni-font-size-base;
			line-height: $uni-font-size-base;
			color: $uni-text-color-grey;
			margin-left: 10rpx;
		}
	}
	.order-pic-right {
		display: flex;
		align-items: center;
		text {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}
		.iconfont {
			color: $uni-text-color-grey;
			font-size: 26rpx;
			margin-left: 10rpx;
		}
	}
}
.tips {
	width: 100%;
	padding: 32rpx;
	box-sizing: border-box;
	margin-top: 20rpx;
	background-color: #fdf3e8;
	font-size: $uni-font-size-sm;
	line-height: $uni-bg-color;
	color: $uni-text-color;
}
.other-info {
	width: 100%;
	background-color: #fff;
	padding: 0rpx 32rpx 20rpx 32rpx;
	box-sizing: border-box;
	margin-top: 20rpx;
	.orther-title {
		padding: 20rpx 0rpx 20rpx 0rpx;
		font-size: $uni-font-size-big;
		color: $uni-text-color;
		font-weight: 700;
	}
	.cell-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		&:last-child {
			margin-bottom: 0rpx;
		}
		.cell-label {
			font-size: $uni-font-size-base;
			color: $uni-text-color;
		}
		.cell-value {
			display: flex;
			align-items: center;
			.cell-value-text {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}
			.line {
				width: 1px;
				height: 18rpx;
				background-color: $uni-text-color-grey;
				margin: 0rpx 20rpx;
			}
			.cell-value-handle {
				font-size: $uni-font-size-base;
				color: $uni-text-color-light;
			}
		}
	}
}
:deep(.order-record-popup) {
	.uni-popup__wrapper {
		.popup-content {
			.record {
				padding: 50rpx 40rpx 120rpx 80rpx;
				box-sizing: border-box;
				.record-item {
					padding-bottom: 80rpx;
					position: relative;
					&:before {
						content: '';
						width: 20rpx;
						height: 20rpx;
						background-color: #eb7930;
						border-radius: 50%;
						position: absolute;
						left: -35.5rpx;
						top: 2rpx;
					}
					&::after {
						content: '';
						width: 2rpx;
						height: calc(100% - 44rpx);
						background-color: #eb7930;
						position: absolute;
						left: -27rpx;
						top: 38rpx;
					}
					&:first-child {
						&:before {
							border: 4rpx solid #f9deca;
							left: -39rpx;
						}
					}
					&:last-child {
						&::after {
							display: none;
						}
					}
					.status-name {
						font-size: $uni-font-size-base;
						line-height: $uni-font-size-base;
						color: $uni-text-color;
						font-weight: bolder;
					}
					.status-time {
						font-size: $uni-font-size-base;
						line-height: $uni-font-size-base;
						color: $uni-text-color;
						margin-top: 20rpx;
					}
					.status-active {
						color: $uni-text-color-light;
					}
				}
			}
		}
	}
}
</style>
