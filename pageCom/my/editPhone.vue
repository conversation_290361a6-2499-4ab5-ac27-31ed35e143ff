<template>
  <view class="personal-page">
		<view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text></template>
			<template #title>修改手机号</template>
			<template #rightBtn>
				{{ '' }}
			</template>
		</cu-header>
		<view class="main-container">
      <view class="tip-box">
        <text class="iconfont icon-owner-wendang"></text>
        <view class="tip-text">本月还剩<span style="color:red;">{{phoneNum}}次</span>修改机会</view>
      </view>
      <view class="change-box">
        <view class="change-item" v-if="isNewPhone">
          <span class="title">
            <span class="rule-required">*</span>
            <span>新手机号</span>
          </span>
          <input class="item-input" name="input" v-model="phone" placeholder="必填 请输入" />
        </view>
        <view class="change-item change-item-phone" v-else>
          <view class="phone-text">{{ thisPhone }}</view>
          <view class="phone-use">该手机号已不再使用？</view>
        </view>
        <view class="change-item">
          <span class="title">
            <span class="rule-required">*</span>
            <span>验证码</span>
          </span>
          <input class="item-input" name="input" v-model="passCode" placeholder="必填 请输入" />
          <span class="input-code" v-if="isCode" @click="getCode">获取验证码</span>
          <span class="input-code" v-else>{{countNumber}}s后重试</span>
        </view>
      </view>
    </view>
    <view class="submit-box">
      <view class="submit-btn" v-if="isNewPhone" @click="submitBtn">提交</view>
      <view class="submit-btn" v-else @click="submitBtn1">验证并继续</view>
    </view>
    
  </view>
</template>
<script setup lang="ts">
import { ref,onMounted } from 'vue'
import {getUserChangePhoneNum,getUserPhone} from "../../api/userCenter";
import {sendVerifyCodeSms,verifyCodeSms,changePhone} from "../../api/common"

const isCode = ref(true)
let countNumber = ref(60)
const isNewPhone = ref(false)
const thisPhone = ref('');
const phoneNotEncrypt = ref('')
const phoneNum = ref(3);
const phone = ref(0)
const passCode = ref(0)
let timer :any = null;
// 验证手机号格式
const validatePhone = (phone: string): boolean => {
  if (!phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
      duration: 2000
    });
    return false;
  }
  const phoneReg = /^1[3-9]\d{9}$/;
  if (!phoneReg.test(phone)) {
    uni.showToast({
      title: '手机号格式不正确',
      icon: 'none',
      duration: 2000
    });
    return false;
  }
  return true;
};
// 获取验证码
const getCode = async () => {
  if (!validatePhone(phone.value)) return;
  isCode.value = false
  try {
     const result: any = await sendVerifyCodeSms({
       mobile: phone.value,
       type: 1
     });
    uni.hideLoading();
    if (result.code == 700) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
        duration: 2000
      });

      // 开始倒计时
      countNumber.value = 60;
      timer = setInterval(function() {
        countNumber.value--;
        if (countNumber.value < 0) {
          clearInterval(timer);
          isCode.value = true
        }
      }, 1000);
    } else {
      uni.showToast({
        title: result.msg || '发送验证码失败',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none',
      duration: 2000
    });
     isCode.value = true
  } finally {
   
  }
};
// 点击提交
const submitBtn = async () => {
  if (!validatePhone(phone.value)) return;
  try {
    const response:any = await changePhone({
       mobile: phone.value,
       type: 1,
       verifyCode: passCode
    });
    if (response.code === 700) {
      uni.showToast({
        title: '修改手机号成功',
        icon: 'success',
        duration: 2000
      });
      clearInterval(timer);

    } else {
      console.error('初始化用户数据失败:', response.code);
    }
  } catch (error) {
    console.error('初始化用户数据失败:', error);
  }
}
const submitBtn1 = async () => {
  if (!validatePhone(phone.value)) return;
  try {
    const response:any = await verifyCodeSms({
       mobile: phone.value,
       type: 1,
       verifyCode: passCode
    });
    if (response.code === 700) {
        isNewPhone.value = true
        isCode.value = true
        passCode.value = null
        phone.value = null
        clearInterval(timer);
    } else {
      console.error('初始化用户数据失败:', response.code);
    }
  } catch (error) {
    console.error('初始化用户数据失败:', error);
  }
}

const initData = async () => {
  try {
    const response:any = await getUserChangePhoneNum();
    if (response.code === 700) {
        thisPhone.value = response.result.phone;
        phoneNum.vaule = response.result.phoneNum;
        phone.value = response.result.phoneNotEncrypt;
    } else {
      console.error('初始化用户数据失败:', response.code);
    }
  } catch (error) {
    console.error('初始化用户数据失败:', error);
  }
};
onMounted(() => {
	initData();
});
</script>
<style lang="scss" scoped>
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding: 20rpx 0rpx 0rpx 0rpx;
	box-sizing: border-box;
	padding-top: v-bind(heightHeader);
}
.tip-box {
  width: calc(95% - 40rpx);
  margin-left: 2%;
  background-color: #f1cac4;
  height: 40rpx;
  line-height: 40rpx;
  border-radius:5rpx;
  padding: 20rpx;
  .iconfont {
    color: red;
    margin-right: 8rpx;
  }
  .tip-text {
    display: inline-block;
    .tip-color{
      color: red;
    }
  }
}
.change-box{
  width: calc(95% - 40rpx);
  margin-left: 2%;
  background-color: #fff;
  border-radius:5rpx;
  margin-top: -4rpx;
  padding: 50rpx 20rpx 20rpx 20rpx;
  .change-item {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    margin-bottom: 20rpx;
    .title {
      width: 25%;
      display: inline-block;
      .rule-required {
        color: red;
      }
    }
    .item-input {
      width: calc(75% - 6rpx);
      height: 80rpx;
      line-height: 80rpx;
      display: inline-block;
      vertical-align: middle;
      background-color: #f1eded;
      opacity: 0.8;
      border-radius:4rpx;
      padding-left: 6rpx;
    }
    .input-code {
      display: inline-block;
      position: absolute;
      right: 50rpx;
      color: red;
    }
    .phone-text {
      font-size: 36rpx;
      text-align: center;
    }
    .phone-use {
      color: red;
      font-size: 26rpx;
      text-align: center;
      height: 20rpx;
      line-height: 20rpx;
    }
  }
  .change-item-phone {
    margin-bottom: 60rpx;
  }
}
.submit-box {
  width: 100%;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  height: 100rpx;
  .submit-btn {
    width: 90%;
    margin-left: 5%;
    text-align: center;
    height: 80rpx;
    line-height: 80rpx;
    background-color: red;
    color: #fff;
    border-radius:8rpx;
    margin: 20rpx 5%;
  }
}

</style>