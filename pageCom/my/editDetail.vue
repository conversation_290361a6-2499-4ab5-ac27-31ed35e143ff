<template>
  <view class="edit-detail">
    <view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON>ji<PERSON><PERSON>"></text></template>
			<template #title>{{ '' }}</template>
			<template #rightBtn>
				{{ '' }}
			</template>
		</cu-header>
    <view class="main-container">
			<view class="base-info">
				<view class="head">
					<image :src="headImg"></image>
				</view>
				<view class="phone-box">
					<view class="owner-name">
						<text>师傅</text>
					</view>
					<view class="owner-phone">
						<text>18231319486</text>
            <view class="iconfont icon-owner-bianji" @click="toEditPhone"></view>
					</view>
				</view>
			</view>
      <view class="card-info-box">
				<view class="card-info">
          资质认证相关板块
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
const headImg = '../../static/images/head.png';
const toEditPhone = () => {
  uni.reLaunch({
		url: './editPhone'
	})
}
</script>
<style lang="scss" scoped>
.header-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	z-index: 1;
}
:deep(.cu-header-box) {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: v-bind(heightHeader);
	background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	background-size: 100% 400rpx;
	z-index: 3;
	.header-container {
		background-color: transparent;
	}
	.cu-header {
		background-color: transparent;
	}
}
.main-container {
	width: 100%;
	position: relative;
	z-index: 1;
	padding: 20rpx 0rpx 0rpx 0rpx;
	box-sizing: border-box;
	padding-top: v-bind(heightHeader);
}
.base-info {
	width: 100%;
	background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	padding-top: 20rpx;
	padding-bottom: 20rpx;
	.head {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		margin: 0 20rpx;
    border: 2px solid #fff;
		image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
		}
	}
	.phone-box {
		display: flex;
		flex-direction: column;
		justify-content: center;
		margin-top: 30rpx;
		.owner-name {
			font-size: 14rpx;
		}
    .owner-phone {
      font-size: 12rpx;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
    }
		text {
			font-size: $uni-font-size-lg;
			color: #333;
			margin-right: 8rpx;
			font-weight: 700;
		}
		.iconfont {
			font-size: $uni-font-size-base;
			color: #333;
      margin-top: 5rpx;
		}
	}
}
.card-info-box {
	padding: 0rpx 30rpx;
	box-sizing: border-box;
	.card-info {
		width: 100%;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
		background-color: #fff;
		border-radius: 16rpx;
		margin-top: 20rpx;
		
	}
}
</style>