<template>
  <view class="message-page">
		<view class="header-bg"></view>
		<cu-header class="cu-header-box">
			<template #leftBtn><text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></text></template>
			<template #title>消息设置</template>
			<template #rightBtn>
				{{ '' }}
			</template>
		</cu-header>
    <view class="main-container">
      <view class="message-box">
        <view class="message-text">聊天消息免打扰</view>
        <switch @change="changeSwitch" />
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
const changeSwitch = (e) => {
  console.log(e.detail.value);//调接口用
  
}
</script>
<style lang="scss" scoped>
.main-container {
  margin-top: 20rpx;
  .message-box {
    width: 90%;
    padding:0 5%;
    height: 120rpx;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

</style>