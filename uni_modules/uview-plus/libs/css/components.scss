@import "./mixin.scss";

/* #ifndef APP-NVUE */
// 由于uview-plus是基于nvue环境进行开发的，此环境中普通元素默认为flex-direction: column;
// 所以在非nvue中，需要对元素进行重置为flex-direction: column; 否则可能会表现异常
// 2024-04-09由于微信小程序会提示 Some selectors are not allowed in component wxss所以注释以下几行
// view,
// scroll-view,
// swiper-item,
.u-empty,
.u-empty__wrap,
.u-tabs,
.u-tabs__wrapper,
.u-tabs__wrapper__scroll-view-wrapper,
.u-tabs__wrapper__scroll-view,
.u-tabs__wrapper__nav,
.u-tabs__wrapper__nav__line,
.up-empty,
.up-empty__wrap,
.up-tabs,
.up-tabs__wrapper,
.up-tabs__wrapper__scroll-view-wrapper,
.up-tabs__wrapper__scroll-view,
.up-tabs__wrapper__nav,
.up-tabs__wrapper__nav__line {
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
	flex-grow: 0;
	flex-basis: auto;
	align-items: stretch;
	align-content: flex-start;
}
/* #endif */
