import App from './App'
import {
	gConfig
} from '@/config.ts'
import { routeInterceptor} from './interceptors'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = gConfig.debug
App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	app.use(routeInterceptor)
	return {
		app
	}
}
// #endif